using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.Transfer;
using Amazon.Transfer.Model;
using FlexCharge.Common.Activities;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Sftp;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.SftpGateway.Activities;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.SftpGateway.Services.SftpService;

public class AwsSftpService : ISftpService
{
    private readonly IAmazonTransfer _transferClient;
    private readonly IAmazonS3 _s3Client;
    private readonly IActivityService _activityService;

    public AwsSftpService(IAmazonS3 s3Client, IAmazonTransfer transferClient,
        IActivityService activityService)
    {
        _s3Client = s3Client;
        _transferClient = transferClient;
        _activityService = activityService;
    }

    /// <summary>
    /// FlexCharge implementation of SFTP user creation
    /// </summary>
    /// <param name="username"></param>
    /// <param name="folderName"></param>
    /// <returns></returns>
    public async Task<dynamic> CreateSftpUserAsync(string username, string folderName)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Creating SFTP folder and user {UserName}", username);
        try
        {
            var env = EnvironmentHelper.GetCurrentEnvironment().ToString().ToLower();
            var rootFolder = $"sftp-gateway-{env}";
            var targetFolder =
                $"/{rootFolder}/{folderName}"; // Which folder will be connected to the SFTP user example /sftp-gateway-development/mid/
            var sftpServerId = "s-394f074ee7f2462eb";
            var sftpServerArn = $"arn:aws:iam::556663010871:role/sftp-gateway-{env}-s3-role";

            var user = await CreateSftpUserAsync(
                targetFolder,
                username,
                sftpServerId,
                sftpServerArn);

            await CreateFolderAsync(rootFolder, folderName);
            // create subfolders for inbound, outbound and reports
            await CreateFolderAsync(rootFolder, $"{folderName}/{SftpFolderNamesHelper.Inbound}");
            await CreateFolderAsync(rootFolder, $"{folderName}/{SftpFolderNamesHelper.Outbound}");
            await CreateFolderAsync(rootFolder, $"{folderName}/{SftpFolderNamesHelper.Reports}");

            return user;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Failed to create SFTP user");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.CreateSftpUser_Error,
                set => set
                    .Meta(meta => meta
                        .SetValue("FolderName", folderName)
                        .SetValue("UserName", username)
                    ));
            throw;
        }
    }

    /// <summary>
    /// FlexCharge implementation of SFTP user creation
    /// </summary>
    /// <param name="username"></param>
    /// <param name="folderName"></param>
    /// <returns></returns>
    public async Task<dynamic> CreateSftpUserAsync(string username, string folderName, string publicSshKey)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Creating SFTP folder and user {UserName}", username);
        try
        {
            var env = EnvironmentHelper.GetCurrentEnvironment().ToString().ToLower();
            var rootFolder = $"sftp-gateway-{env}";
            var targetFolder = $"/{rootFolder}/{folderName}";
            var sftpServerId = "s-394f074ee7f2462eb";
            var sftpServerArn = $"arn:aws:iam::556663010871:role/sftp-gateway-{env}-s3-role";

            var user = await CreateSftpUserAsync(
                targetFolder,
                username,
                sftpServerId,
                sftpServerArn);

            await UpdateSftpUserSsh(username, sftpServerId, publicSshKey);

            await CreateFolderAsync(rootFolder, folderName);
            await CreateFolderAsync(rootFolder, $"{folderName}/inbound");
            await CreateFolderAsync(rootFolder, $"{folderName}/outbound");
            await CreateFolderAsync(rootFolder, $"{folderName}/reports");

            return user;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Failed to create SFTP user");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.CreateSftpUser_Error,
                set => set
                    .Meta(meta => meta
                        .SetValue("FolderName", folderName)
                        .SetValue("UserName", username)
                    ));
            throw;
        }
    }

    public async Task<dynamic> GetSftpUserAsync(string username)
    {
        return await GetSftpUserAsync(username, "s-394f074ee7f2462eb");
    }

    //write a get sftp user method
    public async Task<dynamic> GetSftpUserAsync(string username, string serverId)
    {
        using var workspan = Workspan.Start<AwsSftpService>();
        workspan.Log.Information("Getting SFTP user {UserName}", username);

        dynamic user = new ExpandoObject();
        try
        {
            var sftpUser = await _transferClient.DescribeUserAsync(new DescribeUserRequest
            {
                ServerId = serverId,
                UserName = username
            });

            if (sftpUser.User != null)
            {
                user.Succeeded = true;
                user.Enabled = true;
                user.ResponseMessage = "User found";

                user.Password = sftpUser.User.SshPublicKeys.Any()
                    ? sftpUser.User.SshPublicKeys.First().SshPublicKeyBody
                    : "No SSH key found";

                user.Username = sftpUser.User.UserName;
                user.HomeDirectory = sftpUser.User.HomeDirectory;
                user.Url = "sftp.flex-charge.com";
                user.Port = 22;
            }
            else
            {
                user.Succeeded = false;
                user.Enabled = false;
                user.ResponseMessage = "User not found";
            }

            return user;
        }
        catch (Exception e)
        {
            workspan.Log.Warning(e, "Failed to get SFTP user");

            user.Succeeded = false;
            user.Enabled = false;
            user.ResponseMessage = "Unknown user";
            return user;
        }
    }

    /// <summary>
    /// More generic implementation of SFTP user creation
    /// </summary>
    /// <param name="targetFolder"></param>
    /// <param name="userName"></param>
    /// <param name="serverId"></param>
    /// <param name="arnRole"></param>
    /// <returns></returns>
    public async Task<dynamic> CreateSftpUserAsync(string targetFolder, string userName, string serverId,
        string arnRole)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information(
            "Creating SFTP folder and user {userName} folder: {rootFoldername} sftp server: {serverId} role: {arnRole}",
            userName, targetFolder, serverId, arnRole);
        try
        {
            // Create an instance of the AWS Transfer for SFTP client
            var homeDirectoryMappings = new List<HomeDirectoryMapEntry>
            {
                new()
                {
                    Entry = "/",
                    Target = targetFolder
                }
            };

            // Create the SFTP user
            var createUserRequest = new CreateUserRequest
            {
                ServerId = serverId,
                UserName = userName,
                HomeDirectoryType = "LOGICAL",
                HomeDirectoryMappings = homeDirectoryMappings,
                Role = arnRole,
                //SshPublicKeyBody = ""
            };

            // var sshKeyResponse = await _transferClient.ImportSshPublicKeyAsync(new ImportSshPublicKeyRequest
            // {
            //     ServerId = serverId,
            //     SshPublicKeyBody = null,
            //     UserName = userName
            // });

            var createUserResponse = await _transferClient.CreateUserAsync(createUserRequest);

            workspan.Log.Information("SFTP User created {UserResponse}", createUserResponse);
            await _activityService.CreateActivityAsync(SftpGatewayActivities.SftpCreated,
                set => set
                    .Data(createUserResponse));

            return JsonConvert.SerializeObject(createUserResponse);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Failed to create SFTP user");
            throw;
        }
    }

    public async Task<string> UpdateSftpUserSsh(string userName, string serverId, string publicSshKey)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Updating SFTP user {userName} ", userName);
        try
        {
            var response = await _transferClient.ImportSshPublicKeyAsync(new ImportSshPublicKeyRequest
            {
                ServerId = serverId,
                SshPublicKeyBody = publicSshKey,
                UserName = userName,
            });

            workspan.Log.Information("SFTP updated {UserResponse}", response);

            await _activityService.CreateActivityAsync(SftpGatewayActivities.SftpUpdated,
                set => set
                    .Data(response));

            return response.SshPublicKeyId ?? null;
            //return await GenerateSshKeyPair(userName);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Updating SFTP user");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.UpdateSftpUser_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));
            throw;
        }
    }

    public async Task DeleteSftpUserSsh(string userName, string serverId, string sshPublicKeyId)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Deleting SFTP user {userName} ", userName);
        try
        {
            var response = await _transferClient.DeleteSshPublicKeyAsync(new DeleteSshPublicKeyRequest
            {
                ServerId = serverId,
                UserName = userName,
                SshPublicKeyId = sshPublicKeyId
            });

            workspan.Log.Information("SFTP deleted {UserResponse}", response);

            await _activityService.CreateActivityAsync(SftpGatewayActivities.SftpDeleted,
                set => set
                    .Data(response));
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Deleting SFTP user");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.DeleteSftpUser_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));
            throw;
        }
    }

    public async Task ResetSftpSshAccess(string userName)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Reset Sftp SshAccess {userName}", userName);
        try
        {
            // var createUserResponse = await _transferClient.UpdateUserAsync(new UpdateUserRequest
            // {
            //     HomeDirectory = null,
            //     HomeDirectoryMappings = null,
            //     HomeDirectoryType = null,
            //     Policy = null,
            //     PosixProfile = null,
            //     Role = null,
            //     ServerId = null,
            //     UserName = null
            // });
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Reset Sftp SshAccess");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.ResetSftpUser_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("UserName", userName)
                    ));
            throw;
        }
    }

    public async Task CreateFolderAsync(string bucketName, string folderName)
    {
        using var workspan = Workspan.Start<AwsSftpService>();

        workspan.Log.Information("Create S3 Folder {bucketName} {folderName}", bucketName, folderName);
        try
        {
            var request = new PutObjectRequest
            {
                BucketName = bucketName,
                Key = $"{folderName}/",
                ContentBody = ""
            };

            await _s3Client.PutObjectAsync(request);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Create S3 Folder");
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.CreateS3Folder_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("FolderName", folderName)
                    ));
            throw;
        }
    }

    public async Task<(string, string)> GenerateSshKeyPair(string comment)
    {
        // using var workspan = Workspan.Start<AwsSftpService>();
        //
        // var key = SshNet.Security.Cryptography.SHA512.Create("test.key");
        //
        // var publicKey = key.ToPublic();
        // var fingerprint = key.Fingerprint();
        //
        return ("privateKeyString", "publicKeyString");
    }
}