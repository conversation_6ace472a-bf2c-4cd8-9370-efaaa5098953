using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.SftpGateway.DTO;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.SftpGateway.Services.S3WatcherBackgroundService;

public partial class S3WatcherBackgroundService
{
    private void EnsureMerchantHasAccessToBatchEvaluateOrTransmitRequests(Guid mid, string authorizationJwtToken,
        out Guid principalMerchantId)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        using var serviceScope = _scopeFactory.CreateScope();

        authorizationJwtToken = authorizationJwtToken.Replace("Bearer ", "");

        var jwtHandler = serviceScope.ServiceProvider.GetRequiredService<IJwtHandler>();
        var jwtTokenValidationResult = jwtHandler.ValidateToken(authorizationJwtToken);

        if (jwtTokenValidationResult == Guid.Empty)
        {
            workspan.Log.Error("JWT token is not valid");
            Exceptions.ThrowHelper.ThrowSecurityException_InvalidAuthorizationToken();
        }

        var principal = jwtHandler.GetPrincipal(authorizationJwtToken);
        var principalMerchantIdString = principal.Claims.FirstOrDefault(x => x.Type == MyClaimTypes.MERCHANT_ID)?.Value;

        if (!Guid.TryParse(principalMerchantIdString, out principalMerchantId))
        {
            workspan.Log.Error("MerchantId provided in JWT token is not valid: {Mid}", principalMerchantIdString);
            Exceptions.ThrowHelper.ThrowSecurityException_InvalidMerchantIdInToken();
        }

        #region Commented

        // if (principal.IsInRole(SuperAdminGroups.MERCHANT_ADMIN))
        // {
        //     if (principalMerchantId != mid)
        //     {
        //         workspan.Log.Error("MerchantId {Mid} passed in request must be equal to merchant id in JWT token", mid);
        //         Exceptions.ThrowHelper.ThrowSecurityException_MerchantHasNoAccess();
        //     }
        // }
        // else if (principal.IsInRole(SuperAdminGroups.SUPER_ADMIN))
        // {
        //     Exceptions.ThrowHelper.ThrowSecurityException_MerchantHasNoAccess();
        // }

        #endregion
    }

    private async Task SerializeJsonToStreamAsync<T>(T objectToSerialize, MemoryStream stream,
        CancellationToken cancellationToken)
    {
        var serializerOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy =
                JsonNamingPolicy.CamelCase, // On deserialization below, the property names are case-insensitive
            WriteIndented = true
        };

        await System.Text.Json.JsonSerializer.SerializeAsync(stream, objectToSerialize, serializerOptions,
            cancellationToken);
    }

    private static async Task<T> DeserializeJsonFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken)
    {
        var serializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        return await System.Text.Json.JsonSerializer.DeserializeAsync<T>(stream, serializerOptions, cancellationToken);
    }

    public async Task<T> DeserializeBatchFileAsync<T>(string storageName, string filePath,
        CancellationToken cancellationToken)
    {
        using var serviceScope = _scopeFactory.CreateScope();
        var cloudStorage = serviceScope.ServiceProvider.GetRequiredService<ICloudStorage>();

// #if DEBUG
//         using (Stream fileStream = await cloudStorage.GetFileStreamAsync(storageName, filePath))
//         {
//             //read from fileStream to string
//             using (var reader = new StreamReader(fileStream, Encoding.UTF8))
//             {
//                 var fileContents = await reader.ReadToEndAsync();
//             }
//         }
// #endif

        using (Stream fileStream = await cloudStorage.GetFileStreamAsync(storageName, filePath))
        {
            return await DeserializeJsonFromStreamAsync<T>(fileStream, cancellationToken);
        }
    }
}