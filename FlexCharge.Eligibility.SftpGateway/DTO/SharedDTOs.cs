using System;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Mvc.Validators;
using FlexCharge.Common.SensitiveData.Obfuscation;
using FlexCharge.Contracts.Common;

namespace FlexCharge.Eligibility.SftpGateway.DTO;

public class PaymentMethodBase
{
    [Required] public string HolderName { get; set; }

    [Required] public string CardType { get; set; } //Credit/Debit/Prepaid Card
    [Required] public string CardBrand { get; set; } //VISA, Mastercard, AMEX, etc.

    /// <summary>
    /// Country Code
    /// </summary>
    /// <remarks>ISO 3166-1 alpha-2 country code (2-letter)</remarks>
    [Required]
    public string CardCountry { get; set; }

    public string? CardIssuer { get; set; }
    public string? CardLevel { get; set; }
    public string? CardFingerprint { get; set; }

    [Required]
    [Range(1, 12)]
    [SensitiveData(ObfuscationType.MaskAllChars)]
    public int ExpirationMonth { get; set; }

    [Required]
    [Range(1, 2100)]
    [SensitiveData(ObfuscationType.MaskAllChars)]
    public int ExpirationYear { get; set; }
}

public class PaymentMethodLast4Digits : PaymentMethodBase
{
    [Required] public string CardBinNumber { get; set; }
    [Required] public string CardLast4Digits { get; set; }
}

public class PaymentMethod : PaymentMethodLast4Digits
{
    [Required]
    [SensitiveData(ObfuscationType.MaskAllChars)]
    public string CardNumber { get; set; } // Can be token
}

public class Transaction
{
    [Required] public string Id { get; set; } // External transaction identifier

    [Required] public DateTime TimestampUtc { get; set; }
    [Required] public int TimezoneUtcOffset { get; set; }

    public string? TransactionType { get; set; } // Auth, Capture, etc.

    [Required] public int Amount { get; set; }
    [Required] public string Currency { get; set; } // ISO 4217 currency code

    [Required] public string ResponseCode { get; set; }
    public string? ResponseDescription { get; set; }

    public string ResponseStatus { get; set; } // Approved, Declined, Voided, Refunded, Chargeback, etc.
    public string? ResponseSubStatus { get; set; }

    /// <summary>
    /// This is the source of the code from the original transaction ( Example: "nmi", "Paypal" )
    /// in case this value cannot be provided just leave this as empty string
    /// </summary>
    [Required]
    public string ResponseCodeSource { get; set; }

    public string ProcessorName { get; set; }

    public string AvsResultCode { get; set; }
    public string CvvResultCode { get; set; }
    string CavvResultCode { get; set; }

    [Required] public bool CardNotPresent { get; set; }

    #region Commented

    // public string ParentId { get; set; }
    //public string DynamicDescriptor { get; set; }

    #endregion
}

public class Payer
{
    public string? Id { get; set; } //External Id

    //[Required] 
    public DateTime? Birthdate { get; set; }

    [Required] public string Email { get; set; }

    public string? Phone { get; set; }
}

public class Merchant
{
    public string? Name { get; set; }

    public string Id { get; set; }
    public int Mcc { get; set; }
    public string Country { get; set; }
}

public class OrderItem
{
    public string Sku { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public int Amount { get; set; }
    public int DiscountAmount { get; set; }
    public int Tax { get; set; }
    public string DiscountType { get; set; }
    public int Quantity { get; set; }
}

public class BillingInformation
{
    [Required] public string FirstName { get; set; }

    [Required] public string LastName { get; set; }

    //public string? MiddleName { get; set; }

    public string? Phone { get; set; }

    [Required] public string Country { get; set; }

    /// <summary>
    /// Country Code
    /// </summary>
    /// <remarks>ISO 3166-1 alpha-2 country code (2-letter)</remarks>
    [Required]
    public string CountryCode { get; set; }

    [Required] public string AddressLine1 { get; set; }

    public string? AddressLine2 { get; set; }

    [RequiredIf(nameof(CountryCode), desiredValue: "US", trim: true)]
    public string? State { get; set; }

    [Required] public string City { get; set; }

    [Required] public string Zipcode { get; set; }
}

public static class BillingInformationExtensions
{
    public static BillingAddress ToBillingAddress(this BillingInformation billingInformation)
    {
        return new BillingAddress()
        {
            Address1 = billingInformation.AddressLine1,
            Address2 = billingInformation.AddressLine2,
            City = billingInformation.City,
            State = billingInformation.State,
            Zip = billingInformation.Zipcode,
            Country = billingInformation.Country,
            Phone = billingInformation.Phone ?? ""
        };
    }
}

public class ShippingInformation
{
    [Required] public string FirstName { get; set; }

    [Required] public string LastName { get; set; }

    public string? Phone { get; set; }

    [Required] public string Country { get; set; }

    /// <summary>
    /// Country Code
    /// </summary>
    /// <remarks>ISO 3166-1 alpha-2 country code (2-letter)</remarks>
    [Required]
    public string CountryCode { get; set; }

    [Required] public string AddressLine1 { get; set; }

    public string? AddressLine2 { get; set; }

    [RequiredIf(nameof(CountryCode), desiredValue: "US", trim: true)]
    public string? State { get; set; }

    [Required] public string City { get; set; }

    [Required] public string Zipcode { get; set; }
}

public class Subscription
{
    [Required] public string SubscriptionId { get; set; }

    [Required] public string SchemeTransactionId { get; set; }
    [Required] public string SchemeBrand { get; set; }

    [Required] public string Interval { get; set; }
    [Required] public int Price { get; set; }
    [Required] public string Currency { get; set; }
    public string PaymentNumber { get; set; }
    public string TotalPayments { get; set; }
}

public class AdditionalField
{
    public string Key { get; set; }
    public string Value { get; set; }
}