using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands.Orders;
using FlexCharge.Orders.Services.PayoutServices;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Consumers;

public class
    GenerateOrdersReportsCommandConsumer : IdempotentCommandConsumer<GenerateOrdersReportsCommand>
{
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

    public GenerateOrdersReportsCommandConsumer(
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
    }

    protected override async Task ConsumeCommand(GenerateOrdersReportsCommand command,
        CancellationToken cancellationToken)
    {
        _backgroundWorkerCommandQueue.Enqueue(new GenerateOrdersSnapshotReportBackgroundService());
        _backgroundWorkerCommandQueue.Enqueue(new GenerateOrdersExceptionsReportBackgroundService());

        await Task.CompletedTask;
    }
}