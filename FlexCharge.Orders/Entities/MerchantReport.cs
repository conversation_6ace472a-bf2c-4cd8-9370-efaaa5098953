using System;
using FlexCharge.Orders.Services;
using System.Collections.Generic;

namespace FlexCharge.Orders.Entities;

public class MerchantReport : AuditableEntity
{
    public string ReportName { get; set; } = string.Empty;
    public string ReportDescription { get; set; } = string.Empty;
    public string ReportFileName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public string ReportFormat { get; set; } = string.Empty;
    public Guid ReportTypeId { get; set; }

    public string? ErrorMessage { get; set; }
    public string? FilePath { get; set; }

    public Guid MerchantId { get; set; }
    public Merchant Merchant { get; set; }
}