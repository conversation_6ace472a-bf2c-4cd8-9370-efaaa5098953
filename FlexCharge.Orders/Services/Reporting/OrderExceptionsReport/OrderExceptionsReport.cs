using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace FlexCharge.Orders.Services.Reporting.OrderExceptionsReport;

public class OrderExceptionsReport : ReportBase<OrderExceptionsReportParams, OrderExceptionsReportOptions,
    OrderExceptionsReportModel>
{
    public override Guid ReportId => new("326985b0-1848-4e7c-b787-f4733f246f33");
    public override string ReportName => "order-exceptions";

    public OrderExceptionsReport(IOptions<OrderExceptionsReportOptions> options, PostgreSQLDbContext dbContext) : base(
        options, dbContext)
    {
    }

    protected override bool TryValidate(OrderExceptionsReportParams parameter, out ReportValidator dict)
    {
        throw new NotImplementedException();
    }

    public override async Task<List<OrderExceptionsReportModel>> GenerateRowsAsync(DateTime from, DateTime to,
        OrderExceptionsReportParams param, CancellationToken ctoken)
    {
        var result = new List<OrderExceptionsReportModel>();

        using var workspan = Workspan.Start<OrderExceptionsReport>()
            .Baggage("ReportId", ReportId.ToString())
            .Baggage("ReportName", ReportName)
            .Baggage("From", from.ToString("o"))
            .Baggage("To", to.ToString("o"))
            .Baggage("Mid", param.Mid.ToString())
            .LogEnterAndExit();

        workspan.Log.Information("Generating report for {ParamMid} from {From} to {Unknown}", param.Mid, from, to);

        try
        {
            await foreach (var row in GenerateRowsYieldAsync(from, to, param, ctoken))
            {
                result.Add(row);
            }
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error generating report rows");
            throw;
        }

        return result;
    }

    public override async IAsyncEnumerable<OrderExceptionsReportModel> GenerateRowsYieldAsync(
        DateTime from,
        DateTime to,
        OrderExceptionsReportParams param,
        [EnumeratorCancellation] CancellationToken ctoken)
    {
        using var workspan = Workspan.Start<OrderExceptionsReport>()
            .LogEnterAndExit();

        var dbContext = _dbContext as PostgreSQLDbContext;

        // Toggle depending on backend's handling of capture/settlement
        bool settlementsSupported = false;

        var query = dbContext.Transactions
            .Include(x => x.Order)
            .Where(ai =>
                (ai.Type == "refund" || ai.Type == "credit" || ai.Type == "chargeback" || ai.Type == "void") &&
                ai.CreatedOn >= from && ai.CreatedOn <= to &&
                ai.Order.MerchantId == param.Mid);


        await foreach (var ai in query.AsAsyncEnumerable().WithCancellation(ctoken))
        {
            var activity = new OrderExceptionsReportModel
            {
                OrderState = ai.Type,
                OrderId = ai.Order.Id,
                ExternalId = ai.Order.ReferenceNumber,
                EventTimestamp = ai.CreatedOn,
                Bin = ai.Order.Bin,
                Last4 = ai.Order.Last4,
                Currency = ai.Order.Currency,
                Amount = Utils.Formatters.IntToDecimal(ai.TotalAmount),
                // CurrencyCode = ai.Order.CurrencyCode,
                // CurrencySymbol = ai.Order.CurrencySymbol
            };

            var mappedState = ai.Type switch
            {
                "charge" => OrderStatusCategory.completed.ToString(),
                "capture" => settlementsSupported ? "Captured" : OrderStatusCategory.completed.ToString(),
                "debit" => settlementsSupported ? "Captured" : OrderStatusCategory.completed.ToString(),
                "refund" or "credit" => OrderStatusCategory.returned.ToString(),
                "chargeback" or "refundpredispute" => OrderStatusCategory.disputed.ToString(),
                "settlement" when settlementsSupported => "Settled",
                "void" => "Voided",
                _ => "Unknown"
            };

            activity.OrderState = mappedState;

            yield return activity;
        }
    }
}