using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper.Configuration;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Orders;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Orders.Services.FundsReserveService;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Services.BatchServices.Batches;

public class CalculateFISCBatchesCommand : BackgroundWorkerCommand
{
    private Guid? Pid { get; set; }
    private Guid? Mid { get; set; }

    private DateTime Date { get; set; }
    private bool ForceRecalculation { get; set; }

    public CalculateFISCBatchesCommand(Guid? pid, Guid? mid, DateTime date, bool forceRecalculation)
    {
        Pid = pid;
        Mid = mid;
        Date = date;
        ForceRecalculation = forceRecalculation;
    }

    public CalculateFISCBatchesCommand()
    {
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<CalculateFISCBatchesCommand>()
            .Baggage(nameof(Pid), Pid)
            .LogEnterAndExit();

        var batchService = serviceProvider.GetRequiredService<IBatchService>();
        var reserveService = serviceProvider.GetRequiredService<IFundsReserveService>();
        var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();
        var cloudStorageService = serviceProvider.GetService<ICloudStorage>();
        var fundsReserveCommand =
            serviceProvider.GetRequiredService<IRequestClient<GetMerchantFundsReserveConfigCommand>>();

        try
        {
            var merchants = dbContext.Merchants.AsQueryable();

            if (Pid != null && Pid != Guid.Empty)
                merchants = merchants.Where(x => x.Pid == Pid);

            if (Mid != null && Mid != Guid.Empty)
                merchants = merchants.Where(x => x.Mid == Mid);

            var merchantsToCalc = await merchants.ToListAsync();

            if (!merchantsToCalc.Any())
            {
                workspan.Log.Information("PAYOUTS: No merchants found to calculate payouts");
                return;
            }

            workspan.Log.Information("PAYOUTS: Found {MerchantsCount} Merchants to calculate payouts",
                merchantsToCalc.Count);

            //Setup date for calculations and validations. 
            var now = DateTime.Now;

            //Execution
            foreach (var merchant in merchantsToCalc)
                await CalculateBatchForMerchant(merchant, now);

            workspan.Log.Information("PAYOUTS: Payouts calculations completed");
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "PAYOUTS: Can't calculate FISC Batches");
            throw;
        }

        async Task CalculateBatchForMerchant(Merchant merchant, DateTime now)
        {
            workspan.Baggage(nameof(merchant.Mid), merchant.Mid);
            workspan.Log.Information("PAYOUTS: Starting payout calculation for {MerchantName} Mid: {MerchantId}",
                merchant.Dba, merchant.Mid);

            var payoutFrequency = merchant.ToPayoutFrequencyType();
            workspan.Log.Information(
                "PAYOUTS: Payout frequency is set to: {PayoutFrequency} for Mid: {MerchantID}",
                payoutFrequency, merchant.Mid);

            //check if merchant should calculate payouts today
            if (!merchant.IsPayoutDate(now) && !ForceRecalculation)
            {
                workspan.Log.Information(
                    "PAYOUTS: Skipping payout calculation for {MerchantName} Mid: {MerchantId} as {Date} is not a payout date, next payout date {NextPayoutDate}",
                    merchant.Dba, merchant.Mid, now, merchant.GetNextPayoutDate(now));
                return;
            }

            var batchStartDate = Date != DateTime.MinValue
                ? payoutFrequency.GetCurrentBatchPayoutRangeStartDate(Date)
                : payoutFrequency.GetCurrentBatchPayoutRangeStartDate(now);

            var batchEndDate = Date != DateTime.MinValue
                ? payoutFrequency.GetCurrentBatchPayoutRangeEndDate(Date)
                : payoutFrequency.GetCurrentBatchPayoutRangeEndDate(now);

            try
            {
                var sender = await dbContext.FinancialAccounts
                    .FirstOrDefaultAsync(
                        x =>
                            x.RelatedEntityId == merchant.Pid &&
                            x.RelatedEntityType == FinancialAccountsRelatedEntityType.Partner,
                        cancellationToken);

                if (sender == null)
                {
                    workspan.RecordException(
                        new FlexChargeException($"PAYOUTS: Can't find sender financial account {merchant.Pid}"));

                    return;
                }

                var receiver = await dbContext.FinancialAccounts
                    .FirstOrDefaultAsync(
                        x =>
                            x.RelatedEntityId == merchant.Mid &&
                            x.RelatedEntityType == FinancialAccountsRelatedEntityType.Merchant,
                        cancellationToken);

                if (receiver == null)
                {
                    workspan.RecordException(
                        new FlexChargeException($"PAYOUTS: Can't find receiver financial account {merchant.Mid}"));

                    return;
                }

                var merchantFees =
                    await dbContext.MerchantFees.Where(x => x.MerchantId == merchant.Mid && x.IsActive)
                        .ToListAsync();

                //log merchant fees
                workspan.Log.Information("PAYOUTS: Merchant fees: {MerchantFees}",
                    JsonConvert.SerializeObject(merchantFees));

                var fundsReserveConfiguration =
                    await fundsReserveCommand.GetResponse<GetMerchantFundsReserveConfigCommandResponse>(
                        new GetMerchantFundsReserveConfigCommand
                        {
                            Mid = merchant.Mid
                        }).ConfigureAwait(false);

                await using (await dbContext.Database.BeginTransactionAsync())
                {
                    //log batch start creation
                    workspan.Log.Information(
                        "PAYOUTS: Creating batch for merchant {MerchantId} date {Date}",
                        merchant.Mid, now);

                    var batch = await batchService.GetOrCreateBatchV2(
                        sender,
                        receiver,
                        nameof(FIMovementType.FISC),
                        batchStartDate,
                        batchEndDate,
                        isOffline: true,
                        includeRecords: true,
                        overrideExistingBatch: true,
                        autoPosted: false);

                    await AddSettlementsToBatch(merchant.Mid, batchStartDate, batchEndDate, batch, merchantFees);
                    await AddDisputesToBatch(merchant.Mid, batchStartDate, batchEndDate, batch, merchantFees);
                    await AddRefundsToBatch(merchant.Mid, batchStartDate, batchEndDate, batch, merchantFees);
                    await ApplyReserveReleases(receiver.Id, batch, fundsReserveConfiguration?.Message);
                    await ApplyReserveIfApplicable(merchant.Mid, batchStartDate, batchEndDate, batch,
                        fundsReserveConfiguration?.Message);
                    await ApplyFundsReserveUtilization(receiver.Id, batch);

                    await dbContext.SaveChangesAsync();
                    await dbContext.Database.CommitTransactionAsync();

#if DEBUG
                    workspan.Log.Information("PAYOUTS: {BatchDescribe}", batch.Describe());
#endif

                    try
                    {
                        workspan.Log.Information(
                            "PAYOUTS: Generating Summary report for batch {BatchId} for merchant {MerchantId}",
                            batch.Id, merchant.Mid);

                        // generate CSV
                        var reportCsv = GenerateCSVFromRows(batch.ToVerticalCsvRows());

                        // store batch on s3
                        await Store(merchant, reportCsv, cloudStorageService);
                    }
                    catch (Exception e)
                    {
                        workspan.RecordFatalException(e,
                            "PAYOUTS: Failed to generate & store summary report for batch {BatchId}",
                            batch.Id);
                    }

                    workspan.Log.Information(
                        "PAYOUTS: Batch ID: {BatchId} created for merchant {MerchantId} on {Date}",
                        batch.Id, merchant.Mid, now);
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "PAYOUTS: Generate payouts for merchant {MerchantId} failed",
                    merchant.Mid);
            }
        }

        async Task AddSettlementsToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate,
            Batch batch, List<MerchantFee> merchantFees)
        {
            var orders = await dbContext.Orders
                //.Include(x => x.ActivityItems)
                .Where(x =>
                    x.OrderPayoutStatus != OrderPayOutStatuses.Completed &&
                    x.OrderPayinStatus == PayInStatuses.Completed && //update when we will start taking liability on NSF
                    x.StatusCategory == nameof(OrderStatusCategory.completed) &&
                    x.PaidOutOfBand != true &&
                    x.IsGhostMode == false &&
                    x.MerchantId == mid &&
                    x.OrderPlacedDate.Date >= batchStartDate && x.OrderPlacedDate.Date <= batchEndDate)
                .ToListAsync();

            foreach (var order in orders)
            {
                //log order 
                workspan.Log.Information("Processing order {OrderId} status: {Status}", order.Id,
                    order.StatusCategory);

                batch.AddCompletedOrder(order, merchantFees);
            }

            workspan.Log.Information("Found {OrdersCount} orders for batch {BatchId}", orders?.Count,
                batch.Id);
        }

        async Task AddDisputesToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate,
            Batch batch, List<MerchantFee> merchantFees)
        {
            var disputesToCollect = await dbContext
                .Transactions
                .Include(x => x.Order)
                .Where(x =>
                    x.Type == "chargeback" &&
                    x.Order.MerchantId == mid &&
                    x.Order.StatusCategory == nameof(OrderStatusCategory.completed) &&
                    x.BatchId == null &&
                    //x.CreatedOn.Date >= batchStartDate &&
                    x.CreatedOn.Date <= batchEndDate &&
                    x.CreatedOn > new DateTime(2024, 07, 01).ToUtcDateTime())
                .ToListAsync();

            workspan.Log.Information("Found {DisputesToCollectCount} disputes for batch {BatchId}",
                disputesToCollect?.Count, batch.Id);

            foreach (var dispute in disputesToCollect)
            {
                batch.AddChargeback(dispute.Order, dispute.CreatedOn, dispute.Description,
                    merchantFees);

                dispute.BatchId = batch.Id;
                dbContext.Transactions.Update(dispute);
            }
        }

        async Task AddRefundsToBatch(Guid mid, DateTime batchStartDate, DateTime batchEndDate,
            Batch batch, List<MerchantFee> merchantFees)
        {
            var refundsToCollect = await dbContext.Transactions.Include(x => x.Order)
                .Where(x =>
                    x.Type == "refund" &&
                    x.Order.MerchantId == mid &&
                    x.Order.StatusCategory == nameof(OrderStatusCategory.completed) &&
                    //x.BatchId == null &&
                    x.CreatedOn.Date >= batchStartDate &&
                    x.CreatedOn.Date <= batchEndDate).ToListAsync();

            workspan.Log.Information("Found {RefundsToCollectCount} refunds for batch {BatchId}",
                refundsToCollect?.Count,
                batch.Id);

            foreach (var refund in refundsToCollect)
            {
                batch.AddReturn(refund.Order, refund.CreatedOn, refund.TotalAmount,
                    refund.Description, merchantFees);

                refund.BatchId = batch.Id;
                dbContext.Transactions.Update(refund);
            }
        }

        async Task ApplyReserveIfApplicable(Guid mid, DateTime batchStartDate, DateTime batchEndDate,
            Batch batch, GetMerchantFundsReserveConfigCommandResponse configuration)
        {
            if (configuration.ConfigurationId == Guid.Empty)
            {
                workspan.Log.Information("No reserve configuration found for merchant {MerchantId}", mid);
                return;
            }

            if (batch.TotalAmount <= 0)
            {
                workspan.Log.Information(
                    "Skipping reserve holding for merchant{MerchantId}  due to zero or negative batch{BatchId} total amount",
                    mid,
                    batch.Id);

                return;
            }

            var ReserveAmountType = "";
            if (configuration.ReserveAmountType == "PERCENTAGE")
            {
                ReserveAmountType = "%";
            }
            else
            {
                ReserveAmountType = batch.Receiver.Currency;
            }


            batch.FundsReserveConfig = configuration.ConfigurationId;
            batch.Comment =
                $"Reserve was applied based on " +
                $"{Formatters.IntToDecimal(configuration.ReserveAmount)}{ReserveAmountType} " +
                $"for {configuration.ReserveDays} days " +
                $"  configuration ID: {configuration.ConfigurationId} ";

            var orders = await dbContext.Orders
                .Where(x =>
                    x.MerchantId == mid &&
                    x.StatusCategory == nameof(OrderStatusCategory.completed) &&
                    x.OrderPayinStatus == PayInStatuses.Completed &&
                    x.OrderPayoutStatus != OrderPayOutStatuses.Completed &&
                    x.OrderPlacedDate.Date >= batchStartDate &&
                    x.OrderPlacedDate.Date <= batchEndDate)
                .Include(x => x.ActivityItems)
                .ToListAsync(cancellationToken: cancellationToken);

            foreach (var order in orders)
            {
                var reserveAmount = reserveService.CalculateReserveAmount(configuration.ReserveAmountType,
                    configuration.ReserveAmount, order.Amount);

                workspan.Log.Information("Order amount: {OrderAmount} Reserve amount: {ReserveAmount}",
                    order.Amount,
                    reserveAmount);

                var reservedAmount = await reserveService.GetHeldReserveBalanceByOrderIdAsync(order.Id);
                if (reservedAmount > 0)
                {
                    workspan.Log.Information("Skipping: Reserve found for order {OrderId}", order.Id);

                    //if order is already disputed or refunded skip reserve
                    if (order.Amount - order.RefundsAmount == 0)
                    {
                        workspan.Log.Information("Cancelling reserve for refunded order {OrderId}", order.Id);
                        batch.CancelReserve(order.Id, reservedAmount,
                            ReserveCancellationReason.OrderRefunded.DescriptionAttr());
                    }

                    //TODO take care of partial refunds


                    continue;
                }

                var recordId = batch.ApplyReserve(order.Id, reserveAmount,
                    ReserveReason.DisputeOrChargebackPotential.DescriptionAttr());

                workspan.Log.Information(
                    "Applied reserve {ReserveAmount} for order {OrderId} batch record {BatchRecordId}",
                    Formatters.IntToDecimal(reserveAmount).ToString("C0"),
                    order.Id, recordId);
            }
        }

        async Task ApplyReserveReleases(Guid financialAccountId, Batch batch,
            GetMerchantFundsReserveConfigCommandResponse configuration)
        {
            var reserveExpiry = DateTime.Now.AddDays(-configuration.ReserveDays);

            var fundsToRelease =
                await reserveService.GetReservesToReleaseAsync(financialAccountId, reserveExpiry.ToUtcDateTime());

            foreach (var reserveToRelease in fundsToRelease)
            {
                workspan.Log.Information("Releasing reserve {ReserveAmount} for order {OrderId}",
                    Formatters.IntToDecimal(reserveToRelease.Amount).ToString("C0"),
                    reserveToRelease.OrderId);

                batch.AddReleaseReserve(reserveToRelease.OrderId, reserveToRelease.Amount,
                    ReserveReleaseReason.ExpirationOfHoldPeriod.DescriptionAttr());
            }
        }

        async Task ApplyFundsReserveUtilization(Guid financialAccountId, Batch batch)
        {
            if (batch.CalculatePayoutAmount() >= 0)
            {
                workspan.Log.Information(
                    "PAYOUTS: No reserve utilization required for financial account {FinancialAccountId} batch {BatchId}",
                    financialAccountId, batch.Id);
                return;
            }

            var availableFundsReserve = await reserveService.GetReserveBalanceAsync(financialAccountId);
            var payoutAmount = batch.CalculatePayoutAmount();

            if (payoutAmount < 0 && availableFundsReserve.AmountInCents >= Math.Abs(payoutAmount))
            {
                workspan.Log.Information(
                    "PAYOUTS: Reserve utilization required for financial account {FinancialAccountId} batch {BatchId}",
                    financialAccountId, batch.Id);

                var reserveUtilizationResponse =
                    await reserveService.GetReservesForUtilizationAsync(financialAccountId,
                        Math.Abs(batch.CalculatePayoutAmount()));

                foreach (var reserveToUtilize in reserveUtilizationResponse)
                {
                    batch.AddReserveUtilization(reserveToUtilize.OrderId, reserveToUtilize.Amount,
                        ReserveUtilizationReason.LossCoverage.DescriptionAttr());
                }
            }
            else
            {
                workspan.Log.Information(
                    "PAYOUTS: Reserve utilization not required for financial account {FinancialAccountId} batch {BatchId}",
                    financialAccountId, batch.Id);
            }
        }
    }

    private static async Task Store(Merchant merchant, string reportCsv, ICloudStorage cloudStorageService)
    {
        var fileName = $"{merchant.Mid}_settlement-summary_{DateTime.UtcNow:yyyyMMdd-HHmmss}.csv";

        var bucket = $"sftp-gateway-{EnvironmentHelper.GetCurrentEnvironment()}".ToLower();

        var folderPath = $"{merchant.Mid}/reports";

        var filePath = $"{folderPath}/{fileName}";

        // Convert the CSV string to a stream
        using var reportStream = new MemoryStream(Encoding.UTF8.GetBytes(reportCsv));

        // Upload the stream to cloud storage
        await cloudStorageService.UploadFileAsync(reportStream, bucket, filePath);
    }

    protected string GenerateCSVFromRows(IEnumerable<KeyValuePair<string, string>> rows)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = false,
            //PrepareHeaderForMatch = args => args.Header.ToLower(),
        };

        String result;
        using (MemoryStream mem = new MemoryStream())
        {
            using (StreamWriter sw = new StreamWriter(mem))
            {
                CSVHelper.Create(rows, sw, config);
            }

            var stringBytes = mem.ToArray();
            result = Encoding.UTF8.GetString(stringBytes, 0, (int) stringBytes.Length);
        }

        return result;
    }
}