using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Shared.Sftp;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Services.BatchServices.Batches;
using FlexCharge.Orders.Services.Reporting;
using FlexCharge.Orders.Services.Reporting.OrdersSnapshotReport;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services.PayoutServices;

public class GenerateOrdersSnapshotReportBackgroundService : BackgroundWorkerCommand
{
    public GenerateOrdersSnapshotReportBackgroundService()
    {
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<GenerateOrdersSnapshotReportBackgroundService>()
            .LogEnterAndExit();

        try
        {
            workspan.Log.Information("Starting GenerateOrdersSnapshotReportBackgroundService");

            //foreach merchant 
            var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();

            var reportId = new Guid("a9967a72-c4e4-49f4-9f3b-0a89c966fd07");
            var reportName = "order-snapshot-report";

            var listOfMerchants = await dbContext.MerchantReports
                .Include(x => x.Merchant)
                .Where(x => x.ReportTypeId == reportId)
                .Select(x => new
                {
                    x.Merchant.Mid
                })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            if (listOfMerchants == null || !listOfMerchants.Any())
            {
                workspan.Log.Information("No merchants found for Order Snapshot Report");
                return;
            }

            foreach (var merchant in listOfMerchants)
            {
                workspan.Log.Information("Processing merchant {MerchantId}", merchant.Mid);

                var reportingService = serviceProvider.GetRequiredService<IReportingService>();

                var result = await reportingService.GenerateAndStoreReportAsync(
                    reportId,
                    DateTime.UtcNow.Date.AddDays(-1),
                    DateTime.UtcNow.Date.AddTicks(-1),
                    $"{merchant.Mid}/{SftpFolderNamesHelper.Reports}",
                    $"{merchant.Mid}_{reportName}_{DateTime.UtcNow:yyyyMMdd-HHmmss}",
                    new OrdersSnapshotReportParams
                    {
                        Mid = merchant.Mid
                    });

                workspan.Log.Information(
                    "Report is being generated for merchant {MerchantId}", merchant.Mid);
            }
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: CalculateOrdersSnapshotReportCommand > ExecuteAsync");
        }
    }
}