{"app": {"name": "orders-service", "version": "0.0.1"}, "activity": {"domain": "Orders", "source": "Orders"}, "grpc": {"VaultEndpoint": "http://vault.production:5000"}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "3v4ll30aqfb435trbds7447524", "Region": "us-east-1", "UserPoolId": "us-east-1_xHv2n5DhZ", "AppClientId": "3v4ll30aqfb435trbds7447524"}, "basicOauth": {"expiryMinutes": 60, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudience": "orders"}, "cache": {"connectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "idempotency.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "tracking.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379"}, "dataStream": {"provider": "kinesis"}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "bccEmail": "<EMAIL>", "merchant_order_notification_after_full_capture": "d-9be8b802dbcb43be952b732e664a199e", "order_confirmation_after_full_debit": "d-3c37a7835d7b4f3c99b2acb5d1fe9aa0", "order_confirmation_after_initial_auth_captured": "d-3c37a7835d7b4f3c99b2acb5d1fe9aa0", "order_confirmation_no_auth": "d-dd1e7011e621452cba91207151053999", "click_to_refund_succeeded": "d-91eb08691608492ab20bde03fd326b21", "click_to_refund_already_refunded": "d-edeeae1f62dc4c2bb77f307239ee6003", "click_to_refund_failed": "d-c02898076d8b46208311b095b1a6eb7d", "stripe_update_billing_information": "d-21c8054266c04233aa2e43ed6d0b53f2", "senderEmail": "<EMAIL>", "senderName": "FlexFactor"}, "sms": {"SID": "**********************************", "Token": "3e902332976f1378c0edd4a8889230c2", "TwilioPhone": "+16206340920", "WhatsappPhone": "", "TwilioCompanyName": "", "ServiceSid": "MG70eaa8fa28ab1ebfb685253fb4e672e8", "TwilioAuthyAPIKey": "", "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start", "TwilioVoiceSmsChecktUrl": ""}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/orders-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "commentsEnabled": true, "reDocEnabled": false, "name": "v1", "title": "orders-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "backgroundWorkerService": {"executionInterval": 500}, "sftpGateway": {"bucket": "sftp-gateway-production", "rootFolderToWatch": "", "processingRootFolder": "_INTERNAL/"}, "internalReporting": {"emailTemplateId": "d-d3185eabd44c4469b2f804f55b0fddd2"}, "urlShortener": {"baseUrl": "https://flfc.io"}, "Kestrel": {"Endpoints": {"HTTP": {"Url": "http://*:80", "Protocols": "Http1AndHttp2"}, "gRPC": {"Url": "http://*:5000", "Protocols": "Http2"}}}, "AllowedHosts": "*"}