using AutoMapper;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Merchants.Services.ApplicationServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Merchant = FlexCharge.Merchants.Entities.Merchant;

namespace FlexCharge.Merchants.Services.MerchantServices;

public class MerchantService : IMerchantService
{
    private PostgreSQLDbContext _dbContext { get; set; }
    private readonly IMapper _mapper;
    private readonly IEmailSender _emailSender;
    private readonly IPublishEndpoint _publisher;
    private readonly IOptions<LogoImageValidationOptions> _logoImageValidationOptions;
    private readonly ICloudStorage _cloudStorage;
    private readonly IConfiguration _configuration;
    private readonly IOptions<DocumentUploadOptions> _documentUploadOptions;
    private readonly IUrlShortenerService _urlShortenerService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public MerchantService(PostgreSQLDbContext dbContext,
        IMapper mapper,
        IPublishEndpoint publisher, IHttpContextAccessor contextAccessor,
        IOptions<LogoImageValidationOptions> logoImageValidationOptions,
        ICloudStorage cloudStorage, IEmailSender emailSender, IConfiguration configuration,
        IOptions<DocumentUploadOptions> documentUploadOptions, IUrlShortenerService urlShortenerService,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _publisher = publisher;
        _logoImageValidationOptions = logoImageValidationOptions;
        _cloudStorage = cloudStorage;
        _emailSender = emailSender;
        _configuration = configuration;
        _documentUploadOptions = documentUploadOptions;
        _urlShortenerService = urlShortenerService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task StoreDocumentAsync(Merchant merchant, IFormFile file,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", merchant.Id)
            .Baggage("Mid", merchant.Id)
            .Baggage("FileName", file.FileName)
            .LogEnterAndExit();

        using var reader = new StreamReader(file.OpenReadStream());
        var stream = reader.BaseStream;
        try
        {
            await FileValidator.ValidateAsync(stream, file.FileName, new FileValidationOptions
            {
                MaxFileSize = 10 * 1024 * 1024,
                MinFileSize = 1,
                Types = new List<string>
                    {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".csv", ".Jpeg", ".jpg", ".jpeg", ".png", ".PNG"}
            });

            await StoreDocumentAsync(merchant, file.FileName, file.ContentType, stream, sendNotification);
        }
        catch (FileValidationException e)
        {
            throw new FlexValidationException("Document", e.Message);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task StoreDocumentAsync(Merchant merchant, string fileName, string contentType, Stream stream,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", merchant.Id)
            .Baggage("Mid", merchant.Id)
            .Baggage("FileName", fileName)
            .Baggage("ContentType", contentType)
            .LogEnterAndExit();

        try
        {
            //arn:aws:s3:::flex-staging-static-assets
            var root = Environment.GetEnvironmentVariable("AWS_S3_STATIC_FILES_BUCKET");
            var folder = "documents";

            var documentRandomId = Guid.NewGuid();

            var destination = $"{merchant.Id}/{folder}";

            await _cloudStorage.CreateFolderIfMissingAsync(root, destination);
            await _cloudStorage.UploadFileAsync(stream,
                root,
                $"{documentRandomId}_{fileName}",
                folder: destination,
                allowPublicAccess: false,
                uploadFileInChunks: true); // merchant documents can be huge

            try
            {
                await ApplyMerchantActivity(merchant.Id, new ApplicationActivity
                {
                    Category = "Merchant",
                    Activity = "Document Uploaded",
                    UpdateMessage = $"Document {documentRandomId}_{fileName} uploaded",
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Failed create activity while saving document");
            }

            var documentPath = $"{root}/{destination}/{documentRandomId}_{fileName}";
            var documentFileName = $"{documentRandomId}_{fileName}";
            var documentId = documentRandomId.ToString();

            workspan
                .Tag("DocumentId", documentId)
                .Tag("FileName", documentFileName)
                .Tag("FullPath", documentPath)
                .Log.Information($"Document stored successfully");


            var document = new Document
            {
                Name = documentFileName,
                Path = documentPath,
                Type = contentType,
                ApplicationId = merchant.Id,
                MerchantId = merchant.Id,
            };

            _dbContext.Documents.Add(document);
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            throw;
        }
    }

    public async Task StoreDocumentAsync(Guid mid, string fileName, string contentType, Stream stream,
        bool sendNotification = true)
    {
        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Id == mid);
        await StoreDocumentAsync(merchant, fileName, contentType, stream, sendNotification);
    }

    public async Task ApplyMerchantActivity(Guid applicationId, ApplicationActivity activity)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var userId = GetUserIdFromJwt();

            if (userId != null)
            {
                activity.UserId = userId.Value;
            }

            activity.User = GetEmailFromJwt();

            var entity = await _dbContext.Applications
                .Include(x => x.Activities)
                .SingleOrDefaultAsync(x => x.Id == applicationId);

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{applicationId}");

            entity.Activities.Add(activity);
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to apply activity");
            throw;
        }
    }

    public async Task<PagedDTO<MerchantResponse>> GetAsync(MerchantQueryDTO payload)
    {
        using var workspan = Workspan.Start<MerchantService>().LogEnterAndExit();

        try
        {
            var dbset = _dbContext.Merchants
                .Include(x => x.Address)
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.PrimaryContact)   
                .Include(x => x.SalesAgency)
                .Include(x => x.FundsReserveConfigurations)
                .Include(x => x.Documents)
                .Include(x => x.Fees).AsQueryable();

            dbset = ApplyMerchantIdFilter(dbset, payload.Mid);
            dbset = ApplyPartnerFilter(dbset, payload.Pid);
            dbset = ApplyIntegrationPartnerFilter(dbset, payload.IntegrationPartnerId);
            dbset = ApplyStatusFilter(dbset, payload.Status);
            dbset = ApplySearchFilter(dbset, payload.Q);

            var pagedMerchants = await dbset.ToPagedListAsync(payload.PageNumber, payload.PageSize);

            return _mapper.Map<IPagedList<Merchant>, PagedDTO<MerchantResponse>>(pagedMerchants);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: MerchantService => Unable to get merchants");
            throw;
        }
    }

    private IQueryable<Merchant> ApplySearchFilter(IQueryable<Merchant> dbset, string q)
    {
        if (!string.IsNullOrEmpty(q))
        {
            dbset = dbset.Where(m =>
                m.CompanyName.ToLower().Contains(q) ||
                m.Dba.ToLower().Contains(q) ||
                m.Id.ToString().ToLower().Contains(q) ||
                m.LegalEntityName.ToLower().Contains(q));
        }

        return dbset;
    }

    private IQueryable<Merchant> ApplyStatusFilter(IQueryable<Merchant> dbset, ActiveInActive? status)
    {
        if (status.HasValue)
        {
            dbset = dbset.Where(m => m.Status == status);
        }

        return dbset;
    }

    private IQueryable<Merchant> ApplyMerchantIdFilter(IQueryable<Merchant> dbset, Guid? mid)
    {
        if (mid.HasValue)
        {
            dbset = dbset.Where(m => m.Id == mid);
        }

        return dbset;
    }
    
    private IQueryable<Merchant> ApplyPartnerFilter(IQueryable<Merchant> dbset, Guid? pid)
    {
        if (pid.HasValue)
        {
            dbset = dbset.Where(m => m.PartnerId == pid);
        }

        return dbset;
    }

    private IQueryable<Merchant> ApplyIntegrationPartnerFilter(IQueryable<Merchant> dbset, Guid? integrationPartnerId)
    {
        if (integrationPartnerId.HasValue)
        {
            dbset = dbset.Where(m => m.IntegrationPartnerId == integrationPartnerId);
        }

        return dbset;
    }

    private string GetEmailFromJwt()
    {
        if (_httpContextAccessor.HttpContext == null || _httpContextAccessor.HttpContext.User == null)
        {
            return null;
        }

        var email = _httpContextAccessor.HttpContext.User.Claims
            .FirstOrDefault(c => c.Type.ToString().Contains(ClaimTypes.Email))?.Value;
        return email;
    }

    private Guid? GetUserIdFromJwt()
    {
        if (_httpContextAccessor.HttpContext == null || _httpContextAccessor.HttpContext.User == null)
        {
            return null;
        }

        var IsValid = Guid.TryParse(_httpContextAccessor.HttpContext.User.Claims
            .Where(x => x.Type == ClaimTypes.NameIdentifier)
            .Select(x => x.Value).SingleOrDefault(), out Guid uid);

        if (IsValid)
        {
            return uid;
        }
        else
        {
            return null;
        }
    }
}