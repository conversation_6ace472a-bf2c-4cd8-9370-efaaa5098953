using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Response;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace FlexCharge.Merchants.Services.MerchantServices;

public interface IMerchantService
{
    Task StoreDocumentAsync(Merchant merchant, IFormFile file,
        bool sendNotification = true);

    Task StoreDocumentAsync(Merchant merchant, string fileName, string contentType, Stream stream,
        bool sendNotification = true);

    Task StoreDocumentAsync(Guid mid, string fileName, string contentType, Stream stream,
        bool sendNotification = true);

    Task<PagedDTO<MerchantResponse>> GetAsync(MerchantQueryDTO payload);
}