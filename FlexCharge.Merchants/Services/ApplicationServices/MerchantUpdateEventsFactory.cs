using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Enums;
using FlexCharge.Merchants.Entities;

namespace FlexCharge.Merchants.Services.ApplicationServices;

public static class MerchantUpdateEventsFactory
{
    public static MerchantCreatedEvent ConstructMerchantCreatedEvent(Entities.Merchant merchant, IMapper mapper)
    {
        var merchantCreatedEvent = CreateEventFromMerchant<MerchantCreatedEvent>(merchant, mapper);

        return merchantCreatedEvent;
    }

    public static MerchantUpdatedEvent ConstructMerchantUpdatedEvent(Entities.Merchant merchant, IMapper mapper)
        => CreateEventFromMerchant<MerchantUpdatedEvent>(merchant, mapper);


    private static TEvent CreateEventFromMerchant<TEvent>(Entities.Merchant merchant,
        IMapper mapper) where TEvent : MerchantCreateOrUpdateEventBase, new()
    {
        var transactionBaseFee = merchant.Fees.SingleOrDefault(x => x.Name == "Fee Amount" && x.IsActive);
        var chargebackFee = merchant.Fees.SingleOrDefault(x => x.Name == "Chargeback Fee" && x.IsActive);
        var transactionFee = merchant.Fees.SingleOrDefault(x => x.Name == "Percentage" && x.IsActive);
        var refundFee = merchant.Fees.SingleOrDefault(x => x.Name == "Refund Fee" && x.IsActive);
        var riskFee = merchant.Fees.SingleOrDefault(x => x.Name == "Risk Fee" && x.IsActive);

        var @event = new TEvent()
        {
            MerchantId = merchant.Id,
            PartnerId = merchant.PartnerId,
            IntegrationPartnerId = merchant.IntegrationPartnerId,

            AccountId = merchant.AccountId,

            Currency = merchant.Currency,

            Pcidss = merchant.Pcidss,
            IntegrationType = merchant.IntegrationType,

            GhostModeThrottlePercentage = merchant.GhostModeThrottlePercentage,

            DynamicAuthorizationDiscountThrottlePercentage =
                merchant.DynamicAuthorizationDiscountThrottlePercentage,
            OfferRequestsRateLimitIntervalMS = merchant.OfferRequestsRateLimitIntervalMS,
            OfferRequestsRateLimitCount = merchant.OfferRequestsRateLimitCount,
            OfferRequestsThrottlePercentage = merchant.OfferRequestsThrottlePercentage,
            OfferRequestsMaxPerDay = merchant.OfferRequestsMaxPerDay,
            Offer_NSF_RequestsThrottle_Percentage = merchant.Offer_NSF_RequestsThrottle_Percentage,
            Orders_MaxMonthlyAmount = merchant.Orders_MaxMonthlyAmount,
            IsBureauProductionActive = merchant.IsBureauProductionActive,
            IsMitEnabled = merchant.IsMitEnabled,
            IsMitEvaluateAsync = merchant.MITEvaluateAsync,
            IsCitEvaluateAsync = merchant.CITEvaluateAsync,

            PayoutsEnabled = merchant.PayoutsEnabled,

            IsActive = merchant.Status == ActiveInActive.ACTIVE,
            IsLocked = merchant.Locked,

            Dba = merchant.Dba,
            Mcc = merchant.Mcc,

            CustomerSupportName = merchant.CustomerSupportName,
            CustomerSupportEmail = merchant.CustomerSupportEmail,
            CustomerSupportPhone = merchant.CustomerSupportPhone,
            CustomerSupportLink = merchant.CustomerSupportLink,

            MerchantFeeHistory = mapper.Map<IEnumerable<Contracts.MerchantFee>>(merchant.Fees),

            CompanyName = merchant.CompanyName,
            LegalEntityName = merchant.LegalEntityName,

            SupportedCountries = merchant.GetSupportedCountriesList(),

            Descriptor = merchant.Descriptor,
            Descriptor_Address = merchant.Address?.Line1,
            Descriptor_Country = merchant.Address?.Country,
            Descriptor_City = merchant.Address?.City,
            Descriptor_Phone = merchant.CustomerSupportPhone,
            Descriptor_Postal = merchant.Address?.ZipCode,
            Descriptor_State = merchant.Address?.State,

            PayerEnabled = merchant.PayerEnabled,

            RedactIpEnabled = merchant.RedactIpEnabled,

            AchReceivingAccountType = merchant.DdaType,
            AchReceivingAccountNumber = merchant.AccountNumber,
            AchReceivingRoutingNumber = merchant.RoutingNumber,
            AchBankAccountVerified = merchant.BankAccountVerified,
            AchReceivingAccountCurrency = merchant.Currency,

            TransactionBaseFee = transactionBaseFee != null ? transactionBaseFee.Amount : 0,
            TransactionBaseFeeType = transactionBaseFee != null ? transactionBaseFee.Type.ToString() : null,
            TransactionFee = transactionFee != null ? transactionFee.Amount : 0,
            TransactionFeeType = transactionFee != null ? transactionFee?.Type.ToString() : null,
            ChargebackFee = chargebackFee != null ? chargebackFee.Amount : 0,
            ChargebackFeeType = chargebackFee != null ? chargebackFee.Type.ToString() : null,
            RefundFee = refundFee != null ? refundFee.Amount : 0,
            RefundFeeType = refundFee != null ? refundFee.Type.ToString() : null,
            RiskFee = riskFee != null ? riskFee.Amount : 0,
            RiskFeeType = riskFee != null ? riskFee.Type.ToString() : null,

            VirtualTerminalEnabled = merchant.VirtualTerminalEnabled,
            BillingInformationOptional = merchant.BillingInformationOptional,
            MITGetSiteByDynamicDescriptorEnabled = merchant.MITGetSiteByDynamicDescriptorEnabled,
            MITConsumerNotificationsEnabled = merchant.MITConsumerNotificationsEnabled,
            MITConsumerCuresEnabled = merchant.MITConsumerCuresEnabled,
            UIWidgetOptional = merchant.UIWidgetOptional,
            CITConsumerNotificationsEnabled = merchant.CITConsumerNotificationsEnabled,
            AllowBinCheckOnTokenization = merchant.AllowBinCheckOnTokenization,
            EnableGlobalNetworkTokenization = merchant.EnableGlobalNetworkTokenization,
            CITClickToRefundEnabled = merchant.CITClickToRefundEnabled,
            MITClickToRefundEnabled = merchant.MITClickToRefundEnabled,
            MITAgreedExpiryHours = merchant.MITAgreedExpiryHours,
            UseDefaultSiteForUnknownMerchantUrlsEnabled = merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled,
            IsSenseJsOptional = merchant.IsSenseJsOptional,
            AccountUpdaterEnabled = merchant.AccountUpdaterEnabled,

            MinOrderAmount = merchant.MinOrderAmount,
            MaxOrderAmount = merchant.MaxOrderAmount,

            IsCrawlingEnabled = merchant.IsCrawlingEnabled,
            IsIframeMessagesCollectEnabled = merchant.IsIframeMessagesCollectEnabled,
            IsEnforceMFAEnabled = merchant.IsEnforceMFAEnabled,
            CaptureRequired = merchant.CaptureRequired,
            SchemeTransactionIdEnabled = merchant.SchemeTransactionIdEnabled,
            MITImmediateRetryEnabled = merchant.MITImmediateRetryEnabled,

            RiskLevel = merchant.RiskLevel,
            RiskLevel_Visa = merchant.RiskLevel_Visa,
            RiskTier = merchant.RiskTier,

            IsAvsRequired = merchant.IsAvsRequired,
            IsCvvRequired = merchant.IsCvvRequired,

            Global3DSEnabled = merchant.Global3DSEnabled,
            InformationalOnly3DS = merchant.InformationalOnly3DS,
            ConsumerOrderNotificationChannel = merchant.ConsumerOrderNotificationChannel?.ToString(),

            IgnoreSiteIdFromClient = merchant.IgnoreSiteIdFromClient,

            EligibilityStrategyWorkflowId = merchant.EligibilityStrategyWorkflowId,
            NotEligibleOrderProcessingWorkflowId = merchant.NotEligibleOrderProcessingWorkflowId,
            NotEligibleEverOrderProcessingWorkflowId = merchant.NotEligibleEverOrderProcessingWorkflowId,
            RecyclingStrategyWorkflowId = merchant.RecyclingStrategyWorkflowId,
        };

        return @event;
    }
}