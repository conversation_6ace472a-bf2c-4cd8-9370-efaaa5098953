using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Response;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.DTO.Public;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace FlexCharge.Merchants.Services.ApplicationServices;

public interface IApplicationService
{
    Task<RiskResponseDTO> RiskUpdateStatusAsync(RiskAssessmentRequestDTO request, Guid? partnerId);

    Task<OperationAssessmentResponseDTO> OperationUpdateStatusAsync(OperationAssessmentRequestDTO payload,
        Guid? partnerId);

    Task<ApplicationNewResponse> CreateAndConvertAsync(ApplicationNewRequest payload, bool isStripeAppApplicant);
    Task<ApplicationNewResponse> AdminCreateAsync(AdminApplicationCreateRequest payload);
    Task<ApplicationNewResponse> AdminUpdateAsync(AdminApplicationUpdateRequest payload, Guid? partnerId);
    Task<ApplicationNewResponse> QuickUpsertAsync(ApplicationQuickCreateRequest payload, Guid? partnerId);
    Task<ApplicationResponse> GetQuickByIdAsync(Guid id, Guid? partnerId);
    Task<ApplicationNewResponse> QuickSignupAsync(SignUpRequestDTO payload);
    Task<ApplicationNewResponse> StripeQuickSignupAsync(StripeSignUpRequestDTO payload);

    Task<ApplicationResponse> GetByIdAsync(Guid id);

    Task<ApplicationQueryResponse> AdminGetAsync(string q, DateTime from, DateTime to, ApplicationStatus?[] statuses,
        OperationsStatus?[] operationsStatuses, RiskAssessmentStatus?[] riskStatuses,
        string orderBy, string sortField, int pageSize, int pageNumber, Guid? partnerId);

    Task<ApplicationSummaryResponse> AdminGetSummaryAsync(Guid? pid, Guid? ipid);

    Task<ApplicationQueryResponse> PartnerGetAsync(Guid pid, bool isIntegrationPartner, string q, DateTime from,
        DateTime to, ApplicationStatus?[] statuses,
        OperationsStatus?[] operationsStatuses, RiskAssessmentStatus?[] riskStatuses,
        string orderBy, string sortField, int pageSize, int pageNumber);

    Task<ApplicationCancelResponse> CancelAsync(Guid id, Guid? partnerId);
    Task<ApplicationDeclineResponse> DeclineAsync(Guid id, Guid? partnerId);
    Task<ApplicationConvertResponse> ConvertAsync(Guid id, Guid? partnerId,
        ApplicationConvertOptions convertOptions);
    Task<ApplicationStatusResponse> StatusAsync(Guid id);
    Task<ApplicationReopenResponse> ReopenAsync(Guid id, Guid? partnerId);

    Task<FullApplicationResponse> AdminGetApplicationByIdAsync(Guid Id, Guid? integrationPartnerId);

    //Applicant Functions
    Task<ApplicantGetResponse> ApplicantApplicationGet(Guid id);
    Task<ApplicationUpdateResponse> ApplicantUpdateAsync(Application application, ApplicantBoardingRequestDTO payload);
    Task<ApplicationSubmitResponse> SubmitAsync(Guid userId, Guid id, ApplicationSubmitRequest payload);

    Task StoreDocumentAsync(Guid applicationId, IFormFile file,
        bool sendNotification = true);

    //Global Functions
    Task StoreDocumentAsync(Application application, IFormFile file,
        bool sendNotification = true);

    Task StoreDocumentAsync(Guid applicationId, string fileName, string contentType, Stream stream,
        bool sendNotification = true);

    Task StoreDocumentAsync(Application application, string fileName, string contentType, Stream stream,
        bool sendNotification = true);

    Task<string> StoreLogoImageAsync(Guid id, BaseResponse? response, string logoFileBase64);
    Task<IEnumerable<MccCodesDTO>> MccCodes();

    Task ApplyApplicationActivity(Guid id, ApplicationActivity activity);
    Task UpdateRiskCategoryAsync(Guid id, RiskCategory category, Guid? partnerId);
    Task<string> GetDocumentPublicUrlAsync(Guid documentId);
    Task NotifyAccountManager(Guid applicationId, AccountManagerNotificationRequestDTO payload, Guid? partnerId);
    Task NotifyApplicant(Guid applicationId, ApplicantNotificationRequestDTO payload, Guid? partnerId);

    Task<IList<AplicationActivityDTO>> GetApplicationActivitiesAsync(Guid id, Guid? partnerId);
    Task SendStripeMerchantCreatedNotificationAsync(Guid merchantId);

    Task<ApplicationNewResponse> PublicCreateAsync(Partner partner,
        PublicApplicantCreateDTO payload);

    Task<ApplicationNewResponse> PublicUpdateAsync(Guid id, Guid partnerId, PublicApplicantUpdateDTO payload);

    Task<PublicApplicationQueryResponse> PublicGetAsync(Guid integrationPartnerId, DateTime? from, DateTime? to,
        string orderBy,
        string sortField, int pageSize, int pageNumber);

    Task<PublicApplicationResponse> PublicGetApplicationByIdAsync(Guid id, Guid? integrationPartnerId);
}