using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Response;
using FlexCharge.Merchants.DTO.Public;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO
{
    /// <summary>
    /// 
    /// </summary>
    public class ApplicationQueryResponse : BaseResponse
    {
        public PagedDTO<ApplicationDTO> Results { get; set; }
    }

    public class ApplicationResponse : BaseResponse
    {
        public ApplicationQuickGetResponse Record { get; set; }
    }


    public class FullApplicationResponse : BaseResponse
    {
        public AdminApplicationGetResponse Record { get; set; }
    }

    public class ApplicationGetResponse : BaseResponse
    {
        public ApplicantGetResponse Record { get; set; }
    }


    public class ApplicationDTO
    {
        public Guid Id { get; set; }

        public string CompanyName { get; set; }

        [Required] public string LegalEntityName { get; set; }

        public string LegalEntityCountry { get; set; } //new

        public string EstablishedEcommerceDate { get; set; } //new

        public List<string> Urls { get; set; } //new

        public int UrlsCount { get; set; } //new

        [Required] public string Dba { get; set; }

        public string Type { get; set; }

        public string TaxId { get; set; }

        public string Descriptor { get; set; }

        public string BusinessEstablishedDate { get; set; }

        public DateTime? AgreeToTerms { get; set; }

        public string Mcc { get; set; }

        public string Industry { get; set; }

        public bool Pcidss { get; set; }

        public string EcommercePlatform { get; set; }

        public string Website { get; set; }
        public string LogoUrl { get; set; }

        public string Description { get; set; }

        public string SpecialTerms { get; set; }

        public ApplicationStatus Status { get; set; }
        public string StatusName => Enum.GetName(typeof(ApplicationStatus), Status);

        public string RiskStatus { get; set; }
        public string OperationsStatus { get; set; }

        public MerchantIntegrationTypes IntegrationType { get; set; }
        public string IntegrationTypeName => Enum.GetName(typeof(MerchantIntegrationTypes), IntegrationType);

        public DateTime CreatedOn { get; set; }

        public DateTime ModifiedOn { get; set; }

        public AddressDTO Address { get; set; }

        public PrimaryContact PrimaryContact { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public PartnerQueryDTO Partner { get; set; }

        public PartnerQueryDTO IntegrationPartner { get; set; }
        public SalesAgencyQueryDTO SalesAgency { get; set; }
        public BankAccountInformationDTO BankAccountInformation { get; set; }

        public ApplicationCustomerSupportInformationDTO CustomerSupportInformation { get; set; }

        public bool StandardFeeSelected { get; set; }

        public IEnumerable<ApplicationFeeQueryDTO> Fees { get; set; }

        public bool IsSiteValidated { get; set; }

        public Guid? AccountId { get; set; }

        public List<Owner>? Owners { get; set; }//TODO OwnerDto

        public string? ProductsSold { get; set; }

        public List<SiteCandidate>? Sites { get; set; }//TODO SiteCandidate DTO

        public List<DocumentDTO> Documents { get; set; }
    }

    public class ApplicationFeeQueryDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public FEE_TYPE Type { get; set; }
        public CHARGE_TYPE ChargeType { get; set; }
        public int Amount { get; set; }
        public int? MinimumFeeAmount { get; set; }
        public bool IsStandard { get; set; }
    }

    // Documents DTO
    public class DocumentDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Path { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public DateTime CreatedOn { get; set; }
    }
}