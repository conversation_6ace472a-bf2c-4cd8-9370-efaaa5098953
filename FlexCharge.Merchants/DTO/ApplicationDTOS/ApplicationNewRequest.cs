using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using FlexCharge.Merchants.Enums;
using FluentValidation;

namespace FlexCharge.Merchants.DTO
{
    public class ApplicationNewBaseDTO : ApplicationBaseDTO
    {
        public Guid? Id { get; set; }
        public PrimaryContact PrimaryContact { get; set; }
        public Guid? AccountManagerId { get; set; }
        public bool StandardFeeSelected { get; set; }
        public List<ApplicationFeeDTO> Fees { get; set; }
        public Guid? AccountId { get; set; }
        [Required] public string Dba { get; set; }
        [Required] public Guid PartnerId { get; set; }
        [Required] public string IntegrationTier { get; set; }
        public bool? ContractAlreadySigned { get; set; }
        public bool? IntegrationPartnerParticipateSale { get; set; }
        public string CrmId { get; set; }
    }

    public class ApplicationQuickCreateRequest : ApplicationNewBaseDTO
    {
        [Required] public bool SaveAndSend { get; set; } = false;
        public Guid? IntegrationPartnerId { get; set; }
        public string SpecialTerms { get; set; }
    }

    public class ApplicationQuickGetResponse : ApplicationNewBaseDTO
    {
        public ApplicationStatus Status { get; set; }
        public string StatusName { get; set; }
        public List<ApplicationFeeQueryDTO> Fees { get; set; }
    }

    public class AdminApplicationCreateRequest : ApplicationNewBaseDTO
    {
        public bool SaveAndSend { get; set; } = false;
        public string TaxId { get; set; }
        public string Descriptor { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public ApplicationCustomerSupportInformationDTO CustomerSupportInformation { get; set; }
        public BusinessModelDTO BusinessModel { get; set; }
        public BankAccountInformationDTO BankAccountInformation { get; set; }
        public TransactionInformationDTO TransactionInformation { get; set; }
        public AddressDTO Address { get; set; }
        public CustomFields CustomFields { get; set; }
        public ProductInfoDTO ProductInfo { get; set; }
        public List<OwnerDTO>? Owners { get; set; }
        public bool Send { get; set; } = false;
        public bool? ContractAlreadySigned { get; set; }
        public bool? IntegrationPartnerParticipateSale { get; set; }
        public int AnnualSalesVolume { get; set; }
        public string CrmId { get; set; }
    }

    public class AdminApplicationUpdateRequest : ApplicationNewBaseDTO
    {
        public Guid Id { get; set; }
        [Required] public PrimaryContact PrimaryContact { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public ApplicationCustomerSupportInformationDTO? CustomerSupportInformation { get; set; }
        public BusinessModelDTO BusinessModel { get; set; }
        public BankAccountInformationDTO BankAccountInformation { get; set; }
        public TransactionInformationDTO TransactionInformation { get; set; }
        public AddressDTO Address { get; set; }
        public CustomFields CustomFields { get; set; }
        public ProductInfoDTO ProductInfo { get; set; }
        public List<OwnerDTO>? Owners { get; set; }
        public bool Send { get; set; } = false;
        public bool? ContractAlreadySigned { get; set; }
        public bool? IntegrationPartnerParticipateSale { get; set; }
        
        public int AnnualSalesVolume { get; set; }
        public string CrmId { get; set; }
    }

    public class AdminApplicationGetResponse : ApplicationNewBaseDTO
    {
        public DeveloperContact DeveloperContact { get; set; }
        public ApplicationCustomerSupportInformationDTO CustomerSupportInformation { get; set; }
        public BusinessModelDTO BusinessModel { get; set; }
        public BankAccountInformationDTO BankAccountInformation { get; set; }
        public TransactionInformationDTO TransactionInformation { get; set; }
        public AddressDTO Address { get; set; }
        public CustomFields CustomFields { get; set; }
        public ProductInfoDTO ProductInfo { get; set; }
        public List<OwnerDTO>? Owners { get; set; }
        public bool Send { get; set; } = false;
        public bool? ContractAlreadySigned { get; set; }
        public ApplicationStatus Status { get; set; }
        public string StatusName { get; set; }
        public List<DocumentDTO> UploadedDocuments { get; set; }
        public string LogoUrl { get; set; }
        public bool? IntegrationPartnerParticipateSale { get; set; }
        public string CrmId { get; set; }
    }

    public class ApplicantGetResponse : ApplicantBoardingRequestDTO
    {
        public Guid Id { get; set; }
        public List<DocumentDTO> UploadedDocuments { get; set; }
        public ApplicationStatus Status { get; set; }
        public string StatusName { get; set; }
        public List<ApplicationFeeDTO> Fees { get; set; }
        public string SpecialTerms { get; set; }
        public bool IsSiteValidated { get; set; }
        public bool? ContractAlreadySigned { get; set; }
        public bool? IntegrationPartnerParticipateSale { get; set; }

        public string TermsAndConditionsUrl { get; set; }
        public string MerchantTermsAndConditionsUrl { get; set; }
        public string TermsAndConditions { get; set; }
        public string ConsentLabel { get; set; }
        public string CrmId { get; set; }
    }

    public class ApplicationNewRequest : ApplicationNewBaseDTO
    {
        public bool Send { get; set; } = false;
        public TransactionInformationDTO TransactionInformation { get; set; }
        public AddressDTO Address { get; set; }
        [Required] public ApplicationCustomerSupportInformationDTO CustomerSupportInformation { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public BankAccountInformationDTO BankAccountInformation { get; set; }
        public CustomFields CustomFields { get; set; }
        public bool IsSiteValidated { get; set; } = false;
        public List<OwnerDTO>? Owners { get; set; }
        public List<string>? Urls { get; set; }
        public string? Password { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
    }

    public class CustomFields
    {
        public string Value { get; set; }
        public string Key { get; set; }
    }
    
    
}