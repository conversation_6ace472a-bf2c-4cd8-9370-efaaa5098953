using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO;

public class ApplicationBaseDTO
{
    [Required]
    [StringLength(50,
        ErrorMessage = "Legal entity must be between 3 and 50 character in length.")]
    [DisplayName("Legal entity name")]
    public string LegalEntityName { get; set; }

    [StringLength(50,
        ErrorMessage = "Dba must be between 3 and 50 character in length.")]
    public string Dba { get; set; }
    
    public string LegalEntityCountry { get; set; }
    
    // public string TimeInBusiness { get; set; }

    public string TaxId { get; set; }

    public string Mcc { get; set; }
    public Guid? PartnerId { get; set; }
    public Guid? IntegrationPartnerId { get; set; }

    // [Url(ErrorMessage = "Website must be a valid URL.")]
    // public string? Website { get; set; }

    public bool IsSiteValidated { get; set; }
    public string RiskCategory { get; set; }
    public string RiskStatus { get; set; }
    public string OperationsStatus { get; set; }

    public string Descriptor { get; set; }

    public Guid? SalesAgencyId { get; set; }
    public string ExternalId { get; set; }


    public string EcommercePlatform { get; set; }
    // public string EcommercePlatformUsagePeriod { get; set; }

    public string Type { get; set; }
    public string BusinessEstablishedDate { get; set; }


    // public string Industry { get; set; }
    public bool Pcidss { get; set; }

    public string IntegrationTier { get; set; }


    [Required] public ApplicationStatus Status { get; set; }

    [Required]
    [DisplayName("Integration type")]
    public MerchantIntegrationTypes IntegrationType { get; set; }

    public string Description { get; set; }
    public string SpecialTerms { get; set; }

    public string LogoFile { get; set; }
    //public string RiskStatus { get; set; }
    public int AnnualSalesVolume { get; set; }
}