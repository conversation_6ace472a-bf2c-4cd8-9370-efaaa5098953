using System;
using System.Linq;
using FluentValidation;

namespace FlexCharge.Merchants.DTO;

public class ValorPayTechApplicantBoardingRequestValidator : AbstractValidator<ApplicantBoardingRequestDTO>
{
    public ValorPayTechApplicantBoardingRequestValidator()
    {
        RuleFor(x => x.Next)
            .Must(value => value == true || value == false)
            .WithMessage("Next must be a valid boolean.");
        
        RuleFor(x => x.CompanyInformation)
            .SetValidator(new LegalEntityDTOValidator())
            .When(x => x.CompanyInformation != null);
        
        RuleFor(x => x.PrimaryContact)
            .SetValidator(new PrimaryContactValidator())
            .When(x => x.PrimaryContact != null);
        
        RuleFor(x => x.DeveloperContact)
            .SetValidator(new DeveloperContactValidator())
            .When(x => x.DeveloperContact != null);

        RuleFor(x => x.CustomerSupportContact)
            .SetValidator(new ApplicationCustomerSupportInformationDTOValidator())
            .When(x => x.CustomerSupportContact != null);
        
        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerDTOValidator())
            .When(x => x.Owners != null);
        
        RuleFor(x => x.ProductInfo)
            .NotNull().WithMessage("ProductInfo is required.")
            .SetValidator(new ProductInfoDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.BusinessModel)
            .NotNull().WithMessage("BusinessModel is required.")
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.TransactionInformation)
            .NotNull().WithMessage("TransactionInformation is required.")
            .SetValidator(new TransactionInformationDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.Address)
            .SetValidator(new AddressDTOValidator())
            .When(x => x.Address != null);
        
        RuleFor(x => x.BankAccountInformation)
            .SetValidator(new BankAccountInformationDTOValidator())
            .When(x => x.BankAccountInformation != null);
    
        RuleFor(x => x.Documents)
            .Must(documents => documents == null || documents.Any()).WithMessage("If provided, at least one document is required.")
            .ForEach(document => document.SetValidator(new DocumentDTOValidator()))
            .When(x => x.Documents != null);
    }
}