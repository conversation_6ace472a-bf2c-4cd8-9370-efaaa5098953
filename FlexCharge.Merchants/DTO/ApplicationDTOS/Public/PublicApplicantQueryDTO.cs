using System;
using System.Collections.Generic;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Response;

namespace FlexCharge.Merchants.DTO.Public;

public class PublicApplicantQueryDTO : PublicApplicationBaseDTO
{
    public Guid Id { get; set; }

    public DateTime CreatedOn { get; set; }
    public DateTime? UpdatedOn { get; set; }

    public string Status { get; set; }
}

public class PublicApplicationQueryResponse : BaseResponse
{
    public PagedDTO<PublicApplicantQueryDTO> Results { get; set; }
    public List<HateoasLink> Links { get; set; } = new List<HateoasLink>();
}

public class PublicApplicationResponse : BaseResponse
{
    public PublicApplicantQueryDTO Record { get; set; }
}
