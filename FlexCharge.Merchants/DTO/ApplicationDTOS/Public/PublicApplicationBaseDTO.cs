using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FluentValidation;

namespace FlexCharge.Merchants.DTO.Public;

public class PublicApplicationBaseDTO
{
    [StringLength(50, ErrorMessage = "Legal entity must be between 3 and 50 character in length.")]
    [DisplayName("Legal entity name")]
    public string LegalEntityName { get; set; }

    [StringLength(50, ErrorMessage = "Dba must be between 3 and 50 character in length.")]
    public string Dba { get; set; }

    public string TaxId { get; set; }

    public string Mcc { get; set; }

    public Guid? PartnerId { get; set; }
    public Guid? IntegrationPartnerId { get; set; }

    public string ProcessingType { get; set; }
    public string Descriptor { get; set; }

    public PrimaryContact PrimaryContact { get; set; }

    public ApplicationCustomerSupportInformationDTO CustomerSupportInformation { get; set; }

    public BankAccountInformationDTO BankAccountInformation { get; set; }

    public AddressDTO Address { get; set; }

    public List<OwnerDTO> Owners { get; set; }
    // public string Website { get; set; }
    public BusinessModelDTO BusinessModel { get; set; }
}

public class ProcessingVolume
{
    public int AverageTransactionAmountCard { get; set; }

    public int AverageTransactionAmountDebit { get; set; }

    public string Currency { get; set; }
    public int HighTicketAmount { get; set; }
    public int MonthlyProcessingVolume { get; set; }
    public int MonthlyTransactionsCount { get; set; }
}