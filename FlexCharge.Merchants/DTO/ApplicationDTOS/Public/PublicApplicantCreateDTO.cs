using System;
using System.Globalization;
using System.Linq;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Merchants.Entities;
using FluentValidation;

namespace FlexCharge.Merchants.DTO.Public;

public class PublicApplicantCreateDTO : PublicApplicationBaseDTO
{
}

public class PublicApplicantUpdateDTO : PublicApplicationBaseDTO
{
    public Guid Id { get; set; }
}

public class PublicGetApplicationsDTO
{
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public string OrderBy { get; set; } = "CreatedOn";
    public string SortField { get; set; } = "desc";
    public int PageSize { get; set; } = 10;
    public int PageNumber { get; set; } = 1;
}

public class PublicApplicantCreateValidator : AbstractValidator<PublicApplicantCreateDTO>
{
    public PublicApplicantCreateValidator()
    {
        RuleFor(x => x.PartnerId)
            .NotEmpty().WithMessage("PartnerId is required.");

        RuleFor(x => x.ProcessingType)
            .NotEmpty().WithMessage("ProcessingType is required.")
            .Must(pt => pt == "CP" || pt == "ECOM" || pt == "ALL")
            .WithMessage("ProcessingType must be one of CP, ECOM, ALL.");

        RuleFor(x => x.LegalEntityName)
            .NotEmpty().WithMessage("LegalEntityName is required.");

        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");

        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.TaxId)
            .NotEmpty().WithMessage("TaxId is required.");

        RuleFor(x => x.Descriptor)
            .NotEmpty().WithMessage("Descriptor is required.");

        RuleFor(x => x.CustomerSupportInformation)
            .SetValidator(new CustomerSupportInformationValidator())
            .When(x => x.CustomerSupportInformation != null);

        // For ProcessingType ECOM, additional customer support details are required.
        When(x => x.ProcessingType == "ECOM", () =>
        {
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportLink)
                .NotEmpty().WithMessage("SupportLink is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportEmail)
                .NotEmpty().EmailAddress().WithMessage("A valid SupportEmail is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportName)
                .NotEmpty().WithMessage("SupportName is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportPhone)
                .NotEmpty().WithMessage("SupportPhone is required for ECOM processing.");
        });

        RuleFor(x => x.BankAccountInformation)
            .NotNull().WithMessage("BankAccountInformation is required.")
            .SetValidator(new BankAccountInformationValidator());

        RuleFor(x => x.Address)
            .NotNull().WithMessage("Address is required.")
            .SetValidator(new AddressValidator());

        // Validate each owner if OwnersInformation is provided.
        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerValidator())
            .When(x => x.Owners != null);

        RuleFor(x => x.Owners)
            .Custom((owners, context) =>
            {
                if (owners == null || owners.Count == 0) return;

                var totalOwnership =
                    owners.Aggregate<OwnerDTO, decimal>(0, (current, owner) => current + owner.PercentOwnership);

                if (totalOwnership != 100)
                    context.AddFailure("OwnersInformation", "The total ownership percentage must equal 100.");
            });

        RuleFor(x => x.BusinessModel)
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.BusinessModel != null);
        
        // RuleFor(x => x.Website)
        //     .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
        //     .WithMessage("WebsiteUrl must be a valid URL.")
        //     .When(x => !string.IsNullOrEmpty(x.Website));

        // Optionally, add validators for BusinessModel, ProductInformation, and VolumeEstimates if needed.
    }
}

public class PrimaryContactValidator : AbstractValidator<PrimaryContact>
{
    public PrimaryContactValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("FirstName is required.");
        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("LastName is required.");
        RuleFor(x => x.Email)
            .NotEmpty().EmailAddress().WithMessage("A valid Email is required.");
        RuleFor(x => x.Phone)
            .NotEmpty().WithMessage("Phone is required.");
    }
}

public class BankAccountInformationValidator : AbstractValidator<BankAccountInformationDTO>
{
    public BankAccountInformationValidator()
    {
        RuleFor(x => x.BankName)
            .NotEmpty().WithMessage("BankName is required.");
        RuleFor(x => x.AccountNumber)
            .NotEmpty().WithMessage("AccountNumber is required.");
        RuleFor(x => x.RoutingNumber)
            .NotEmpty().WithMessage("RoutingNumber is required.");
        RuleFor(x => x.DdaType)
            .NotEmpty().WithMessage("AccountType is required.");
    }
}

public class CustomerSupportInformationValidator : AbstractValidator<ApplicationCustomerSupportInformationDTO>
{
    public CustomerSupportInformationValidator()
    {
        RuleFor(x => x.CustomerSupportLink)
            .NotEmpty().WithMessage("CustomerSupportLink is required.");
        RuleFor(x => x.CustomerSupportEmail)
            .NotEmpty().EmailAddress().WithMessage("A valid CustomerSupportEmail is required.");
        RuleFor(x => x.CustomerSupportName)
            .NotEmpty().WithMessage("CustomerSupportName is required.");
        RuleFor(x => x.CustomerSupportPhone)
            .NotEmpty().WithMessage("CustomerSupportPhone is required.");
    }
}

public class AddressValidator : AbstractValidator<AddressDTO>
{
    public AddressValidator()
    {
        RuleFor(x => x.Line1)
            .NotEmpty().WithMessage("AddressLine1 is required.");
        // AddressLine2 is optional.
        RuleFor(x => x.City)
            .NotEmpty().WithMessage("City is required.");
        RuleFor(x => x.ZipCode)
            .NotEmpty().WithMessage("Zipcode is required.");
        RuleFor(x => x.Country)
            .NotEmpty().WithMessage("Country is required.");
        RuleFor(x => x.State)
            .NotEmpty().WithMessage("State is required.");
    }
}

public class OwnerValidator : AbstractValidator<OwnerDTO>
{
    public OwnerValidator()
    {
        // Optional fields may be empty but if provided, they should be valid.
        RuleFor(x => x.DateOfBirth)
            .NotEmpty().NotNull()
            .WithMessage("DateOfBirth must be in YYYY-MM-DD format.");

        RuleFor(x => x.PercentOwnership)
            .NotEmpty().WithMessage("PercentOwnership is required.")
            .InclusiveBetween(0, 100).WithMessage("PercentOwnership must be between 0 and 100.");

        RuleFor(x => x.Email)
            .EmailAddress().When(x => !string.IsNullOrEmpty(x.Email))
            .WithMessage("Invalid Email format.");

        When(x => x.Address != null, () =>
        {
            RuleFor(x => x.Address)
                .SetValidator(new AddressValidator());
        });
    }
}

public class PublicApplicantUpdateValidator : AbstractValidator<PublicApplicantUpdateDTO>
{
    public PublicApplicantUpdateValidator()
    {
        RuleFor(x => x.PartnerId)
            .NotEmpty().WithMessage("PartnerId is required.");

        RuleFor(x => x.ProcessingType)
            .NotEmpty().WithMessage("ProcessingType is required.")
            .Must(pt => pt == "CP" || pt == "ECOM" || pt == "ALL")
            .WithMessage("ProcessingType must be one of CP, ECOM, ALL.");

        RuleFor(x => x.LegalEntityName)
            .NotEmpty().WithMessage("LegalEntityName is required.");

        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");

        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.TaxId)
            .NotEmpty().WithMessage("TaxId is required.");

        RuleFor(x => x.Descriptor)
            .NotEmpty().WithMessage("Descriptor is required.");

        RuleFor(x => x.CustomerSupportInformation)
            .SetValidator(new CustomerSupportInformationValidator())
            .When(x => x.CustomerSupportInformation != null);

        // For ProcessingType ECOM, additional customer support details are required.
        When(x => x.ProcessingType == "ECOM", () =>
        {
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportLink)
                .NotEmpty().WithMessage("SupportLink is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportEmail)
                .NotEmpty().EmailAddress().WithMessage("A valid SupportEmail is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportName)
                .NotEmpty().WithMessage("SupportName is required for ECOM processing.");
            RuleFor(x => x.CustomerSupportInformation.CustomerSupportPhone)
                .NotEmpty().WithMessage("SupportPhone is required for ECOM processing.");
        });

        RuleFor(x => x.BankAccountInformation)
            .NotNull().WithMessage("BankAccountInformation is required.")
            .SetValidator(new BankAccountInformationValidator());

        RuleFor(x => x.Address)
            .NotNull().WithMessage("Address is required.")
            .SetValidator(new AddressValidator());

        // Validate each owner if OwnersInformation is provided.
        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerValidator())
            .When(x => x.Owners != null);

        RuleFor(x => x.Owners)
            .Custom((owners, context) =>
            {
                if (owners == null || owners.Count == 0) return;

                var totalOwnership =
                    owners.Aggregate<OwnerDTO, decimal>(0, (current, owner) => current + owner.PercentOwnership);

                if (totalOwnership != 100)
                    context.AddFailure("OwnersInformation", "The total ownership percentage must equal 100.");
            });
        
        // RuleFor(x => x.Website)
        //     .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
        //     .WithMessage("WebsiteUrl must be a valid URL.")
        //     .When(x => !string.IsNullOrEmpty(x.Website));
        
        RuleFor(x => x.BusinessModel)
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.BusinessModel != null);
    }
}

public class PublicGetApplicationsValidator : AbstractValidator<PublicGetApplicationsDTO>
{
    public PublicGetApplicationsValidator()
    {
        RuleFor(x => x.PageSize)
            .GreaterThan(0).WithMessage("PageSize must be greater than 0.")
            .LessThanOrEqualTo(100).WithMessage("PageSize must not exceed 100.");

        RuleFor(x => x.PageNumber)
            .GreaterThan(0).WithMessage("PageNumber must be greater than 0.");

        RuleFor(x => x.From)
            .LessThanOrEqualTo(x => x.To.GetValueOrDefault(DateTime.MaxValue))
            .WithMessage("From date must be earlier than or equal to the To date.")
            .When(x => x.From.HasValue && x.To.HasValue);

        RuleFor(x => x.OrderBy)
            .NotEmpty().WithMessage("OrderBy cannot be empty.");

        RuleFor(x => x.SortField)
            .Must(value => value == "asc" || value == "desc")
            .WithMessage("SortField must be either 'asc' or 'desc'.");
    }
}

public class BusinessModelDTOValidator : AbstractValidator<BusinessModelDTO>
{
    public BusinessModelDTOValidator()
    {
        RuleFor(x => x.Urls)
            .Must(urls => urls == null || urls.All(url => Uri.IsWellFormedUriString(url, UriKind.Absolute)))
            .WithMessage("All URLs must be valid.")
            .When(x => x.Urls != null);
    }
}

public class ValorPayTechInviteRequestValidator : AbstractValidator<PublicApplicantCreateDTO>
{
    public ValorPayTechInviteRequestValidator()
    {
        
        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.LegalEntityName)
                    .NotEmpty().WithMessage("LegalEntityName is required.");
        
        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");
    }
}

public class ValorPayTechUpdateRequestValidator : AbstractValidator<PublicApplicantUpdateDTO>
{
    public ValorPayTechUpdateRequestValidator()
    {
        
        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.LegalEntityName)
            .NotEmpty().WithMessage("LegalEntityName is required.");
    }
}