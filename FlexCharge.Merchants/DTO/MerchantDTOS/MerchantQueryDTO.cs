using System;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO;

public class MerchantQueryDTO
{
    public string Q { get; set; }
    public ActiveInActive? Status { get; set; }
    public Guid? Mid { get; set; }
    public Guid? Pid { get; set; }
    public Guid? IntegrationPartnerId { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}