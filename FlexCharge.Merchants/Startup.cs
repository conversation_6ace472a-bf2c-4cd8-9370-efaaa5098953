using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Diagnostics;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.Cloud.Storage.Amazon;
using FlexCharge.Common.Emails;
using FlexCharge.Merchants.Services.ApplicationServices;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Merchants.Services.NotificationsServices;
using FlexCharge.Utils;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Contracts.Commands;
using FlexCharge.Merchants.Enums;
using FlexCharge.Merchants.Services;
using FlexCharge.Merchants.Services.MerchantServices;
using FlexCharge.Merchants.Services.SalesAgencyServices;
using FlexCharge.Orders.Services;
using GoogleReCaptcha.V3;
using GoogleReCaptcha.V3.Interface;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace FlexCharge.Merchants
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddHttpClient();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<IApplicationService, ApplicationService>();
            services.AddTransient<IMerchantService, MerchantService>();
            services.AddTransient<ISalesService, SalesService>();
            services.AddTransient<IEmailSender, SendGridEmailSender>();
            services.AddTransient<INotificationsService, NotificationsService>();
            services.AddTransient<IGeoServices, GeoServices>();
            services.AddTransient<ISecurityCheckService, SecurityCheckService>();
            services.AddSingleton<IUrlShortenerService, UrlShortenerService>();

            services.AddTransient<ICaptchaValidator, GoogleReCaptchaValidator>();
            services.AddTransient<IRecaptchaService, RecaptchaService>();

            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));
            
            services.AddTransient<LinkGeneratorService>();

            services.Configure<ApiBehaviorOptions>(options => { options.SuppressModelStateInvalidFilter = true; });

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.Configure<LogoImageValidationOptions>(Configuration.GetSection("io:logoImageValidation"));
            services.Configure<DocumentUploadOptions>(Configuration.GetSection("documentUpload"));
            services.AddOptions();

            services.AddAmazonS3Storage();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString =
                "Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(
                    connectionString,
                    npgsqlOptionsAction: options => options
                        .MapEnum<ActiveInActive>()
                        .MapEnum<ApplicationStatus>());
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****");

            //Connection to JS server database to get Sense JS data (SenseKey)
            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<SenseJsExternalPostgreSQLDbContext>(connectionString);
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****");

            services.AddRedisCache();

            services.AddMassTransit<Startup>(config =>
            {
                config.AddRequestClient<GetPublicTokenizationKeyCommand>();
                config.AddRequestClient<VerifyPaymentCommand>();
                config.AddRequestClient<CreateSftpUserCommand>();
                config.AddRequestClient<GetSftpUserCommand>();
            });

            services.AddJwt();
            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.MERCHANT_ADMINS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });

                    options.AddPolicy(MyPolicies.PARTNER_ADMINS_ONLY,
                        policy => { policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.PARTNER_ADMIN); });

                    options.AddPolicy(MyPolicies.INTEGRATION_PARTNER_ADMINS_ONLY,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);
                        });

                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.SALESAGENCY_ADMINS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SALESAGENCY_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS_AND_SALESAGENCY_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.SALESAGENCY_ADMIN);
                        });

                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });

                    options.AddPolicy(MyPolicies.ALL_MERCHANTS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                }
            );

            services.AddAutoMapper(typeof(Startup));

            services.AddControllers().AddNewtonsoftJson(x =>
                x.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore);

            //Added to fix circular reference errors (somehow configuration above was not enough)
            Newtonsoft.Json.JsonConvert.DefaultSettings = () => new JsonSerializerSettings()
                {ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore};

            services.AddSwaggerDocs();

            services.AddEmailClient();

            services.AddBigPayloadSupport();

            #region Registering Shared Flex Services

            services.AddSharedPartnerSettings();

            #endregion

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader());
            });

            services.AddAmazonQuickSight();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();

                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            app.UseSwaggerDocs();
            //app.UseHttpsRedirection();
            app.UseAutoMigrations<PostgreSQLDbContext>();

            // app.UseMiddleware<ErrorHandlerMiddleware>();

            app.UseRouting();

            // app.UseFlexGrpc<MerchantGetterServer>();
            // app.UseEndpoints(endpoints =>
            // {
            //    // endpoints.MapGet("/", async context => { await context.Response.WriteAsync("Hello World!"); });
            //     endpoints.MapGrpcService<MerchantGetterServer>();
            //     endpoints.MapGrpcReflectionService();
            // });

            app.UseAuthorization();
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseMassTransit();
        }
    }
}