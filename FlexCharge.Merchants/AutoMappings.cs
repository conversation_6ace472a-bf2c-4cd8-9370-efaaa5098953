using AutoMapper;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Merchants.Enums;
using FlexCharge.Utils;
using SixLabors.ImageSharp.ColorSpaces.Companding;
using System.Text.Json;
using FlexCharge.Merchants.DTO.Public;

namespace FlexCharge.Merchants
{
    public class AutoMappings : Profile
    {
        public AutoMappings()
        {
            CreateMap<Merchant, MerchantRequest>();
            CreateMap<MerchantRequest, Merchant>();

            CreateMap<MerchantUpdateRequest, Merchant>()
                .ForMember(dest => dest.Fees, opt => opt.Ignore())
                .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
                .ForMember(dest => dest.AccountNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.AccountNumber))
                .ForMember(dest => dest.RoutingNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.RoutingNumber))
                .ForMember(dest => dest.DdaType, opt => opt.MapFrom(src => src.BankAccountInformation.DdaType))
                .ForMember(dest => dest.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportName))
                .ForMember(dest => dest.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportEmail))
                .ForMember(dest => dest.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportPhone))
                .ForMember(dest => dest.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportLink))
                .ForPath(dest => dest.DeveloperContact.FirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperFirstName))
                .ForPath(dest => dest.DeveloperContact.LastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperLastName))
                .ForPath(dest => dest.DeveloperContact.Phone,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperPhone))
                .ForPath(dest => dest.DeveloperContact.Email,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperEmail))
                .ForPath(dest => dest.Address.Line1,
                    opt => opt.MapFrom(src => src.Address.Addressline1))
                .ForPath(dest => dest.Address.Line2,
                    opt => opt.MapFrom(src => src.Address.Addressline2))
                .ForPath(dest => dest.BusinessEstablishedDate,
                    opt => opt.MapFrom(src => src.BusinessEstablishedDate))
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));

            CreateMap<MerchantPrimaryContactUpdate, Contact>();
            CreateMap<MerchantAddressDTO, Address>();
            CreateMap<MerchantFeeDTO, MerchantFee>();

            // CreateMap<Site, SiteDTO>().ReverseMap();
            // CreateMap<SiteWhitelistedUrl, Uri>().ReverseMap()
            //     .ForMember(dest => dest.Link,
            //         opt => opt.MapFrom(src => src));

            CreateMap<MerchantFee, Contracts.MerchantFee>();
            CreateMap<ApplicationFee, Contracts.MerchantFee>();

            CreateMap<MerchantFee, FeeConfigurationResponse>()
                .ForMember(dest => dest.Type, opt => opt.Ignore())
                .ForMember(dest => dest.ChargeType, opt => opt.Ignore());
            // CreateMap<Merchant,BankAccountInformationDTO>();
            CreateMap<FeeConfigurationResponse, MerchantFee>()
                .ForMember(dest => dest.Name, opt => opt.Ignore());

            CreateMap<MerchantFundsReserveConfiguration, MerchantFundsReserveConfigurationDTO>()
                .ForMember(
                    dest => dest.Id,
                    opt => opt.MapFrom(src => src.Id))
                .ForMember(
                    dest => dest.Name,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.Name))
                .ForMember(
                    dest => dest.DisputeRateMin,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.DisputeRateMin))
                .ForMember(
                    dest => dest.DisputeRateMax,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.DisputeRateMax))
                .ForMember(
                    dest => dest.Type,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.Type))
                .ForMember(
                    dest => dest.Period,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.Period))
                .ForMember(
                    dest => dest.ReserveRate,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.ReserveRate))
                .ForMember(
                    dest => dest.ReserveRateType,
                    opt => opt.MapFrom(src => src.FundsReserveConfiguration.ReserveRateType)).ReverseMap();

            CreateMap<Merchant, MerchantResponse>()
                .ForMember(dest => dest.MinOrderAmount,
                    opt => opt.MapFrom(src => Formatters.IntToDecimal(src.MinOrderAmount)))
                .ForMember(dest => dest.MaxOrderAmount,
                    opt => opt.MapFrom(src => Formatters.IntToDecimal(src.MaxOrderAmount)))
                .ForPath(dest => dest.BankAccountInformation.MaskedAccountNumber,
                    opt => opt.MapFrom(src =>
                        src.AccountNumber.Length >= 4
                            ? src.AccountNumber.Mask(0, src.AccountNumber.Length - 4, 'x')
                            : src.AccountNumber))
                .ForPath(dest => dest.BankAccountInformation.MaskedRoutingNumber,
                    opt => opt.MapFrom(src =>
                        src.RoutingNumber.Length >= 4
                            ? src.RoutingNumber.Mask(0, src.RoutingNumber.Length - 4, 'x')
                            : src.RoutingNumber))
                .ForPath(dest => dest.SupportedCountries,
                    opt => opt.MapFrom(src =>
                        src.GetSupportedCountriesList()))
                .ForPath(dest => dest.IsMITEvaluateAsync,
                    opt => opt.MapFrom(src =>
                        src.MITEvaluateAsync))
                .ForPath(dest => dest.IsCITEvaluateAsync,
                    opt => opt.MapFrom(src =>
                        src.CITEvaluateAsync))
                .ForPath(dest => dest.BankAccountInformation.DdaType,
                    opt => opt.MapFrom(src => src.DdaType))
                .ForPath(dest => dest.BankAccountInformation.BankName,
                    opt => opt.MapFrom(src => src.BankName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportEmail))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportPhone))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportLink))
                .ForPath(dest => dest.DeveloperContact.DeveloperFirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.FirstName))
                .ForPath(dest => dest.DeveloperContact.DeveloperLastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.LastName))
                .ForPath(dest => dest.DeveloperContact.DeveloperPhone,
                    opt => opt.MapFrom(src => src.DeveloperContact.Phone))
                .ForPath(dest => dest.DeveloperContact.DeveloperEmail,
                    opt => opt.MapFrom(src => src.DeveloperContact.Email))
                .ForPath(dest => dest.ConsumerOrderNotificationChannel,
                    opt => opt.MapFrom(src => src.ConsumerOrderNotificationChannel.ToString()));

            CreateMap<Merchant, MerchantPublicResponse>()
                .ForPath(dest => dest.BankAccountInformation.MaskedAccountNumber,
                    opt => opt.MapFrom(src =>
                        src.AccountNumber.Length >= 4
                            ? src.AccountNumber.Mask(0, src.AccountNumber.Length - 4, 'x')
                            : src.AccountNumber))
                .ForPath(dest => dest.BankAccountInformation.MaskedRoutingNumber,
                    opt => opt.MapFrom(src =>
                        src.RoutingNumber.Length >= 4
                            ? src.RoutingNumber.Mask(0, src.RoutingNumber.Length - 4, 'x')
                            : src.RoutingNumber))
                .ForPath(dest => dest.AccountId,
                    opt => opt.MapFrom(src => src.Account.Id))
                .ForPath(dest => dest.BankAccountInformation.DdaType,
                    opt => opt.MapFrom(src => src.DdaType))
                .ForPath(dest => dest.BankAccountInformation.BankName,
                    opt => opt.MapFrom(src => src.BankName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportEmail))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportPhone))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportLink))
                .ForPath(dest => dest.DeveloperContact.DeveloperFirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.FirstName))
                .ForPath(dest => dest.DeveloperContact.DeveloperLastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.LastName))
                .ForPath(dest => dest.DeveloperContact.DeveloperPhone,
                    opt => opt.MapFrom(src => src.DeveloperContact.Phone))
                .ForPath(dest => dest.DeveloperContact.DeveloperEmail,
                    opt => opt.MapFrom(src => src.DeveloperContact.Email));


            CreateMap<Application, Merchant>()
                // .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Sites, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => ActiveInActive.ACTIVE));

            CreateMap<ApplicationFee, MerchantFee>();

            // CreateMap<SelfBordingApplicationRequestDTO, Application>()
            //
            //
            //     .ForPath(dest => dest.Fees.FirstOrDefault().ApplicationId,
            //         opt => opt.MapFrom(src => src.Id))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().Name,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().Name))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().Amount,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().Amount))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().ChargeType,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().ChargeType))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().IsStandard,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().IsStandardFee))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().MinimumFeeAmount,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().MinimumFeeAmount))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().Type,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().Type))
            //     .ForPath(dest => dest.Fees.FirstOrDefault().IsStandard,
            //         opt => opt.MapFrom(src => src.ApplicationFee.FirstOrDefault().IsStandardFee));
            //     

            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
            // .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName));


            CreateMap<ApplicationNewRequest, Application>()
                .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
                .ForMember(dest => dest.AccountNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.AccountNumber))
                .ForMember(dest => dest.RoutingNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.RoutingNumber))
                .ForMember(dest => dest.DdaType, opt => opt.MapFrom(src => src.BankAccountInformation.DdaType))
                .ForMember(dest => dest.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportName))
                .ForMember(dest => dest.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportEmail))
                .ForMember(dest => dest.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportPhone))
                .ForMember(dest => dest.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportLink))
                .ForMember(dest => dest.LogoUrl, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.LegalEntityName))
                .ForPath(dest => dest.DeveloperContact.FirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperFirstName))
                .ForPath(dest => dest.DeveloperContact.LastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperLastName))
                .ForPath(dest => dest.DeveloperContact.Phone,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperPhone))
                .ForPath(dest => dest.DeveloperContact.Email,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperEmail));
            //.ForMember(dest => dest.Fees, opt => opt.Ignore());
            
            CreateMap<PublicApplicantCreateDTO, Application>()
                .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
                .ForMember(dest => dest.AccountNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.AccountNumber))
                .ForMember(dest => dest.RoutingNumber,
                    opt => opt.MapFrom(src => src.BankAccountInformation.RoutingNumber))
                .ForMember(dest => dest.DdaType, opt => opt.MapFrom(src => src.BankAccountInformation.DdaType))
                .ForMember(dest => dest.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportName))
                .ForMember(dest => dest.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportEmail))
                .ForMember(dest => dest.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportPhone))
                .ForMember(dest => dest.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportLink))
                .ForMember(dest => dest.LogoUrl, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.LegalEntityName));

            CreateMap<ApplicationFeeDTO, ApplicationFee>();
            CreateMap<ApplicationFee, ApplicationFeeDTO>();
            CreateMap<ApplicationFee, ApplicationFeeQueryDTO>();
            CreateMap<Partner, PartnerQueryDTO>().ReverseMap();
            CreateMap<SalesAgency, SalesAgencyQueryDTO>().ReverseMap();

            CreateMap<Application, ApplicationQueryResponse>();
            CreateMap<Application, BankAccountInformationDTO>().ReverseMap();
            CreateMap<Application, ApplicationDTO>()
                .ForPath(dest => dest.BankAccountInformation.MaskedAccountNumber,
                    opt => opt.MapFrom(src =>
                        src.AccountNumber.Length >= 4
                            ? src.AccountNumber.Mask(0, src.AccountNumber.Length - 4, 'x')
                            : src.AccountNumber))
                .ForPath(dest => dest.BankAccountInformation.MaskedRoutingNumber,
                    opt => opt.MapFrom(src =>
                        src.RoutingNumber.Length >= 4
                            ? src.RoutingNumber.Mask(0, src.RoutingNumber.Length - 4, 'x')
                            : src.RoutingNumber))
                .ForPath(dest => dest.BankAccountInformation.DdaType,
                    opt => opt.MapFrom(src => src.DdaType))
                .ForPath(dest => dest.BankAccountInformation.BankName,
                    opt => opt.MapFrom(src => src.BankName))
                .ForMember(dest => dest.StandardFeeSelected,
                    opt => opt.MapFrom(src => src.Fees.Any(x => x.IsStandard)))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportEmail))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportPhone))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportLink))
                .ForPath(dest => dest.Partner.Name,
                    opt => opt.MapFrom(src => src.Partner.Name))
                .ForPath(dest => dest.Partner.Id,
                    opt => opt.MapFrom(src => src.Partner.Id))
                .ForPath(dest => dest.SalesAgency.Name,
                    opt => opt.MapFrom(src => src.SalesAgency.Name))
                .ForPath(dest => dest.SalesAgency.Id,
                    opt => opt.MapFrom(src => src.SalesAgency.Id))
                .ForPath(dest => dest.DeveloperContact.DeveloperFirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.FirstName))
                .ForPath(dest => dest.DeveloperContact.DeveloperLastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.LastName))
                .ForPath(dest => dest.DeveloperContact.DeveloperPhone,
                    opt => opt.MapFrom(src => src.DeveloperContact.Phone))
                .ForPath(dest => dest.DeveloperContact.DeveloperEmail,
                    opt => opt.MapFrom(src => src.DeveloperContact.Email));
            
            CreateMap<Application, PublicApplicantQueryDTO>()
                .ForPath(dest => dest.BankAccountInformation.MaskedAccountNumber,
                    opt => opt.MapFrom(src =>
                        src.AccountNumber.Length >= 4
                            ? src.AccountNumber.Mask(0, src.AccountNumber.Length - 4, 'x')
                            : src.AccountNumber))
                .ForPath(dest => dest.BankAccountInformation.MaskedRoutingNumber,
                    opt => opt.MapFrom(src =>
                        src.RoutingNumber.Length >= 4
                            ? src.RoutingNumber.Mask(0, src.RoutingNumber.Length - 4, 'x')
                            : src.RoutingNumber))
                .ForPath(dest => dest.BankAccountInformation.DdaType,
                    opt => opt.MapFrom(src => src.DdaType))
                .ForPath(dest => dest.BankAccountInformation.BankName,
                    opt => opt.MapFrom(src => src.BankName))

                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportName))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportEmail))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportPhone))
                .ForPath(dest => dest.CustomerSupportInformation.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportLink));

            CreateMap<Application, ApplicationResponse>();
            CreateMap<BankAccountInformationDTO, ApplicationUpdateRequest>();
            CreateMap<ApplicationUpdateRequest, Application>()
                .ForMember(dest => dest.CustomerSupportName,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportName))
                .ForMember(dest => dest.CustomerSupportEmail,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportEmail))
                .ForMember(dest => dest.CustomerSupportPhone,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportPhone))
                .ForMember(dest => dest.CustomerSupportLink,
                    opt => opt.MapFrom(src => src.CustomerSupportInformation.CustomerSupportLink))
                .ForMember(dest => dest.DdaType, opt => opt.MapFrom(src => src.BankAccountInformation.DdaType))
                .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.BankAccountInformation.BankName))
                .ForMember(dest => dest.RoutingNumber,
                    opt =>
                    {
                        opt.PreCondition(src => int.TryParse(src.BankAccountInformation.RoutingNumber, out int res));
                        opt.MapFrom(src => src.BankAccountInformation.RoutingNumber);
                    })
                .ForMember(dest => dest.AccountNumber,
                    opt =>
                    {
                        opt.PreCondition(src => int.TryParse(src.BankAccountInformation.AccountNumber, out int res));
                        opt.MapFrom(src => src.BankAccountInformation.AccountNumber);
                    })
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.LegalEntityName))
                .ForMember(dest => dest.LogoUrl, opt => opt.Ignore())
                .ForPath(dest => dest.DeveloperContact.FirstName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperFirstName))
                .ForPath(dest => dest.DeveloperContact.LastName,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperLastName))
                .ForPath(dest => dest.DeveloperContact.Phone,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperPhone))
                .ForPath(dest => dest.DeveloperContact.Email,
                    opt => opt.MapFrom(src => src.DeveloperContact.DeveloperEmail));

            CreateMap<FeeConfiguration, FeeConfigurationResponse>();
            CreateMap<FeeConfigurationRequest, FeeConfiguration>();

            CreateMap<FundsReserveConfiguration, FundsReserveConfigurationResponse>()
                .ForMember(dest => dest.DisputeRateMax,
                    opt => opt.MapFrom(src => Formatters.IntToDecimal(src.DisputeRateMax)))
                .ForMember(dest => dest.DisputeRateMin,
                    opt => opt.MapFrom(src => Formatters.IntToDecimal(src.DisputeRateMin)));

            CreateMap<IPagedList<Application>, PagedDTO<ApplicationDTO>>()
                .ConvertUsing(new PagedListTypeConverter<Application, ApplicationDTO>());
            
            CreateMap<IPagedList<Application>, PagedDTO<PublicApplicantQueryDTO>>()
                .ConvertUsing(new PagedListTypeConverter<Application, PublicApplicantQueryDTO>());

            CreateMap<IPagedList<Merchant>, PagedDTO<MerchantResponse>>()
                .ConvertUsing(new PagedListTypeConverter<Merchant, MerchantResponse>());

            CreateMap<Address, AddressDTO>()
                .ForMember(dest => dest.Line1, opt => opt.MapFrom(src => src.Line1))
                .ForMember(dest => dest.Line2, opt => opt.MapFrom(src => src.Line2));

            CreateMap<AddressDTO, Address>()
                .ForMember(dest => dest.Line1, opt => opt.MapFrom(src => src.Line1))
                .ForMember(dest => dest.Line2, opt => opt.MapFrom(src => src.Line2));

            CreateMap<OwnerDTO, Owner>()
                .ForMember(dest => dest.Address, opt => opt.MapFrom(src => src.Address));
            
            CreateMap<Contact, PrimaryContact>();
            CreateMap<PrimaryContact, Contact>();

            CreateMap<MccCode, MccCodesDTO>();

            CreateMap<Site, SiteDTO>()
                .ForMember(dest => dest.Tags, opt => opt.MapFrom(src =>
                    JsonSerializer.Deserialize<IEnumerable<string>>(src.Tags.ToString(), new JsonSerializerOptions())));

            CreateMap<AuditLog, AuditLogResponseDTO>();
            CreateMap<IPagedList<AuditLog>, PagedDTO<AuditLogResponseDTO>>()
                .ConvertUsing(new PagedListTypeConverter<AuditLog, AuditLogResponseDTO>());

            CreateMap<IPagedList<AuditLogResponseDTO>, PagedDTO<AuditLogResponseDTO>>()
                .ConvertUsing(new PagedListTypeConverter<AuditLogResponseDTO, AuditLogResponseDTO>());

            CreateMap<IPagedList<AplicationActivityDTO>, PagedDTO<AplicationActivityDTO>>();

            CreateMap<IPagedList<AplicationActivityDTO>, PagedDTO<AplicationActivityDTO>>()
                .ConvertUsing(new PagedListTypeConverter<AplicationActivityDTO, AplicationActivityDTO>());
        }
    }

    //class ContactConverter : ITypeConverter<PrimaryContact, IEnumerable<Contact>>
    //{
    //    public IEnumerable<Contact> Convert(PrimaryContact source, IEnumerable<Contact> destination, ResolutionContext context)
    //    {
    //        foreach (var dto in destination.Select(e => Mapper.Map<PrimaryContact>(e)))
    //        {
    //            Mapper.Map(destination, dto); 
    //            yield return dto;
    //    }
    //}

    public class SingleObjectToListConverter<T> : ITypeConverter<T, List<T>>
    {
        public List<T> Convert(T source, List<T> destination, ResolutionContext context)
        {
            return new List<T>() {source};
        }
    }
}