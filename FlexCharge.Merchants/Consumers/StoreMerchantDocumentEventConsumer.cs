using System;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Services.ApplicationServices;
using FlexCharge.Merchants.Services.MerchantServices;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Merchants.Consumers;

public class StoreMerchantDocumentEventConsumer : IdempotentCommandConsumer<DownloadAndStoreMerchantDocumentCommand>
{
    private readonly IBigPayloadService _bigPayloadService;
    private readonly IMerchantService _merchantService;
    private IHttpClientFactory _httpClientFactory;

    public StoreMerchantDocumentEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IBigPayloadService bigPayloadService,
        IMerchantService merchantService, IHttpClientFactory httpClientFactory) : base(serviceScopeFactory)
    {
        _bigPayloadService = bigPayloadService;
        _merchantService = merchantService;
        _httpClientFactory = httpClientFactory;
    }

    protected override async Task ConsumeCommand(DownloadAndStoreMerchantDocumentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("FileName", command.FileName);

            using var response = await GetFileDownloadStreamAsync(command);
            await using var responseContentStream = await response.Content.ReadAsStreamAsync();


            await _merchantService.StoreDocumentAsync(command.Mid, command.FileName, command.ContentType,
                responseContentStream,
                sendNotification: false);
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e, "Cannot download and store merchant document");
            throw;
        }
    }

    private async Task<HttpResponseMessage> GetFileDownloadStreamAsync(DownloadAndStoreMerchantDocumentCommand command)
    {
        var httpClient = _httpClientFactory.CreateClient();

        httpClient.Timeout = TimeSpan.FromMinutes(10);

        using var request = new HttpRequestMessage(HttpMethod.Get, command.DownloadUrl);

        if (command.Bearer != null)
        {
            request.Headers.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", command.Bearer);
        }

        if (command.Headers?.Count > 0)
        {
            foreach (var header in command.Headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }
        }

        // using ResponseHeadersRead option to avoid loading the whole file into the memory
        // see: https://medium.com/@szntb/getting-burnt-with-httpclient-9c1712151039 
        var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);

        if (!response.IsSuccessStatusCode)
        {
            Workspan
                .Tag("StatusCode", response.StatusCode)
                .Tag("ReasonPhrase", response.ReasonPhrase)
                .Log.Fatal("Failed to download file");

            throw new FlexChargeException($"Failed to download report. Status: {response.StatusCode}");
        }

        return response;
    }
}