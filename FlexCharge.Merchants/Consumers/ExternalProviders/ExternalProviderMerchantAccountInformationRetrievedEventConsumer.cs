using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Services.ApplicationServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Merchants.Entities.Merchant;

namespace FlexCharge.Merchants.Consumers.ExternalProviders;

#region Retry Configuration

public class ExternalProviderMerchantAccountInformationRetrievedEventConsumerDefinition
    : ConsumerDefinitionBase<ExternalProviderMerchantAccountInformationRetrievedEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class ExternalProviderMerchantAccountInformationRetrievedEventConsumer
    : IdempotentEventConsumer<ExternalProviderMerchantAccountInformationRetrievedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IMapper _mapper;

    public ExternalProviderMerchantAccountInformationRetrievedEventConsumer(
        IServiceScopeFactory serviceScopeFactory,
        PostgreSQLDbContext dbContext,
        IPublishEndpoint publisher, IMapper mapper)
        : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _mapper = mapper;
    }

    protected override async Task ConsumeEvent(ExternalProviderMerchantAccountInformationRetrievedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Message(message)
            .Baggage("ExternalAccountId", message.ExternalAccountId);

        try
        {
            var merchant = await _dbContext.Merchants
                .Include(x => x.Address)
                .Include(x => x.Fees)
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .Include(x => x.Partner)
                .Include(x => x.SalesAgency)
                .Include(x => x.Owners)
                .ThenInclude(owner => owner.Address)
                .SingleAsync(x => x.Id == message.Mid);

            if (merchant.CustomerSupportEmail == null && !string.IsNullOrWhiteSpace(message.SupportEmail))
            {
                if (ValidationHelpers.IsValidEmail(message.SupportEmail))
                {
                    merchant.CustomerSupportEmail = message.SupportEmail;
                }
                else
                {
                    Workspan.Log.Fatal("Invalid email for SupportEmail");
                }
            }

            if (merchant.CustomerSupportPhone == null && !string.IsNullOrWhiteSpace(message.SupportPhone))
            {
                // if (ValidationHelpers.IsValidPhoneNumber(message.SupportPhone))
                // {
                merchant.CustomerSupportPhone = message.SupportPhone;
                // }
                // else
                // {
                //     Workspan.Log.Fatal("Invalid phone number for SupportPhone");
                // }
            }

            if (merchant.CustomerSupportLink == null && !string.IsNullOrWhiteSpace(message.SupportUrl))
            {
                if (ValidationHelpers.IsValidUrl(message.SupportUrl))
                {
                    merchant.CustomerSupportLink = message.SupportUrl;
                }
                else
                {
                    Workspan.Log.Fatal("Invalid URL for SupportUrl");
                }
            }

            if (merchant.Descriptor == null && !string.IsNullOrWhiteSpace(message.PaymentsDescriptor))
            {
                merchant.Descriptor = message.PaymentsDescriptor;
            }

            // TODO: Also store CardPaymentsDescriptorPrefix?


            await _dbContext.SaveChangesAsync();

            await _publisher.Publish(
                MerchantUpdateEventsFactory.ConstructMerchantUpdatedEvent(merchant, _mapper));
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e, "Could not update merchant information from external provider");
            throw new MassTransitRetryException();
        }
    }
}