using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands.ExternalProviders;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Merchants.Consumers.ExternalProviders;

public class
    ExternalProviderDownloadReportFileCommandConsumer : IdempotentCommandConsumer<
    ExternalProviderDownloadReportFileCommand>
{
    public ExternalProviderDownloadReportFileCommandConsumer(IServiceScopeFactory serviceScopeFactory) : base(
        serviceScopeFactory)
    {
    }

    protected override async Task ConsumeCommand(ExternalProviderDownloadReportFileCommand command,
        CancellationToken cancellationToken)
    {
    }
}