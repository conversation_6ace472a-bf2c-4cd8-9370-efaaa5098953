using System;
using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.Entities;

public class Partner : AuditableEntity
{
    public string Name { get; set; }
    public Contact Contact { get; set; }
    public IEnumerable<CommissionsConfiguration> CommissionsConfigurations { get; set; }

    //These are the fees the partner should pay to FlexCorp inc.
    public IEnumerable<PartnerFee> PartnerFees { get; set; }

    public Guid? FinancialAccountId { get; set; }

    public string Type { get; set; }

    public string SupportName { get; set; }
    public string SupportEmail { get; set; }
    public string SupportPhone { get; set; }
    public string SupportLink { get; set; }

    public string LogoUrl { get; set; }

    public string TermsAndConditionsUrl { get; set; }
    public string PrivacyPolicyUrl { get; set; }

    public string MerchantTermsAndConditionsUrl { get; set; }
    public string TermsAndConditions { get; set; }
    public string ConsentLabel { get; set; }
    public Address Address { get; set; }

    public string NotificationSenderEmail { get; set; }
    public string NotificationSenderPhone { get; set; }
    public string ReplyToEmail { get; set; }
    public string BccEmail { get; set; }

    public string SiteUrl { get; set; }

    public List<FeeConfiguration> FeeConfigurations { get; set; }

    public Guid? DashboardId { get; set; }
    public string OperationsEmail { get; set; }
    public string RiskEmail { get; set; }
    public string SalesEmail { get; set; }

    // FIPAD batch will be auto posted to the partner's account and the fees will be auto collected
    public bool PartnerFeesAutoCollectEnabled { get; set; } = false;
    public ActiveInActive? Status { get; set; }
    public Guid? RelatedWhitelabelPartnerId { get; set; }
    public JsonDocument OnboardingConfiguration { get; set; }
}