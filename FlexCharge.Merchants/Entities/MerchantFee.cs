using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace FlexCharge.Merchants.Entities
{
    public class MerchantFee : AuditableEntity
    {
        public string Name { get; set; }
        public FEE_TYPE Type { get; set; }
        public CHARGE_TYPE ChargeType { get; set; }
        public int Amount { get; set; }

        public bool IsActive { get; set; } = true;

        //public Guid MerchantId { get; set; }
        public int? MinimumFeeAmount { get; set; }
        public Merchant RelatedMerchant { get; set; }
    }

    public enum FEE_TYPE
    {
        Fixed = 1,
        Percentage = 2
    }

    public enum CHARGE_TYPE
    {
        PER_TRANSACTION = 1,
        ONE_TIME = 2,
        PER_CHARGEBACK_TRANSACTION = 3
    }
}