using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Merchants.Authorization
{
    public class Claims
    {
        public const string MERCHANT_READ_ALL = "merchant.read.all";
        public const string MERCHANT_READ_ONE = "merchant.read.one";
        public const string MERCHANT_ADD = "merchant.add";
        public const string MERCHANT_UPDATE = "merchant.update";
        public const string MERCHANT_DELETE = "merchant.delete";
        public const string MERCHANT_MANAGE = "merchant.manage";

        public const string APPLICATION_READ_ALL = "application.read.all";
        public const string APPLICATION_READ_ONE = "application.read.one";
        public const string APPLICATION_ADD = "application.add";
        
        public const string ONBOARDING_WRITE = "onboarding.write";
        public const string ONBOARDING_READE = "onboarding.read";
        public const string ONBOARDING_MANAGE = "onboarding.manage";
    }
}
