using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Elasticsearch.Net;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Entities;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.ModelBinding.Metadata;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Merchant = FlexCharge.Merchants.Entities.Merchant;
using MerchantFee = FlexCharge.Merchants.Entities.MerchantFee;
using HtmlAgilityPack;

namespace FlexCharge.Merchants.Controllers
{
    [Route("public/merchants")]
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
    public class MerchantsPublicController : BaseController
    {
        private PostgreSQLDbContext _context { get; set; }
        private readonly AppOptions _globalData;
        private readonly IMapper _mapper;

        private readonly IPublishEndpoint _publisher;

        private readonly IDistributedCache _cache;

        public MerchantsPublicController(PostgreSQLDbContext context, IMapper mapper,
            IOptions<AppOptions> globalData, IPublishEndpoint publisher, IDistributedCache cache)
        {
            _context = context;
            _mapper = mapper;

            _publisher = publisher;
            _cache = cache;
            _globalData = globalData.Value;
        }

        // [HttpGet()] // GET ALL
        // [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        // [ProducesResponseType(typeof(PagedDTO<Merchant>), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // public async Task<IActionResult> Get(CancellationToken token, string q, ActiveInActive? status, Guid? mid,
        //     string sort,
        //     int pageNumber = 1, int pageSize = 10)
        // {
        //     using var workspan = Workspan.Start<MerchantPublicController>().LogEnterAndExit();
        //     try
        //     {
        //         var merchants = _context.Merchants
        //             .Include(x => x.Address)
        //             .Include(x => x.PrimaryContact)
        //             .Include(x => x.SalesAgency)
        //             .Include(x => x.Fees).AsQueryable();
        //
        //         if (mid != null)
        //             merchants = merchants.Where(x => x.Id == mid);
        //
        //         if (HttpContext.User.Claims.Any(x =>
        //                 x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.PARTNER_ADMIN))
        //             merchants = merchants.Where(x => x.PartnerId == GetPID());
        //
        //         if (status != null)
        //             merchants = merchants.Where(x => x.Status == status);
        //
        //         if (!string.IsNullOrEmpty(q))
        //         {
        //             var search = q.ToLower();
        //
        //             merchants = merchants.Where(x =>
        //                 x.CompanyName.ToLower().Contains(search) ||
        //                 x.Dba.ToLower().Contains(search) ||
        //                 x.Id.ToString().ToLower().Contains(search) ||
        //                 x.LegalEntityName.ToLower().Contains(search));
        //         }
        //
        //         merchants = merchants.OrderByDescending(x => x.ModifiedOn);
        //
        //         var pagedMerchants = await Task.Run(() => merchants.ToPagedListAsync(pageNumber, pageSize));
        //
        //         return Ok(_mapper.Map<IPagedList<Merchant>, PagedDTO<MerchantResponse>>(pagedMerchants));
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.Log.Error(e,
        //             "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
        //             _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
        //             HttpContext.Request.QueryString);
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
        //     }
        // }

        [HttpGet]
        [ProducesResponseType(typeof(MerchantPublicResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(CancellationToken token)
        {
            using var workspan = Workspan.Start<MerchantsPublicController>().LogEnterAndExit();
            try
            {
                var query = _context.Merchants
                    .Include(x => x.Account)
                    .Include(x => x.Sites)
                    .Include(x => x.Address)
                    .Include(x => x.PrimaryContact)
                    .Include(x => x.DeveloperContact)
                    .Include(x => x.Fees)
                    .AsQueryable();

                Merchant record;

                record = await query.SingleOrDefaultAsync(x => x.Id == GetMID(), cancellationToken: token);

                if (record == null)
                    return NotFound("Merchant not found");

                var mapped = _mapper.Map<MerchantPublicResponse>(record);

                return Ok(mapped);
            }
            catch (Exception e)
            {
                workspan.Log.Fatal(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching Merchant");
            }
        }


        // [HttpGet()] // GET BY ID
        // [Route("{id:guid}")]
        // [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        // [ProducesResponseType(typeof(MerchantPublicResponse), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        // {
        //     using var workspan = Workspan.Start<MerchantController>();
        //     try
        //     {
        //         workspan.Log.Information(
        //             "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
        //             _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
        //             HttpContext.Request.QueryString);
        //
        //         var query = _context.Merchants
        //             .Include(x => x.Address)
        //             .Include(x => x.PrimaryContact)
        //             .Include(x => x.DeveloperContact)
        //             .Include(x => x.Partner)
        //             .Include(x => x.Fees)
        //             .Include(x => x.SalesAgency)
        //             .AsQueryable();
        //
        //         Merchant record;
        //
        //
        //         record = await query.SingleOrDefaultAsync(x => x.Id == GetMID(), cancellationToken: token);
        //
        //         if (record == null)
        //             return NotFound("Merchant not found");
        //
        //         var mapped = _mapper.Map<MerchantPublicResponse>(record);
        //
        //         workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
        //             _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
        //             HttpContext.Request.QueryString);
        //         
        //         return Ok(mapped);
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.Log.Error(e,
        //             "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
        //             _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
        //             HttpContext.Request.QueryString);
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching Merchant");
        //     }
        // }

        [HttpGet("{id:guid}/sites")]
        [ProducesResponseType(typeof(PagedDTO<Site>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSites(Guid id)
        {
            using var workspan = Workspan.Start<MerchantController>();
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                var sites = _context.Sites
                    .Include(c => c.WhitelistedUrls)
                    .Where(x => x.MerchantId == GetMID());

                var sitesList = await sites
                    .OrderByDescending(x => x.ModifiedOn)
                    .Select(x => new SiteDTO
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Descriptor = x.Descriptor,
                        DescriptorCity = x.DescriptorCity,
                        CustomerSupportName = x.CustomerSupportName,
                        CustomerSupportEmail = x.CustomerSupportEmail,
                        CustomerSupportPhone = x.CustomerSupportPhone,
                        CustomerSupportLink = x.CustomerSupportLink,
                        WhitelistedUrls = x.WhitelistedUrls.Select(y => y.Link),
                        Tags = x.Tags != null ? JsonConvert.DeserializeObject<List<string>>(x.Tags) : null,
                    }).ToListAsync();

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return Ok(sitesList);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching sites");
            }
        }

        [HttpGet("{id:guid}/sites/{siteId:guid}")]
        [ProducesResponseType(typeof(PagedDTO<Site>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(Guid id, Guid siteId)
        {
            using var workspan = Workspan.Start<MerchantsPublicController>().LogEnterAndExit();
            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => {RequestMethod} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                var site = _context.Sites.Where(x => x.MerchantId == GetMID() && x.Id == siteId);

                var siteDto = await site.Select(x => new SiteDTO()
                {
                    Id = x.Id,
                    Name = x.Name,
                    Descriptor = x.Descriptor,
                    DescriptorCity = x.DescriptorCity,
                    CustomerSupportName = x.CustomerSupportName,
                    CustomerSupportEmail = x.CustomerSupportEmail,
                    CustomerSupportPhone = x.CustomerSupportPhone,
                    CustomerSupportLink = x.CustomerSupportLink,
                    WhitelistedUrls = x.WhitelistedUrls.Select(y => y.Link),
                    Tags = x.Tags != null ? JsonConvert.DeserializeObject<List<string>>(x.Tags) : null,
                }).SingleOrDefaultAsync();

                if (siteDto == null)
                    return NotFound("Site not found");

                workspan.Log.Information("EXIT: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return Ok(siteDto);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching site");
            }
        }
    }
}