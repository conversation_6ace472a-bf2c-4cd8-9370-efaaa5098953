using FlexCharge.Merchants.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Authorization;
using FlexCharge.Merchants.DTO.Public;
using FlexCharge.Merchants.Enums;
using FlexCharge.Merchants.Services.ApplicationServices;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;


namespace FlexCharge.Merchants.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
    public class OnboardingPublicController : BaseController
    {
        private readonly IApplicationService _applicationService;
        private readonly AppOptions _globalData;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IRecaptchaService recaptchaService;
        private readonly IPublishEndpoint _publisher;
        private readonly IRequestClient<GenerateProviderOAuthLinkCommand> _generateProviderOAuthLinkCommand;
        private readonly LinkGeneratorService _urlHelper;

        public OnboardingPublicController(PostgreSQLDbContext context, IApplicationService applicationService,
            IOptions<AppOptions> globalData, IUrlShortenerService urlShortenerService,
            IRecaptchaService recaptchaService, IPublishEndpoint publisher,
            IRequestClient<GenerateProviderOAuthLinkCommand> generateProviderOAuthLinkCommand, LinkGeneratorService urlHelper)
        {
            _globalData = globalData.Value;
            _applicationService = applicationService;
            _dbContext = context;
            this.recaptchaService = recaptchaService;
            _publisher = publisher;
            _generateProviderOAuthLinkCommand = generateProviderOAuthLinkCommand;
            _urlHelper = urlHelper;
        }
        
        [HttpPost("/application/invite")]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PublicInviteApplicant([FromBody] PublicApplicantCreateDTO request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                //check JWT token
                Guid partnerId = GetPID();
                var scopeClaims = GetScopeClaims();
                var hasPermissions =
                    scopeClaims?.Contains(Claims.ONBOARDING_MANAGE) == true;
                
                if (partnerId == Guid.Empty || scopeClaims == null || !hasPermissions) 
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                var partner = await _dbContext.Partners
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == partnerId, token);
                
                if (partner == null)
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                request.PartnerId = partnerId;
                
                var applicantUpdateValidation = partner.OnboardingConfiguration?.RootElement
                    .GetProperty("applicationInviteValidation")
                    .GetString();
                
                IValidator<PublicApplicantCreateDTO> validator = applicantUpdateValidation switch
                {
                    "ValorPayTechInviteRequestValidator" => new ValorPayTechInviteRequestValidator(),
                    _ => new PublicApplicantCreateValidator()
                };

                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                {
                    ModelState.AddFluentValidationErrors(validationResult);
                }
                
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var response = await _applicationService.PublicCreateAsync(partner, request);

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        [HttpPost("/application")]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PublicCreateApplicant(PublicApplicantCreateDTO request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                //check JWT token
                Guid partnerId = GetPID();
                var scopeClaims = GetScopeClaims();
                var hasPermissions =
                    scopeClaims?.Contains(Claims.ONBOARDING_MANAGE) == true;
                
                if (partnerId == Guid.Empty || scopeClaims == null || !hasPermissions) 
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                request.PartnerId = partnerId;

                var partner = await _dbContext.Partners
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == partnerId, token);

                if (partner == null)
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var applicantCreateValidation = partner.OnboardingConfiguration?.RootElement
                    .GetProperty("applicationCreateValidation")
                    .GetString();

                IValidator<PublicApplicantCreateDTO> validator = applicantCreateValidation switch
                {
                    "ValorPayTechCreateRequestValidator" => new ValorPayTechInviteRequestValidator(),
                    _ => new PublicApplicantCreateValidator()
                };

                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                {
                    ModelState.AddFluentValidationErrors(validationResult);
                }

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var response = await _applicationService.PublicCreateAsync(partner, request);

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        [HttpPut("/application/{id:guid}")]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PublicUpdateApplicant([FromRoute] Guid id,
            [FromBody] PublicApplicantUpdateDTO request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                if (id == Guid.Empty)
                    ModelState.AddModelError("id", "Id is required");

                Guid partnerId = GetPID();
                var scopeClaims = GetScopeClaims();
                var hasPermissions =
                    scopeClaims?.Contains(Claims.ONBOARDING_MANAGE) == true;
                
                if (partnerId == Guid.Empty || scopeClaims == null || !hasPermissions) 
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                request.PartnerId = partnerId;
                
                var partner = await _dbContext.Partners
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == partnerId, token);

                if (partner == null)
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var applicantUpdateValidation = partner.OnboardingConfiguration?.RootElement
                    .GetProperty("applicationUpdateValidation")
                    .GetString();

                IValidator<PublicApplicantUpdateDTO> validator = applicantUpdateValidation switch
                {
                    "ValorPayTechUpdateRequestValidator" => new ValorPayTechUpdateRequestValidator(),
                    _ => new PublicApplicantUpdateValidator()
                };

                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);

                if (!ModelState.IsValid)
                    return ValidationProblem();

                var response = await _applicationService.PublicUpdateAsync(id, partnerId, request);

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        [HttpGet("/application/{id:guid}")]
        [ProducesResponseType(typeof(FullApplicationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApplication(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
            try
            {
                var partnerId = GetPID();
                var scopeClaims = GetScopeClaims();
                var hasPermissions =
                    scopeClaims?.Contains(Claims.ONBOARDING_MANAGE) == true;
                
                if (partnerId == Guid.Empty || scopeClaims == null || !hasPermissions) 
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return !ModelState.IsValid
                    ? ValidationProblem()
                    : Ok(await _applicationService.PublicGetApplicationByIdAsync(id, partnerId));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching a application");
            }
        }
        
        [HttpGet("/application")] // Get all
        [ProducesResponseType(typeof(FullApplicationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApplications(Guid id, [FromQuery] PublicGetApplicationsDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
            try
            {
                var partnerId = GetPID();
                var scopeClaims = GetScopeClaims();
                var hasPermissions =
                    scopeClaims?.Contains(Claims.ONBOARDING_MANAGE) == true;
                
                if (partnerId == Guid.Empty || scopeClaims == null || !hasPermissions) 
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                var validator = new PublicGetApplicationsValidator();
                
                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);

                var response = await _applicationService.PublicGetAsync(partnerId, request.From, request.To,
                    request.OrderBy,
                    request.SortField, request.PageSize, request.PageNumber);
                response = AddLinksToResponse(response, request.From, request.To, request.OrderBy, request.SortField,
                    request.PageSize, request.PageNumber);
                
                return !ModelState.IsValid
                    ? ValidationProblem()
                    : Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching a application");
            }
        }

        private PublicApplicationQueryResponse AddLinksToResponse(PublicApplicationQueryResponse response,
            DateTime? from, DateTime? to,
            string orderBy, string sortField, int pageSize, int pageNumber)
        {
            object GetQueryParams(int newPageNumber)
            {
                return new
                {
                    from,
                    to,
                    orderBy,
                    sortField,
                    pageSize,
                    pageNumber = newPageNumber
                };
            }

            response.Links = new List<HateoasLink>();

            if (pageNumber > 1)
            {
                response.Links.Add(new HateoasLink
                {
                    Href = _urlHelper.GenerateLink(String.Empty, GetQueryParams(pageNumber - 1)),
                    Rel = "prev",
                    Method = "GET",
                    Category = "Applications",
                    ActionName = "GetPrev",
                    Description = "Get Previous Page"
                });

                response.Links.Add(new HateoasLink
                {
                    Href = _urlHelper.GenerateLink(String.Empty, GetQueryParams(1)),
                    Rel = "first",
                    Method = "GET",
                    Category = "Applications",
                    ActionName = "GetFirst",
                    Description = "Get First Page"
                });
            }

            if (response.Results.PageCount > pageNumber)
            {
                response.Links.Add(new HateoasLink
                {
                    Href = _urlHelper.GenerateLink(String.Empty, GetQueryParams(pageNumber + 1)),
                    Rel = "next",
                    Method = "GET",
                    Category = "Applications",
                    ActionName = "GetNext",
                    Description = "Get Next Page"
                });

                if (response.Results.PageCount > pageNumber + 1 && !response.Results.IsCountEstimated)
                {
                    response.Links.Add(new HateoasLink
                    {
                        Href = _urlHelper.GenerateLink(String.Empty, GetQueryParams(response.Results.PageCount)),
                        Rel = "last",
                        Method = "GET",
                        Category = "Applications",
                        ActionName = "GetLast",
                        Description = "Get Last Page"
                    });
                }
            }

            return response;
        }
    }
}