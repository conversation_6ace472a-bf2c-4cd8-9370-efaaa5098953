using FlexCharge.Merchants.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;


namespace FlexCharge.Merchants.Controllers
{
    public partial class OnboardingController
    {
        [HttpPost("admin/application")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AdminRegularCreatePost(AdminApplicationCreateRequest request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                var validator = new ApplicationCreateRequestValidator();
                var validationResult = await validator.ValidateAsync(request, token);
                
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);
                
                if (!ModelState.IsValid)
                    return ValidationProblem();

                ApplicationNewResponse response;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.AdminCreateAsync(request);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    request.PartnerId = GetPID();
                    response = await _applicationService.AdminCreateAsync(request);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        [HttpPut("admin/application/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AdminUpdateApplicant([FromRoute] Guid id,
            AdminApplicationUpdateRequest request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                if (id == Guid.Empty)
                    ModelState.AddModelError("id", "Id is required");
                
                var validator = new ApplicationUpdateRequestValidator();
                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);

                if (!ModelState.IsValid)
                    return ValidationProblem();
                
                ApplicationNewResponse response;

                request.Id = id;
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.AdminUpdateAsync(request, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.AdminUpdateAsync(request, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        [HttpGet("admin/application/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(FullApplicationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AdminGetApplication(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
            try
            {
                return !ModelState.IsValid
                    ? ValidationProblem()
                    : Ok(await _applicationService.AdminGetApplicationByIdAsync(id, null));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching a application");
            }
        }

        [HttpPost("admin/application/quick")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> QuickCreateApplicant(ApplicationQuickCreateRequest request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData)
                .LogEnterAndExit();

            try
            {
                var validator = new ApplicationQuickCreateRequestValidator();
                var validationResult = await validator.ValidateAsync(request, token);
                
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);
                
               if (!ModelState.IsValid)
                    return ValidationProblem();
               
               ApplicationNewResponse response;

               if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) || HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
               {
                   response = await _applicationService.QuickUpsertAsync(request, null);
               }
               else
               {
                   return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
               }
                
               return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }

        [HttpPut("admin/application/quick/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationNewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> QuickUpdateApplicant([FromRoute] Guid id,
            ApplicationQuickCreateRequest request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);

            try
            {
                if (id == Guid.Empty)
                    ModelState.AddModelError("id", "Id is required");
                var validator = new ApplicationQuickUpdateRequestValidator();
                var validationResult = await validator.ValidateAsync(request, token);
                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);
                
                if (!ModelState.IsValid)
                    return ValidationProblem();

                request.Id = id;
                
                ApplicationNewResponse response;
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.QuickUpsertAsync(request, null);

                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.QuickUpsertAsync(request, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }

        [HttpGet("admin/application/quick/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAdminApplicant(Guid id,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
            try
            {
                ApplicationResponse response;
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.GetQuickByIdAsync(id, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.GetQuickByIdAsync(id, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }

        /// <summary>
        /// Only super admin can invoke cancel 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost("admin/application/{id:guid}/cancel")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationCancelResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Cancel([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.CancelAsync(id, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.CancelAsync(id, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpPost("admin/application/{id:guid}/decline")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationCancelResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Decline([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.DeclineAsync(id, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.DeclineAsync(id, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpPost("admin/application/{id:guid}/reopen")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationCancelResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Reopen([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                { 
                    await _applicationService.ReopenAsync(id, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.ReopenAsync(id, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpPut("admin/application/{id:guid}/risk-status")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ChangeRiskStatus([FromRoute] Guid id, RiskAssessmentRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                
                if (!ModelState.IsValid)
                    return ValidationProblem();

                payload.ApplicationId = id;
                
                RiskResponseDTO response;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.RiskUpdateStatusAsync(payload, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.RiskUpdateStatusAsync(payload, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update risk status");
            }
        }

        [HttpPut("admin/application/{id:guid}/risk-category")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ChangeRiskCategory([FromRoute] Guid id, RiskCategoryRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.UpdateRiskCategoryAsync(id, payload.RiskCategory, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.UpdateRiskCategoryAsync(id, payload.RiskCategory, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update risk status");
            }
        }

        [HttpPut("admin/application/{id:guid}/operations-status")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ChangeOperationsStatus([FromRoute] Guid id,
            OperationAssessmentRequestDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();
                
                payload.ApplicationId = id;
                
                Guid? partnerId = HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ? GetPID() : null;

                OperationAssessmentResponseDTO response;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.OperationUpdateStatusAsync(payload, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.OperationUpdateStatusAsync(payload, partnerId);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                    
                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update operations status");
            }
        }

        [HttpPost("admin/application/{id:guid}/notify-account-manager")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> NotifyAccountManager([FromRoute] Guid id,
            AccountManagerNotificationRequestDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.NotifyAccountManager(id, payload, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.NotifyAccountManager(id, payload, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (FlexChargeException e)
            {
                workspan.RecordFatalException(e);
                ModelState.AddModelError("General", e.Message);
                return BadRequest(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to notify account manager");
            }
        }

        [HttpPost("admin/application/{id:guid}/notify-applicant")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> NotifyApplicant([FromRoute] Guid id, ApplicantNotificationRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.NotifyApplicant(id, payload, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.NotifyApplicant(id, payload, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to notify applicant");
            }
        }

        [HttpPost("admin/application/{id:guid}/convert")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(ApplicationSubmitResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Convert([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>()
                .LogEnterAndExit();
            try
            {
                var convertOptions = new ApplicationConvertOptions
                {
                    IsStripeAppMerchant = false,
                    IsActive = true
                };
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _applicationService.ConvertAsync(id, null, convertOptions);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _applicationService.ConvertAsync(id, GetPID(), convertOptions);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                
                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }


        [HttpGet()] // GET ALL
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IPagedList<AplicationActivitiesResponseDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(string query, DateTime from, DateTime to,
            [FromQuery] ApplicationStatus?[] statuses,
            [FromQuery] OperationsStatus?[] operationsStatuses,
            [FromQuery] RiskAssessmentStatus?[] riskStatuses,
            string orderBy,
            string sortField,
            CancellationToken token,
            Guid? partnerId,
            int pageSize = 10,
            int pageNumber = 1)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                var response = new ApplicationQueryResponse();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.AdminGetAsync(query, from, to, statuses, operationsStatuses,
                        riskStatuses, orderBy, sortField,
                        pageSize, pageNumber, partnerId);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);

                    response = await _applicationService.PartnerGetAsync(GetPID(), isIntegrationPartner, query, from,
                        to, statuses, operationsStatuses, riskStatuses, orderBy,
                        sortField,
                        pageSize,
                        pageNumber);
                }
                else
                {
                    return StatusCode(StatusCodes.Status401Unauthorized, "Unauthorized");
                }

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpGet("summary")] // GET ALL
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IPagedList<ApplicationQueryResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSummary([FromQuery] Guid? pid)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                ApplicationSummaryResponse response;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _applicationService.AdminGetSummaryAsync(pid, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    response = await _applicationService.AdminGetSummaryAsync(GetPID(), null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    response = await _applicationService.AdminGetSummaryAsync(null, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status401Unauthorized, "Unauthorized");
                }

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpGet]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [Route("{id:guid}/activities")]
        [ProducesResponseType(typeof(AplicationActivitiesResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetActivityLog([FromRoute] Guid id)
        {
            using var workspan = Workspan.Start<OnboardingController>().LogEnterAndExit();
            try
            {
                IList<AplicationActivityDTO> activities;
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    activities = await _applicationService.GetApplicationActivitiesAsync(id, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    activities = await _applicationService.GetApplicationActivitiesAsync(id, GetPID());
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
                // var response = _mapper.Map<IPagedList<AplicationActivityDTO>, PagedDTO<AplicationActivityDTO>>(activities);
                return Ok(new {activities});
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }
        
        [HttpPost("invite")]
        [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(200)]
        public async Task<IActionResult> InviteApplicant(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData)
                .Baggage("ApplicationId", id)
                .LogEnterAndExit();

            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var application = await _dbContext.Applications
                    .Include(application => application.PrimaryContact)
                    .FirstOrDefaultAsync(x => x.Id == id, token);

                if (application == null)
                {
                    return NotFound();
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && application.PartnerId == GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    application.IntegrationPartnerId == GetPID())
                {
                    await _publisher.Publish<UserInviteRequestedEvent>(new
                    {
                        FirstName = application.PrimaryContact?.FirstName,
                        LastName = application.PrimaryContact?.LastName,
                        Phone = application.PrimaryContact?.Phone,
                        Email = application.PrimaryContact?.Email,
                        Mid = application.Id,
                        Pid = application.PartnerId,
                        UserId = application.PrimaryContact?.UserId,
                        Group = MerchantGroups.MERCHANT_ADMIN
                    }, token);
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Fatal(e, $"Failed to send invite applicant");
                throw;
            }
        }
    }
}