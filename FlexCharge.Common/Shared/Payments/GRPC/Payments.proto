syntax = "proto3";

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

package payments;

option csharp_namespace = "FlexCharge.Grpc.Payments";

service GrpcGreeter {
  rpc SayHello (HelloRequest) returns (HelloReply);
}

message HelloRequest {
  string name = 1;
}

message HelloReply {
  string message = 1;
}

// Common messages 

message SerializedRequest {
  string request = 1;
}

message SerializedResponse {
  string response = 1;
}


// Services

service GrpcPaymentsService {
  rpc FullyAuthorizePayment(SerializedRequest) returns (SerializedResponse);
  rpc CapturePayment(SerializedRequest) returns (SerializedResponse);
  rpc VoidPayment(SerializedRequest) returns (SerializedResponse);
  rpc ChargePayment(SerializedRequest) returns (SerializedResponse);
  rpc CanChargeExternalToken(SerializedRequest) returns (SerializedResponse);
}

