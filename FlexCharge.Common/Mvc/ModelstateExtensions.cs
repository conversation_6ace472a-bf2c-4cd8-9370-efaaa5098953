using FluentValidation.Results;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace FlexCharge.Common.Mvc;

public static class ModelStateExtensions
{
    public static void AddFluentValidationErrors(this ModelStateDictionary modelState,
        ValidationResult validationResult)
    {
        foreach (var error in validationResult.Errors)
        {
            modelState.AddModelError(error.PropertyName, error.ErrorMessage);
        }
    }
}