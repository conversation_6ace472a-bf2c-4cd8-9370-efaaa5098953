using System.Linq;
using System.Reflection;
using Amazon.S3;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Emails;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Reporting
{
    public static class Extensions
    {
        public static IServiceCollection AddReporting(this IServiceCollection services)
        {
            var reportTypes = Assembly.GetCallingAssembly()
                .DefinedTypes
                .Where(typeInfo =>
                    //typeInfo.GetCustomAttribute(typeof(ReportAttribute)) != null
                    typeInfo.ImplementedInterfaces.Contains(typeof(IReport))
                    && typeInfo.IsClass &&
                    !typeInfo.IsAbstract).ToList();


            var lookupTypes = Assembly.GetCallingAssembly()
                .DefinedTypes
                .Where(typeInfo =>
                    //typeInfo.GetCustomAttribute(typeof(ReportAttribute)) != null
                    typeInfo.ImplementedInterfaces.Contains(typeof(ILookupService))
                    && typeInfo.IsClass &&
                    !typeInfo.IsAbstract).ToList();


            //services.AddTransient<IEmailSender, SendGridEmailSender>();
            services.AddEmailClient();

            services.AddAWSService<IAmazonS3>();
            services.AddTransient<ICloudStorage, AmazonS3Storage>();

            var reportOptionsTypes = reportTypes
                .Select(ti => ti.AsType().BaseType.GenericTypeArguments[1]).ToList();

            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            services.AddBackgroundWorkerService(configuration);

            services.Configure<ReportingServiceOptions>(configuration.GetSection("reportingService"));
            services.AddScoped(typeof(IReportingService), typeof(ReportingService));


            RegisterReportOptions();
            RegisterReportTypes();
            RegisterLookupTypes();

            return services;


            void RegisterReportTypes()
            {
                foreach (var type in reportTypes)
                {
                    services.AddScoped(typeof(IReport), type);
                }
            }

            void RegisterReportOptions()
            {
                var baseMethod = typeof(OptionsConfigurationServiceCollectionExtensions)
                    .GetMethods()
                    .First(m => m.Name == nameof(OptionsConfigurationServiceCollectionExtensions.Configure)
                                && m.GetParameters().Length == 2);


                foreach (var type in reportOptionsTypes)
                {
                    var genericMethod = baseMethod.MakeGenericMethod(type)!;
                    genericMethod.Invoke(genericMethod, new object[] {services, configuration.GetSection(type.Name)});
                }
            }

            void RegisterLookupTypes()
            {
                foreach (var type in lookupTypes)
                {
                    services.AddScoped(typeof(ILookupService), type);
                }
            }
        }
    }
}