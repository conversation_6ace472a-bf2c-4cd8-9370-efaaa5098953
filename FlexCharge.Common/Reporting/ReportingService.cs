using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Emails;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace FlexCharge.Common.Reporting;

public class ReportingService : IReportingService
{
    private readonly IMapper _mapper;
    private readonly IEmailSender _emailSender;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
    private readonly IEnumerable<IReport> _reports;
    private IValidationDictionary _modelState;
    private readonly IServiceProvider _serviceProvider;

    private IOptions<ReportingServiceOptions> _options;


    public ReportingService(
        IMapper mapper,
        IEmailSender emailSender,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IEnumerable<IReport> reports,
        IOptions<ReportingServiceOptions> options, IServiceProvider serviceProvider)
    {
        _mapper = mapper;
        _emailSender = emailSender;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        _reports = reports;
        _options = options;
        _serviceProvider = serviceProvider;
    }

    private bool Validate(Guid reportId, DateTime? from, DateTime? to, string email,
        object parameter)
    {
        if (string.IsNullOrWhiteSpace(email)) _modelState.AddError(nameof(email), "Email is not set");
        if (to is null) _modelState.AddError(nameof(to), "Report end time must be specified");
        if (from is null) _modelState.AddError(nameof(from), "Report start time must be specified");
        if (to < from) _modelState.AddError(nameof(from), "Report start time must be before end time");

        var report = _reports.FirstOrDefault(r => r.ReportId == reportId);
        if (report == null)
        {
            _modelState.AddError(nameof(reportId), $" report with id  {reportId} not found");
        }
        else
        {
            if (!report.TryValidate(parameter, out var validator))
            {
                validator.Errors.ForEach(e => _modelState.AddError(nameof(parameter), e));
            }
        }

        return _modelState.IsValid;
    }


    public List<(string, Guid)> GetAvailableReportNamesAndIds() =>
        _reports.Select(r => (r.ReportName, r.ReportId)).ToList();


    public bool GenerateAndSendReport(Guid reportId, DateTime? from, DateTime? to,
        object param, IValidationDictionary validationDict, string email, out string message)
    {
        _modelState = validationDict;
        message = _options.Value.ResponseMessage;


        if (!Validate(reportId, from, to, email,
                param)) return false;


        from = from!.Value.ToUtcDate();
        to = to!.Value.ToUtcDate().AddHours(24).AddTicks(-1);

        var receiverName = email.Substring(0, email.IndexOf('@'));

        _backgroundWorkerCommandQueue.Enqueue(new GenerateReportAndSendCommand(reportId, from.Value, to.Value, email,
            receiverName, param));
        return true;
    }

    public async Task<bool> GenerateAndStoreReportAsync(
        Guid reportId,
        DateTime? from,
        DateTime? to,
        string path,
        string fileName,
        object param)
    {
        using var workspan = Workspan.Start<GenerateReportAndSendCommand>()
            .Baggage("reportId", reportId)
            .Baggage("from", from?.ToString("yyyy-MM-dd"))
            .Baggage("to", to?.ToString("yyyy-MM-dd"))
            .Baggage("path", path)
            .LogEnterAndExit();

        // In case any of the dates is not set, we set the default values
        from ??= DateTime.UtcNow.Date.AddDays(-1);
        to ??= DateTime.UtcNow.Date.AddTicks(-1);

        var activityService = _serviceProvider.GetService<IActivityService>();
        var report = _serviceProvider.GetServices<IReport>().First(s => s.ReportId == reportId);

        var reportCsv = await Generate(from, to, param, report, workspan, activityService);
        await Store(path, fileName, reportCsv);
        await Notify(reportCsv, report);

        return true;
    }

    private static async Task Notify(string reportCsv, IReport report)
    {
        // if (string.IsNullOrWhiteSpace(_email))
        // {
        //     workspan.Log.Information("No email provided, skipping email sending");
        // }
        // else
        // {
        //     workspan.Log.Information("Sending report to email {email}", _email);
        //     
        //     await SendReportToUserEmailAsync(
        //         serviceProvider.GetService<IEmailSender>(),
        //         reportCsv,
        //         _email,
        //         report.ReportName,
        //         report,
        //         _from,
        //         _to);
        // }
    }

    private static async Task<string> Generate(DateTime? from, DateTime? to, object param, IReport report,
        Workspan workspan,
        IActivityService activityService)
    {
        var reportCsv = await report.GenerateAsync(from.Value, to.Value, param);

        if (reportCsv == null)
        {
            workspan.Log.Fatal("Report Generation Failure");

            await activityService.CreateActivityAsync(
                Activities.ReportingErrorActivities.ReportGenerationFailure,
                set => set.Data(param));
        }

        workspan.Log.Information("Internal Report Generated");

        await activityService.CreateActivityAsync(
            Activities.InternalReportingActivities.InternalReporting_ReportGenerated,
            set => set.Data(param));

        return reportCsv;
    }

    private async Task Store(string path, string fileName, string reportCsv)
    {
        var cloudStorageService = _serviceProvider.GetService<ICloudStorage>();

        var fileNameWithExtension = $"{fileName}.csv";

        var bucket = $"sftp-gateway-{EnvironmentHelper.GetCurrentEnvironment()}".ToLower();

        var folderPath = path;

        var filePath = $"/{folderPath}/{fileNameWithExtension}";

        // Convert the CSV string to a stream
        using var reportStream = new MemoryStream(Encoding.UTF8.GetBytes(reportCsv));

        // Upload the stream to cloud storage
        await cloudStorageService.UploadFileAsync(reportStream, bucket, filePath);
    }


    #region Helper Methods

    private static string GenerateCsvReport<TRow>(IList<TRow> vintageReport)
    {
        String result;
        using (MemoryStream mem = new MemoryStream())
        {
            using (StreamWriter sw = new StreamWriter(mem))
            {
                CSVHelper.Create(vintageReport, sw);
            }

            var stringBytes = mem.ToArray();
            result = Encoding.UTF8.GetString(stringBytes, 0, (int) stringBytes.Length);
        }

        return result;
    }

    #endregion
}