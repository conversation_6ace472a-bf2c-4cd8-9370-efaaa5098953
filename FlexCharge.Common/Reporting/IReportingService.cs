using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FlexCharge.Common.Reporting;

public interface IReportingService
{
    public List<(string, Guid)> GetAvailableReportNamesAndIds();

    bool GenerateAndSendReport(Guid reportId, DateTime? from, DateTime? to,
        object param, IValidationDictionary validationDict, string email, out string message);

    //Task<bool> GenerateAndStoreReportAsync(Guid reportId, DateTime? from, DateTime? to, string path, object param);
    Task<bool> GenerateAndStoreReportAsync(Guid reportId, DateTime? from, DateTime? to, string path, string fileName,
        object param);
}