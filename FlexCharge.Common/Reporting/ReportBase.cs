using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper.Configuration;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace FlexCharge.Common.Reporting;

public abstract class ReportBase<TParam, TOptions, TModel> : IReport where TOptions : class, IReportOptions
{
    private readonly IOptions<TOptions> _options;
    protected DbContext _dbContext { get; set; }
    public IReportOptions Options => _options.Value;

    protected ReportValidator Validator { get; set; } = new();

    protected ReportBase(IOptions<TOptions> options, DbContext dbContext)
    {
        _dbContext = dbContext;
        _options = options;
    }

    public bool TryValidate(object parameter, out ReportValidator validator) =>
        TryValidate((TParam) parameter, out validator);

    protected abstract bool TryValidate(TParam parameter, out ReportValidator dict);

    #region Report Id and Name

    public virtual Guid ReportId => new Guid(_options.Value.ReportId);

    public virtual string ReportName => _options.Value.ReportName;

    #endregion

    #region Calculation and Formatting Helpers

    private static CultureInfo _invariantCulture = CultureInfo.InvariantCulture;


    protected static string CalculatePercent(float fraction, float total)
    {
        if (fraction >= 0 && total > 0)
        {
            return (fraction / total * 100).ToString("0.##", _invariantCulture) + "%";
        }
        else return null;
    }

    protected static string CalculatePercentOrZero(decimal fraction, decimal total)
    {
        if (fraction >= 0 && total > 0)
        {
            return (fraction / total * 100).ToString("0.##", _invariantCulture) + "%";
        }
        else return "0%";
    }

    protected static DateOnly GetDateOnly(DateTime dateTime)
    {
        var universalDateTime = dateTime.ToUniversalTime();
        return new DateOnly(universalDateTime.Year, universalDateTime.Month, universalDateTime.Day);
    }

    protected static string FormatDate(DateTime date)
    {
        return date.Date.ToString("yyyy/MM/dd");
    }

    #endregion

    #region Report Generation

    public abstract Task<List<TModel>> GenerateRowsAsync(DateTime from, DateTime to, TParam param,
        CancellationToken ctoken);

    public abstract IAsyncEnumerable<TModel> GenerateRowsYieldAsync(DateTime from, DateTime to, TParam param,
        CancellationToken ctoken);

    public async Task<string> GenerateAsync(
        DateTime startDate, DateTime endDate,
        object param)
    {
        return await GenerateReportAsync(startDate, endDate, (TParam) param);
    }

    protected virtual string GenerateCSVFromRows(IList<TModel> report)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            PrepareHeaderForMatch = args => args.Header.ToLower(),
        };

        String result;
        using (MemoryStream mem = new MemoryStream())
        {
            using (StreamWriter sw = new StreamWriter(mem))
            {
                CSVHelper.Create(report, sw, config);
            }

            var stringBytes = mem.ToArray();
            result = Encoding.UTF8.GetString(stringBytes, 0, (int) stringBytes.Length);
        }

        return result;
    }

    #endregion


    public async Task<string> GenerateReportAsync(
        DateTime startDate,
        DateTime endDate,
        TParam param)
    {
        using var workspan = Workspan.Start<ReportBase<TParam, TOptions, TModel>>()
            .Baggage("ReportId", ReportId.ToString())
            .Baggage("ReportName", ReportName)
            .Baggage("From", startDate.ToString("o"))
            .Baggage("To", endDate.ToString("o"))
            .Baggage("Param", param.ToString())
            .LogEnterAndExit();

        await using var trans = await _dbContext.Database.BeginTransactionAsync(IsolationLevel.RepeatableRead);

        try
        {
            var cts = new CancellationTokenSource();

            cts.CancelAfter(60000); // 1 minute timeout

            var rows = await GenerateRowsAsync(startDate, endDate, param, cts.Token);

            var retval = GenerateCSVFromRows(rows);

            await trans.CommitAsync();

            workspan.Log.Information("Generated report with {RowsCount} rows", rows?.Count);

            return retval;
        }
        catch (Exception e)
        {
            await trans.RollbackAsync();
            workspan.RecordError(e.Message);
            workspan.RecordError(e.InnerException?.Message);
            workspan.RecordError(e.StackTrace);
            workspan.Log.Error(e, "Error generating report");
            throw;
        }
    }
}