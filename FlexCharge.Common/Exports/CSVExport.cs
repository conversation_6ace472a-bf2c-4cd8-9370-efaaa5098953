using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using FlexCharge.Utils;

namespace FlexCharge.Common.Exports;

public class CSVExport
{
    private static CsvConfiguration GetCsvConfiguration()
    {
        return new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            PrepareHeaderForMatch = args => args.Header.ToLower(),
        };
    }

    public static string GenerateCSVFromRows<TRow>(IList<TRow> list)
    {
        using var memoryStream = new MemoryStream();
        using (var streamWriter = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
        {
            CSVHelper.Create(list, streamWriter, GetCsvConfiguration());
            streamWriter.Flush();
        }

        return Encoding.UTF8.GetString(memoryStream.ToArray());
    }

    public static Stream GenerateCSVStreamFromRows<TRow>(IList<TRow> list)
    {
        var memoryStream = new MemoryStream();
        using (var streamWriter = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
        using (var csvWriter = new CsvWriter(streamWriter, GetCsvConfiguration()))
        {
            csvWriter.WriteRecords(list);
            streamWriter.Flush();
        }

        memoryStream.Seek(0, SeekOrigin.Begin); // Reset the stream position to the beginning
        return memoryStream;
    }

    public static string GenerateCSVFromRows<TRow, TMap>(IList<TRow> list)
        where TMap : ClassMap<TRow>
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            // Example: force headers to lowercase for matching
            PrepareHeaderForMatch = args => args.Header.ToLower(),
        };

        using var mem = new MemoryStream();
        using var sw = new StreamWriter(mem, Encoding.UTF8);
        using var csv = new CsvWriter(sw, config);

        // Register the custom class map so CsvHelper knows how to map TRow
        csv.Context.RegisterClassMap<TMap>();

        // Write records using that map
        csv.WriteRecords(list);

        // Make sure everything is flushed out to the MemoryStream
        sw.Flush();

        // Convert stream contents to string
        var stringBytes = mem.ToArray();
        return Encoding.UTF8.GetString(stringBytes, 0, stringBytes.Length);
    }
}