using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace FlexCharge.Common.Sms
{
    public class SmsRequest
    {
        public string Code { get; set; }
        public string PhoneNumber { get; set; }
        public string Message { get; set; }
        public bool ShortenUrls { get; set; }
    }

    public class WAPPRequest
    {
        public string PhoneNumber { get; set; }
        public string Message { get; set; }
    }

    public class VoiceRequest
    {
        public string carrier { get; set; }
        public bool is_cellphone { get; set; }
        public string message { get; set; }
        public int seconds_to_expire { get; set; }
        public string uuid { get; set; }
        public bool success { get; set; }
    }

    public class TwilioSmsServices : ISmsServices
    {
        private readonly SmsOptions _smsOptions;

        public TwilioSmsServices(IOptions<SmsOptions> smsOptions)
        {
            _smsOptions = smsOptions.Value;
        }

        public async Task<MessageResource> SendSmsAsync(SmsRequest model, string senderPhoneOverride)
        {
            using var workspan = Workspan.Start<TwilioSmsServices>()
                .Baggage("Model", model)
                .LogEnterAndExit();

            // _logger.LogInformation($"ENTERED: ISmsService => SendSms => Payload:{JsonConvert.SerializeObject(model)}");
            // _logger.LogDebug($"IN: ISmsService => SendSms => Options:{JsonConvert.SerializeObject(_smsOptions)}");

            var accountSid = _smsOptions.SID;
            var authToken = _smsOptions.Token;

            //We can make this dynamic once relevant
            var from = senderPhoneOverride ?? _smsOptions.TwilioPhone;
            var companyName = _smsOptions.TwilioCompanyName;
            var serviceSID = _smsOptions.ServiceSid;

            try
            {
                TwilioClient.Init(accountSid, authToken);

                var toNumber = new PhoneNumber(model.PhoneNumber);
                var fromNumber = new PhoneNumber(from);
                var message = await MessageResource.CreateAsync(
                    to: toNumber,
                    from: fromNumber,
                    body: model.Message,
                    messagingServiceSid: serviceSID,
                    shortenUrls: model.ShortenUrls
                );

                workspan
                    .Response(message);

                //_logger.LogInformation($"EXIT: ISmsService => SendSms => Payload:{message.Status}");
                return message;
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex);
                throw;
            }
        }

        // Needs to be rewritten to log results of each message
        // public async Task SendBulkSms(IEnumerable<SmsRequest> bulk)
        // {
        //     using var workspan = Workspan.Start<SmsServices>()
        //         .Baggage("Bulk", bulk)
        //         .LogEnterAndExit();
        //     
        //     // _logger.LogInformation($"ENTERED: ISmsService => SendSms => Payload:{JsonConvert.SerializeObject(bulk)}");
        //     // _logger.LogInformation($"IN: ISmsService => SendSms => Options:{JsonConvert.SerializeObject(_smsOptions)}");
        //
        //     string accountSid = _smsOptions.SID;
        //     string authToken = _smsOptions.Token;
        //     string companyName = _smsOptions.TwilioCompanyName;
        //     string serviceSID = _smsOptions.ServiceSid;
        //
        //     try
        //     {
        //         TwilioClient.Init(accountSid, authToken);
        //
        //         foreach (var item in bulk)
        //         {
        //             var message = SendSms(item);
        //             
        //             workspan.Log.Information($"ISmsService => SendSms => Payload: {message}");
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex,
        //             $"EXCEPTION: ISmsService => SendSms => Payload:{JsonConvert.SerializeObject(bulk)}");
        //         throw;
        //     }
        // }

        public async Task<MessageResource> SendWhatsappAsync(WAPPRequest model)
        {
            try
            {
                string accountSid = _smsOptions.SID;
                string authToken = _smsOptions.Token;
                string companyName = _smsOptions.TwilioCompanyName;
                string whatsappPhone = _smsOptions.WhatsappPhone;

                TwilioClient.Init(accountSid, authToken);

                var from = new PhoneNumber($"whatsapp:{whatsappPhone}");
                var to = new PhoneNumber($"whatsapp:{model.PhoneNumber}");

                var message = await MessageResource.CreateAsync(
                    to,
                    body: model.Message,
                    from: from //messagingServiceSid: srviceSID
                );

                return message;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> SendSmsORVoiceAsync(string phoneNumber, string countryCode, string locale,
            string via = "sms")
        {
            try
            {
                // TODO: Optimize using connection pool
                // see: https://learn.microsoft.com/en-us/dotnet/fundamentals/networking/http/httpclient-guidelines
                using var client = new HttpClient();
                var AuthyAPIKey = _smsOptions.TwilioAuthyAPIKey;

                client.DefaultRequestHeaders.Add("X-Authy-API-Key", AuthyAPIKey);
                FormUrlEncodedContent requestContent = CreateRequestContent(phoneNumber, countryCode, locale, via);
                HttpResponseMessage response =
                    await client.PostAsync(_smsOptions.TwilioVoiceSmsStartUrl, requestContent);
                HttpContent responseContent = response.Content;
                using (var reader = new StreamReader(await responseContent.ReadAsStreamAsync()))
                {
                    var twilioVoiceSmsModel =
                        JsonConvert.DeserializeObject<VoiceRequest>(await reader.ReadToEndAsync());
                    if (twilioVoiceSmsModel.success)
                    {
                        return twilioVoiceSmsModel.success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// function generate random 6 digit number 
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        public string GenerateRandomVerificationCode(int length)
        {
            Random random = new Random();
            const string chars = "0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        /// <summary>
        /// function to create the request content to send the sms
        /// </summary>
        /// <param name="phoneNumber"> </param>
        /// <param name="countryCode"></param>
        /// <param name="locale"></param>
        /// <param name="via"></param>
        /// <returns></returns>
        private FormUrlEncodedContent CreateRequestContent(string phoneNumber, string countryCode, string locale,
            string via = "sms")
        {
            try
            {
                var requestContent = new FormUrlEncodedContent(new[]
                {
                    //how the sms is send to client the value can be call or sms
                    new KeyValuePair<string, string>("via", via),
                    //the language to send the sms code
                    new KeyValuePair<string, string>("locale", locale),
                    //the length of the sms code, can be from 4-10 digits
                    new KeyValuePair<string, string>("code_length", "6"),
                    //the user phone number to send his phone number
                    new KeyValuePair<string, string>("phone_number", phoneNumber),
                    //the country code
                    new KeyValuePair<string, string>("country_code", countryCode)
                });
                return requestContent;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}