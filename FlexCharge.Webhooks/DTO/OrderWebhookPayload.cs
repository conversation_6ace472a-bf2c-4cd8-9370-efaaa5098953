using System;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.DTO;

public class OrderWebhookPayload
{
    public OrderWebhookPayload(
        string @event,
        DateTime timeStamp,
        Guid? mid,
        Guid? pid,
        string externalOrderId,
        Guid? orderId,
        string? confirmationId,
        object? eventData,
        bool isTestMode,
        bool isResent)
    {
        IdempotencyKey = Guid.NewGuid();

        Event = @event;
        TimeStamp = timeStamp;

        Mid = mid;
        Pid = pid;

        ExternalOrderId = externalOrderId;
        OrderId = orderId;
        ConfirmationId = confirmationId;

        IsTestMode = isTestMode;
        IsResent = isResent;

        if (eventData != null)
        {
            EventData = eventData;
        }
    }

    public Guid IdempotencyKey { get; set; }

    public string Event { get; }
    public DateTime TimeStamp { get; }

    public object? EventData { get; }

    public Guid? Mid { get; }
    public Guid? Pid { get; }

    public string ExternalOrderId { get; }
    public Guid? OrderId { get; }
    public string? ConfirmationId { get; }

    public bool IsTestMode { get; }
    public bool IsResent { get; }
}