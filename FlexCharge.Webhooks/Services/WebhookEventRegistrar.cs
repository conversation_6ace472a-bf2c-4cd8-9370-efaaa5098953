using System;
using System.Linq;
using System.Reflection;
using FlexCharge.Common.Webhooks.Outgoing;
using FlexCharge.Webhooks.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Webhooks.Services;

public static class WebhookEventRegistrationExtensions
{
    public static IServiceCollection AddWebhookEventsFromAssembly(
        this IServiceCollection services,
        Assembly assembly)
    {
        using var serviceProvider = services.BuildServiceProvider();
        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();

        // var eventTypes = Assembly.GetCallingAssembly()
        //     .DefinedTypes
        //     .Where(typeInfo =>
        //         //typeInfo.GetCustomAttribute(typeof(ReportAttribute)) != null
        //         typeInfo.ImplementedInterfaces.Contains(typeof(IOutgoingWebhookEvent))
        //         && typeInfo.IsClass &&
        //         !typeInfo.IsAbstract).ToList();

        // foreach (var type in eventTypes)
        // {
        //     var instance = (IOutgoingWebhookEvent)Activator.CreateInstance(type)!;
        //
        //     bool exists = db.Events.Any(e => e.EventName == instance.Name);
        //     if (!exists)
        //     {
        //         db.Events.Add(new Event
        //         {
        //            EventName = instance.Name,
        //            Description = instance.Description
        //         });
        //     }
        // }

        db.SaveChanges();

        return services;
    }
}