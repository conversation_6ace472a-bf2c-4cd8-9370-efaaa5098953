using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ConsumerChallengeAttemptedEventConsumer : IdempotentEventConsumer<ConsumerChallengeAttemptedEvent>
{
    private IWebhookService _webhookService;

    public ConsumerChallengeAttemptedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ConsumerChallengeAttemptedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .Log.Information("CONSUMER CHALLENGE ATTEMPTED");

        try
        {
            var consumerChallengeAttemptedEvent = message;
            string eventName = $"challenge.attempted";

            var eventData = new ChallengeEventData(
                consumerChallengeAttemptedEvent.Timestamp,
                consumerChallengeAttemptedEvent.OrderId,
                consumerChallengeAttemptedEvent.MerchantOrderId
            );


            var webhookPayload = new OrderWebhookPayload(
                eventName,
                DateTime.UtcNow,
                consumerChallengeAttemptedEvent.Mid,
                consumerChallengeAttemptedEvent.Pid,
                consumerChallengeAttemptedEvent.MerchantOrderId,
                consumerChallengeAttemptedEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(consumerChallengeAttemptedEvent.Mid,
                consumerChallengeAttemptedEvent.Pid,
                null,
                eventName,
                payload,
                consumerChallengeAttemptedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}