// using System;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Contracts;
// using FlexCharge.Orders.Entities;
// using FlexCharge.Orders.Services;
// using FlexCharge.Webhooks.Services;
// using MassTransit;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Orders.Consumers;
//
// public class OrderEligibilityStatusUpdatedEventConsumer : IConsumer<EligibilityOfferStateUpdatedEvent>
// {
//     ILogger<OrderEligibilityStatusUpdatedEventConsumer> _logger;
//     private IWebhookService _webhookService;
//
//     private IMapper _mapper;
//
//     public OrderEligibilityStatusUpdatedEventConsumer(ILogger<OrderEligibilityStatusUpdatedEventConsumer> logger,
//         IWebhookService webhookService)
//     {
//         _logger = logger;
//         _webhookService = webhookService;
//     }
//
//     public async Task Consume(ConsumeContext<EligibilityOfferStateUpdatedEvent> context)
//     {
//         try
//         {
//             _logger.LogInformation(
//                 $"ENTERED: Webhooks > OrderEligibilityStatusUpdatedEventConsumer > payload: {JsonConvert.SerializeObject(context)}");
//
//             string event_name = $"order.{context.Message.StatusCategory.ToLower().Trim()}";
//             await _webhookService.InvokeWebhookAsync(context.Message.Mid, event_name, context.Message.UpdatedOrder,
//                 context.CorrelationId);
//         }
//         catch (Exception e)
//         {
//             _logger.LogError(e, $"EXCEPTION: OrderEligibilityStatusUpdatedEventConsumer => Unable to invoke webhook");
//         }
//     }
// }

