// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.MassTransit;
// using FlexCharge.Common.RuntimeEnvironment;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Webhooks.DTO;
// using FlexCharge.Webhooks.Services;
// using MassTransit;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Webhooks.Consumers;
//
// public class MITOrderExpiredEventConsumer : IdempotentEventConsumer<MITOrderExpiredEvent>
// {
//     private IWebhookService _webhookService;
//
//     public MITOrderExpiredEventConsumer(IWebhookService webhookService,
//         IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
//     {
//         _webhookService = webhookService;
//     }
//
//     protected override async Task ConsumeEvent(MITOrderExpiredEvent message, CancellationToken cancellationToken)
//     {
//         Workspan
//             .Baggage("Mid", message.Mid)
//             .Baggage("OrderId", message.OrderId)
//             .Log.Information("MIT ORDER EXPIRED");
//         
//         try
//         {
//             var mitOrderExpiredEvent = message;
//             string eventName = $"order.expired";
//
//             object eventData = null;
//
//             var webhookPayload = new WebhookPayload(
//                 eventName,
//                 DateTime.UtcNow,
//                 mitOrderExpiredEvent.ExternalOrderId,
//                 mitOrderExpiredEvent.OrderId, 
//                 null,
//                 eventData,
//                 !EnvironmentHelper.IsInProduction, 
//                 false);
//
//             var payload = JsonConvert.SerializeObject(webhookPayload);
//
//             await _webhookService.InvokeWebhookAsync(mitOrderExpiredEvent.Mid, eventName, payload, mitOrderExpiredEvent.OrderId);
//         }
//         catch (Exception e)
//         {
//             Workspan.RecordException(e, $"Unable to invoke webhook");
//         }
//     }
//
// }

