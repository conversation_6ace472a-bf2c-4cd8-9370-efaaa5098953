using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ConsumerChallengeFailedEventConsumer : IdempotentEventConsumer<ConsumerChallengeFailedEvent>
{
    private IWebhookService _webhookService;

    public ConsumerChallengeFailedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ConsumerChallengeFailedEvent message,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ConsumerChallengeFailedEventConsumer>()
            .Tag("Message", JsonConvert.SerializeObject(message))
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId);

        workspan.Log.Information("webhooks > ConsumerChallengeFailedEventConsumer");

        try
        {
            var consumerChallengeFailedEvent = message;
            string eventName = $"challenge.failed";

            var eventData = new ChallengeEventData(
                consumerChallengeFailedEvent.Timestamp,
                consumerChallengeFailedEvent.OrderId,
                consumerChallengeFailedEvent.MerchantOrderId
            );


            var webhookPayload = new OrderWebhookPayload(
                eventName,
                DateTime.UtcNow,
                consumerChallengeFailedEvent.Mid,
                consumerChallengeFailedEvent.Pid,
                consumerChallengeFailedEvent.MerchantOrderId,
                consumerChallengeFailedEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(consumerChallengeFailedEvent.Mid,
                consumerChallengeFailedEvent.Pid,
                null,
                eventName,
                payload,
                consumerChallengeFailedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}