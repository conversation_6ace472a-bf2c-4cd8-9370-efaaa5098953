using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class OrderUpdatedEventConsumer : ConsumerBase<OrderUpdatedEvent>
{
    private IWebhookService _webhookService;

    public OrderUpdatedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeMessage(OrderUpdatedEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .Baggage("StatusCategory", message.StatusCategory)
            .Log.Information("ORDER UPDATED");

        try
        {
            var orderUpdatedEvent = message;
            string event_name = $"order.{orderUpdatedEvent.StatusCategory.ToLower().Trim()}";

            object eventData = null;

            var webhookPayload = new OrderWebhookPayload(
                event_name,
                DateTime.UtcNow,
                orderUpdatedEvent.Mid,
                orderUpdatedEvent.Pid,
                orderUpdatedEvent.ExternalOrderId,
                orderUpdatedEvent.OrderId,
                orderUpdatedEvent.ConfirmationId,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(orderUpdatedEvent.Mid,
                orderUpdatedEvent.Pid,
                null,
                event_name,
                payload,
                orderUpdatedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, "Unable to invoke webhook");
        }
    }
}