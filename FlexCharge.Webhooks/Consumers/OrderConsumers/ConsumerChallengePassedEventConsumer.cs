using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ConsumerChallengePassedEventConsumer : IdempotentEventConsumer<ConsumerChallengePassedEvent>
{
    private IWebhookService _webhookService;

    public ConsumerChallengePassedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ConsumerChallengePassedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .Log.Information("CONSUMER CHALLENGE PASSED");

        try
        {
            var consumerChallengePassedEvent = message;
            string eventName = $"challenge.passed";

            var eventData = new ChallengeEventData(
                consumerChallengePassedEvent.Timestamp,
                consumerChallengePassedEvent.OrderId,
                consumerChallengePassedEvent.MerchantOrderId
            );


            var webhookPayload = new OrderWebhookPayload(
                eventName,
                DateTime.UtcNow,
                consumerChallengePassedEvent.Mid,
                consumerChallengePassedEvent.Pid,
                consumerChallengePassedEvent.MerchantOrderId,
                consumerChallengePassedEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(consumerChallengePassedEvent.Mid,
                consumerChallengePassedEvent.Pid,
                null,
                eventName,
                payload,
                consumerChallengePassedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}