// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.MassTransit;
// using FlexCharge.Common.RuntimeEnvironment;
// using FlexCharge.Contracts;
// using FlexCharge.Webhooks.DTO;
// using FlexCharge.Webhooks.Services;
// using Microsoft.Extensions.DependencyInjection;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Webhooks.Consumers;
//
// public class OrderCancelledEventConsumer : ConsumerBase<OrderCancelledEvent>
// {
//     private IWebhookService _webhookService;
//
//     public OrderCancelledEventConsumer(IWebhookService webhookService,
//         IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
//     {
//         _webhookService = webhookService;
//     }
//
//     protected override async Task ConsumeMessage(OrderCancelledEvent message, CancellationToken cancellationToken)
//     {
//         Workspan
//             .Baggage("Mid", message.Mid)
//             .Baggage("OrderId", message.OrderId)
//             .Log.Information("ORDER UPDATED");
//         
//         try
//         {
//             var orderCancelledEvent = message;
//             string eventName = $"order.cancelled";
//
//             object eventData = null;
//
//             var webhookPayload = new WebhookPayload(
//                 eventName,
//                 DateTime.UtcNow,
//                 orderCancelledEvent.ExternalOrderId,
//                 orderCancelledEvent.OrderId, 
//                 null,
//                 eventData,
//                 !EnvironmentHelper.IsInProduction, 
//                 false);
//
//             var payload = JsonConvert.SerializeObject(webhookPayload);
//
//             await _webhookService.InvokeWebhookAsync(orderCancelledEvent.Mid, eventName, payload, orderCancelledEvent.OrderId);
//         }
//         catch (Exception e)
//         {
//             Workspan.RecordException(e, "Unable to invoke webhook");
//         }
//     }
//
// }

