using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class OrderReversedEventConsumer : IdempotentEventConsumer<OrderReversedEvent>
{
    private IWebhookService _webhookService;

    public OrderReversedEventConsumer(IWebhookService webhookService, IServiceScopeFactory serviceScopeFactory) : base(
        serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(OrderReversedEvent message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<OrderReversedEventConsumer>()
            .Tag("Message", JsonConvert.SerializeObject(message))
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId);

        workspan.Log.Information("webhooks > OrderReversedEventConsumer");

        try
        {
            var OrderReversedEvent = message;
            string event_name = $"payment.chargeback.reversed";

            var eventData = new ReverseEventData
            (
                OrderReversedEvent.DisputeDateTime,
                OrderReversedEvent.TransactionId,
                OrderReversedEvent.Amount,
                OrderReversedEvent.Currency
            );


            var webhookPayload = new OrderWebhookPayload(
                event_name,
                DateTime.UtcNow,
                OrderReversedEvent.Mid,
                OrderReversedEvent.Pid,
                null,
                OrderReversedEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(OrderReversedEvent.Mid,
                OrderReversedEvent.Pid,
                null,
                event_name,
                payload,
                OrderReversedEvent.OrderId);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}