using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class PaymentPreDisputeCreditedConsumer : IdempotentEventConsumer<PaymentPreDisputeCreditedEvent>
{
    private IWebhookService _webhookService;

    public PaymentPreDisputeCreditedConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(PaymentPreDisputeCreditedEvent message,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<PaymentPreDisputeCreditedConsumer>()
            .Tag("Message", JsonConvert.SerializeObject(message))
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId);

        workspan.Log.Information("Webhooks > PaymentPreDisputeCreditedConsumer");

        try
        {
            var orderChargedBackEvent = message;
            string event_name = $"payment.chargeback.received";

            var eventData = new ChargebackEventData
            (
                orderChargedBackEvent.RequestDate,
                orderChargedBackEvent.Reason,
                orderChargedBackEvent.TransactionId,
                orderChargedBackEvent.InitialTransactionReference,
                orderChargedBackEvent.Amount,
                orderChargedBackEvent.Currency
            );


            var webhookPayload = new OrderWebhookPayload(
                event_name,
                DateTime.UtcNow,
                orderChargedBackEvent.Mid,
                orderChargedBackEvent.Pid,
                null,
                orderChargedBackEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(orderChargedBackEvent.Mid,
                orderChargedBackEvent.Pid,
                null,
                event_name,
                payload,
                orderChargedBackEvent.OrderId);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}