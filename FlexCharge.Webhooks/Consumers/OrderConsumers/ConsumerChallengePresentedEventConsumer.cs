using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ConsumerChallengePresentedEventConsumer : IdempotentEventConsumer<ConsumerChallengePresentedEvent>
{
    private IWebhookService _webhookService;

    public ConsumerChallengePresentedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ConsumerChallengePresentedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .Log.Information("CONSUMER CHALLENGE PRESENTED");

        try
        {
            var consumerChallengePresentedEvent = message;
            string eventName = $"challenge.presented";

            var eventData = new ChallengeEventData(
                consumerChallengePresentedEvent.Timestamp,
                consumerChallengePresentedEvent.OrderId,
                consumerChallengePresentedEvent.MerchantOrderId
            );


            var webhookPayload = new OrderWebhookPayload(
                eventName,
                DateTime.UtcNow,
                consumerChallengePresentedEvent.Mid,
                consumerChallengePresentedEvent.Pid,
                consumerChallengePresentedEvent.MerchantOrderId,
                consumerChallengePresentedEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(consumerChallengePresentedEvent.Mid,
                consumerChallengePresentedEvent.Pid,
                null,
                eventName,
                payload,
                consumerChallengePresentedEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}