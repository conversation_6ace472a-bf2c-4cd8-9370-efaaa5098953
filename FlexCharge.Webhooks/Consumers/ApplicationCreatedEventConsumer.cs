using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ApplicationCreatedEventConsumer : IdempotentEventConsumer<ApplicationCreatedEvent>
{
    private IWebhookService _webhookService;

    public ApplicationCreatedEventConsumer(IWebhookService webhookService, IServiceScopeFactory serviceScopeFactory) :
        base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ApplicationCreatedEvent message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ApplicationCreatedEvent>()
            .Tag("Message", JsonConvert.SerializeObject(message))
            .Baggage("Mid", message.Mid);

        try
        {
            var webhookEvent = message;

            string event_name = $"application.created";

            var webhookPayload = new WebhookPayload(
                event_name,
                DateTime.UtcNow,
                webhookEvent.Mid,
                webhookEvent.Pid,
                message,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            // Check subscriber & validate subscriber can see this webhook
            // What will prevent a subscriber receive this webhook


            bool sentSuccessfully =
                await _webhookService.InvokeWebhookAsync(
                    webhookEvent.Mid,
                    webhookEvent.Pid,
                    webhookEvent.IntegrationPartnerId,
                    event_name,
                    payload,
                    webhookEvent.Id);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}