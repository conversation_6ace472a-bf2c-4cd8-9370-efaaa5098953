using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class MerchantCreatedEventConsumer : IdempotentEventConsumer<MerchantCreatedEvent>
{
    private IWebhookService _webhookService;

    public MerchantCreatedEventConsumer(IWebhookService webhookService, IServiceScopeFactory serviceScopeFactory) :
        base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(MerchantCreatedEvent message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ApplicationSubmittedEvent>()
            .Tag("Message", JsonConvert.SerializeObject(message));

        try
        {
            var webhookEvent = message;

            string event_name = $"merchant.created";

            var webhookPayload = new WebhookPayload(
                event_name,
                DateTime.UtcNow,
                webhookEvent.MerchantId,
                webhookEvent.PartnerId,
                new MerchantCreatedEventDTO
                {
                    MerchantId = webhookEvent.MerchantId,
                    PartnerId = webhookEvent.PartnerId,
                    IntegrationPartnerId = webhookEvent.IntegrationPartnerId,
                    Dba = webhookEvent.Dba,
                    LegalEntityName = webhookEvent.LegalEntityName,
                    Mcc = webhookEvent.Mcc
                },
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully =
                await _webhookService.InvokeWebhookAsync(
                    webhookEvent.MerchantId,
                    webhookEvent.PartnerId,
                    webhookEvent.IntegrationPartnerId,
                    event_name,
                    payload,
                    webhookEvent.MerchantId);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}