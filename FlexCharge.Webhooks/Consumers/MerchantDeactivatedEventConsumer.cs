using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class MerchantDeactivatedEventConsumer : IdempotentEventConsumer<MerchantDeactivatedEvent>
{
    private IWebhookService _webhookService;

    public MerchantDeactivatedEventConsumer(IWebhookService webhookService, IServiceScopeFactory serviceScopeFactory) :
        base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(MerchantDeactivatedEvent message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ApplicationSubmittedEvent>()
            .Tag("Message", JsonConvert.SerializeObject(message));

        try
        {
            var webhookEvent = message;

            string event_name = $"merchant.deactivated";

            var webhookPayload = new WebhookPayload(
                event_name,
                DateTime.UtcNow,
                webhookEvent.Mid,
                webhookEvent.PartnerId,
                message,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully =
                await _webhookService.InvokeWebhookAsync(
                    webhookEvent.Mid,
                    webhookEvent.PartnerId,
                    webhookEvent.IntegrationPartnerId,
                    event_name,
                    payload,
                    webhookEvent.Mid);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}