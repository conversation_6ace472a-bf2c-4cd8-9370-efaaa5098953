namespace FlexCharge.Contracts;

public record MerchantCreateOrUpdateEventBase : IdempotentEvent
{
    public string Dba { get; set; }

    public Guid MerchantId { get; set; }
    public Guid? PartnerId { get; set; }
    public Guid? AccountId { get; set; }
    public Guid? IntegrationPartnerId { get; set; }

    public string Currency { get; set; }

    public bool Pcidss { get; set; }

    //public string PayoutFrequency { get; set; }
    public string IntegrationType { get; set; }
    public string CompanyName { get; set; }
    public string LegalEntityName { get; set; }
    public string Mcc { get; set; }

    public int? RiskLevel { get; set; }
    public int? RiskLevel_Visa { get; set; }
    public int? RiskTier { get; set; }

    public decimal? GhostModeThrottlePercentage { get; set; }
    public int? OfferRequestsRateLimitIntervalMS { get; set; }
    public int? OfferRequestsRateLimitCount { get; set; }
    public int? OfferRequestsMaxPerDay { get; set; }
    public decimal? OfferRequestsThrottlePercentage { get; set; }

    /// <summary>
    ///  % of NSF declines FlexCharge is willing to pass on evaluate
    /// </summary>
    public decimal? Offer_NSF_RequestsThrottle_Percentage { get; set; }

    /// <summary>
    /// Max value of offers FlexCharge is willing to approve and payout per month (In Cents)
    /// </summary>
    public int? Orders_MaxMonthlyAmount { get; set; }

    public bool IsBureauProductionActive { get; set; }
    public bool IsMitEnabled { get; set; }
    public bool IsMitEvaluateAsync { get; set; }
    public bool IsCitEvaluateAsync { get; set; }

    public bool PayoutsEnabled { get; set; }
    public bool IsActive { get; set; }
    public bool IsLocked { get; set; }
    public string CustomerSupportName { get; set; }
    public string CustomerSupportEmail { get; set; }
    public string CustomerSupportPhone { get; set; }
    public string CustomerSupportLink { get; set; }
    public string? Descriptor { get; set; }
    public string? Descriptor_Phone { get; set; }
    public string? Descriptor_Address { get; set; }
    public string? Descriptor_City { get; set; }
    public string? Descriptor_State { get; set; }
    public string? Descriptor_Postal { get; set; }
    public string? Descriptor_Country { get; set; }
    public string? Descriptor_Mcc { get; set; }
    public string? Descriptor_Merchant_Id { get; set; }
    public string? Descriptor_Url { get; set; }

    public string AchReceivingAccountBankName { get; set; }
    public string AchReceivingAccountType { get; set; }
    public string AchReceivingAccountNumber { get; set; }
    public string AchReceivingRoutingNumber { get; set; }
    public string AchReceivingAccountCurrency { get; set; }
    public DateTime? AchBankAccountVerified { get; set; }

    public Guid? FinancialAccountId { get; set; }
    public IEnumerable<MerchantFee> MerchantFeeHistory { get; set; }

    public int TransactionBaseFee { get; set; }
    public string? TransactionBaseFeeType { get; set; }

    public int TransactionFee { get; set; }
    public string? TransactionFeeType { get; set; }

    public int ChargebackFee { get; set; }
    public string? ChargebackFeeType { get; set; }

    public int RefundFee { get; set; }
    public string? RefundFeeType { get; set; }
    public bool VirtualTerminalEnabled { get; set; }
    public bool BillingInformationOptional { get; set; }

    public bool MITConsumerCuresEnabled { get; set; }
    public bool MITGetSiteByDynamicDescriptorEnabled { get; set; }
    public bool MITConsumerNotificationsEnabled { get; set; }
    public bool CITConsumerNotificationsEnabled { get; set; }

    public bool CITClickToRefundEnabled { get; set; }
    public bool MITClickToRefundEnabled { get; set; }
    public int? MITAgreedExpiryHours { get; set; }
    public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; }
    public bool IsSenseJsOptional { get; set; }

    public bool? UIWidgetOptional { get; set; }


    public bool AllowBinCheckOnTokenization { get; set; }
    public bool EnableGlobalNetworkTokenization { get; set; }

    public int RiskFee { get; set; }
    public string? RiskFeeType { get; set; }

    public bool AccountUpdaterEnabled { get; set; }
    public bool Global3DSEnabled { get; set; }
    public bool InformationalOnly3DS { get; set; }

    public int MinOrderAmount { get; set; }
    public int MaxOrderAmount { get; set; }
    public bool IsCrawlingEnabled { get; set; }
    public bool IsIframeMessagesCollectEnabled { get; set; }
    public bool IsEnforceMFAEnabled { get; set; }
    public bool CaptureRequired { get; set; }
    public decimal? DynamicAuthorizationDiscountThrottlePercentage { get; set; }
    public string? ConsumerOrderNotificationChannel { get; set; }
    public bool SchemeTransactionIdEnabled { get; set; }
    public bool MITImmediateRetryEnabled { get; set; }

    public bool IsAvsRequired { get; set; }
    public bool IsCvvRequired { get; set; }

    public List<string> SupportedCountries { get; set; }

    public bool IgnoreSiteIdFromClient { get; set; }
    public bool PayerEnabled { get; set; }

    public bool RedactIpEnabled { get; set; }

    public Guid? EligibilityStrategyWorkflowId { get; set; }
    public Guid? NotEligibleOrderProcessingWorkflowId { get; set; }
    public Guid? NotEligibleEverOrderProcessingWorkflowId { get; set; }
    public Guid? RecyclingStrategyWorkflowId { get; set; }
}