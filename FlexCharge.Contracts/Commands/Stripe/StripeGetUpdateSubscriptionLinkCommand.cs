namespace FlexCharge.Contracts.Commands;

public record StripeGetUpdateSubscriptionLinkCommand(
    string StripeAccountId,
    Guid Mid,
    Guid OrderId,
    string InvoiceId
) : ICorrelatedMessage
{
    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}

public record StripeGetUpdateSubscriptionLinkCommandResponse(
    string BillingPortalLink
);