using System;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Services.StripeReportsService;
using Microsoft.Extensions.Options;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Eligibility.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class AdministratorToolsController : BaseController
    {
        private readonly IPublishEndpoint _publisher;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IStripeReportsService _stripeReportsService;

        private readonly AppOptions _globalData;

        public AdministratorToolsController(IOptions<AppOptions> globalData,
            IPublishEndpoint publisher, IHttpContextAccessor httpContextAccessor,
            IStripeReportsService stripeReportsService)
        {
            _publisher = publisher;
            _httpContextAccessor = httpContextAccessor;
            _stripeReportsService = stripeReportsService;
            _globalData = globalData.Value;
        }

        [HttpPost("force-mit-off-session-retry")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<ActionResult> ForceMitOffSessionRetry(Guid orderId)
        {
            var workspan = Workspan.StartEndpoint<AdministratorToolsController>(this, null, _globalData);

            try
            {
                await _publisher.Publish(new RetryOffSessionOfferCommand(orderId)
                {
                    ForceRetry = true
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            return Ok();
        }

        [HttpPost("run-mit-off-session-retry-for-merchant")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<ActionResult> ForceMitOffSessionRetry(Guid? mid)
        {
            var workspan = Workspan.StartEndpoint<AdministratorToolsController>(this, null, _globalData);

            try
            {
                if (mid == null)
                {
                    return BadRequest("Mid is required");
                }

                await _publisher.Publish(new InitiateExecuteOffSessionRetryStrategiesCommand
                {
                    Mid = mid
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            return Ok();
        }

        [HttpPost("force-mit-send-default-consumer-notifications")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<ActionResult> ForceSendMitSendDefaultConsumerNotifications(Guid mid, Guid orderId)
        {
            var workspan = Workspan.StartEndpoint<AdministratorToolsController>(this, null, _globalData);

            try
            {
                await _publisher.Publish(new SendOfferConsumerNotificationsCommand(mid, orderId)
                {
                    ForceSendingDefaultNotifications = true
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            return Ok();
        }

        [HttpPost("stripe-create-orders-for-failing-invoices")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<ActionResult> StripeCreateOrdersForFailingInvoices(Guid mid, string accountId)
        {
            await _publisher.RunIdempotentCommandWithoutResponseAsync(
                new PaymentProviderSynchronizeOrdersCommand(mid, "Stripe", accountId));

            return Ok();
        }


        [HttpPost("download-stripe-reports")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<ActionResult> DownloadStripeReports(Guid mid, string accountId, string? reportIdOrPrefix)
        {
            using var workspan = Workspan.StartEndpoint<AdministratorToolsController>(this, null, _globalData)
                .Baggage("Mid", mid)
                .Baggage("AccountId", accountId)
                .Baggage("ReportIdOrPrefix", reportIdOrPrefix);

            try
            {
                await _stripeReportsService.InitiateReportDownloadingAsync(mid, accountId, reportIdOrPrefix);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                throw;
            }

            return Ok();
        }
    }
}