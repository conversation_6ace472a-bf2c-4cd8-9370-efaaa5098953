using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public partial class EligibilityWorkflowFactory
{
    record ProductionTestCard(
        string TestCardName,
        string HolderName,
        string CardBinNumber = "*",
        string CardLast4Digits = "*")
    {
        public bool IsProductionTestCard(string holderName, string cardBinNumber, string cardLast4Digits)
        {
            return !string.IsNullOrWhiteSpace(holderName) &&
                   HolderName == holderName &&
                   // Starts with is used to support both 6 and 8 digit BINs
                   (CardBinNumber == "*" || cardBinNumber.StartsWith(CardBinNumber)) &&
                   (CardLast4Digits == "*" || CardLast4Digits == cardLast4Digits);
        }
    }


    private IReadOnlyList<ProductionTestCard> _productionTestCards = new List<ProductionTestCard>
    {
        new ProductionTestCard("FlexFactor | Test Card 1", "TREY ZUZEY")
        {
        },
        new ProductionTestCard("Everi | USA DEBIT/TEST CARD 02", "USA DEBIT/TEST CARD 02", "476173", "0135")
    };

    public bool IS_PRODUCTION_TEST_MODE_SEQUENCE(Merchant merchant, Order order)
    {
        using var workspan = Workspan.Start<EligibilityWorkflows.EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        // Guid partnerIdWithProductionTestMode = Guid.Parse("7187eadc-81d1-47c4-b1f0-8cc61b1e793a");

        if ( //merchant.Pid == partnerIdWithProductionTestMode &&
            order.IsCIT == true &&
            (order.Amount is 1_00 or 2_00))
        {
            var er = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            var productionTestCard = _productionTestCards
                .FirstOrDefault(x => x.IsProductionTestCard(er.PaymentMethod?.HolderName,
                    er.PaymentMethod?.CardBinNumber,
                    er.PaymentMethod?.CardLast4Digits));

            if (productionTestCard != null)
            {
                workspan.Log.Information("PRODUCTION TEST CARD DETECTED: {TestCardName}",
                    productionTestCard.TestCardName);
                return true; //!!!
            }
        }

        return false;
    }

    public bool IS_NON_PRODUCTION_TEST_MODE_SEQUENCE(Merchant merchant, Order order)
    {
        using var workspan = Workspan.Start<EligibilityWorkflows.EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        if (!EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
            return false;

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment && // double-check
            (order.IsCIT == true &&
             (order.Amount is 1_00 or 2_00 ||
              order.IsInKioskMode()))) // for now - to always show some UI in kiosk mode
        {
            return true; //!!!
        }

        return false;
    }
}