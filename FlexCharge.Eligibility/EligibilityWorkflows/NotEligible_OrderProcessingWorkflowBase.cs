using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.OrdersRecyclingEngine.Strategies;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public abstract class NotEligible_OrderProcessingWorkflowBase : CodeFirstWorkflowBase<EligibilityCheckContext>
{
    public override string Domain => "Eligibility";

    public override bool IsReadonly => true;
}