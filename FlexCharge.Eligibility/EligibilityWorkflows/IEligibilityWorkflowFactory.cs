using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public interface IEligibilityWorkflowFactory
{
    Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        Create_EvaluateEligibility_WorkflowAsync(
            Entities.Merchant merchant, Order order, EvaluateRequest evaluateRequest, bool offSessionRetry);

    Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        Create_NotEligible_OrderProcessing_WorkflowAsync(Order order, Entities.Merchant merchant);

    Task<(WorkflowBuilder<NotEligiblieEverOrderProcessingContext> Workflow, WorkflowDescription Description)>
        Create_NotEligibleEver_OrderProcessing_WorkflowAsync(Order order, Entities.Merchant merchant);
}