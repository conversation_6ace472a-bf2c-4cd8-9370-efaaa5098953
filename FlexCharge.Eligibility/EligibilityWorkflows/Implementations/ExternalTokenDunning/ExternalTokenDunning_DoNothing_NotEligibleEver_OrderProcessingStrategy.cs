using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ExternalTokenDunning;

public class ExternalTokenDunning_DoNothing_NotEligibleEver_OrderProcessingStrategy :
    NotEligibleEver_OrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => _stableId;

    public override string Name =>
        "External Token Dunning - Do Nothing - Not Eligible Ever Order Processing Strategy";

    public override string Description => "";

    // This is a temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("dae3e65d-d836-4945-b55d-d147cbd092a5");
    public static Guid StableId => _stableId;


    #region Workflow

    public override async Task<WorkflowBuilder<NotEligiblieEverOrderProcessingContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<NotEligiblieEverOrderProcessingContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<NotEligiblieEverOrderProcessingContext> workflow)
    {
        workflow
            .DUMMY();
    }

    #endregion
}