using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ExternalTokenDunning;

public class ExternalTokenDunning_CancelSubscription_NotEligibleEver_OrderProcessingStrategy :
    NotEligibleEver_OrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => _stableId;

    public override string Name =>
        "External Token Dunning - Cancel Subscription - Not Eligible Ever Order Processing Strategy";

    public override string Description => "";

    // This is a temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("ff6bcd9a-5b33-48d1-a533-1819c92d3e8d");
    public static Guid StableId => _stableId;


    #region Workflow

    public override async Task<WorkflowBuilder<NotEligiblieEverOrderProcessingContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<NotEligiblieEverOrderProcessingContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<NotEligiblieEverOrderProcessingContext> workflow)
    {
        workflow.Add(nameof(E0029_CancelExternalSubscription));
    }

    #endregion
}