using System;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ExternalTokenDunning;

public class ExternalTokenDunning_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new("c4d39df4-01a8-406d-9c75-d283ad62a0e8");

    public override string Name => "External Token Dunning - Eligibility Strategy";

    public override string Description => "";


    public ExternalTokenDunning_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) : base(order,
        merchant,
        offSessionRetry)
    {
    }

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        if (!Order.IsMIT())
            throw new FlexChargeException("External token dunning is only supported for MIT orders");

        // if (!OffSessionRetry)
        //     throw new FlexChargeException("External recycling is only supported for off-session retries");

        //!!!!!!DO NOT INSERT CURES ABOVE THIS!!!!!!
        if (Order.IsMIT() && OffSessionRetry)
        {
            //!!! MUST BE THE FIRST CURE, OTHERWISE ANOTHER CURE WILL
            //AUTOMATICALLY STOP THE SEQUENCE BECAUSE ORDER IS NOT ELIGIBLE!!!
            workflow.Add(nameof(E0013_MIT_ResetNotEligibleStatusOnRetry));
        }
        else
        {
            // For MIT orders EVALUATION_STARTED is set above
            workflow.Add(nameof(E0015_SetOfferState_EvaluationStarted));
        }

        // workflow.Add(nameof(E0026_InitializeExternalSubscriptionProcessing));

        workflow.Add(nameof(E3009_TryCharge));

#if DEBUG
        workflow.Add(nameof(E0027_UpdatePaymentInstrumentInformation_ForwardedPaymentInstrument));
        workflow.Add(nameof(E3008_TryFullAuthorization_ForwardedPaymentInstrument));
#endif

        workflow.Add(nameof(E0024_RuleEngineStage3_ExternalTokenDunning));


        workflow.Add(nameof(E5001_ProcessEligibilityResult));
    }

    #endregion
}