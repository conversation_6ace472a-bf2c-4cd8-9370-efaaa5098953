using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.General;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.PRODUCTION_TEST_BLOCKS;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ProductionTest;

public class ProductionTest_Challenge_Approve_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new Guid("482f2f92-fa99-40b8-a41c-694df779ec93");

    public override string Name => "Production Test - Challenge Eligibility Strategy";

    public override string Description => "";


    public ProductionTest_Challenge_Approve_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) :
        base(order, merchant, offSessionRetry)
    {
    }

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        bool orderInTerminalMode = Order.IsInTerminalOrKioskMode();

        workflow.Add(nameof(E9998_PRODUCTION_TEST_ORDER_SetIsTestOrderFlag));

        workflow.Add(nameof(E0018_EnsureMerchantIsActiveAndNonLocked));
        workflow.Add(nameof(E0017_InitialTransactionChecks));

        workflow.Add(nameof(E0021_UpdatePaymentInstrumentInformation));

        workflow.Add(nameof(E9997_PRODUCTION_TEST_ORDER_ExecuteCure_ApproveAfter));

        workflow.Add(nameof(E5001_ProcessEligibilityResult));

        if (orderInTerminalMode)
        {
            workflow.Add(nameof(E5004_SendCheckoutLinkToConsumer));
        }

        //eligibilitySequence.Add(nameof(E5002_UpdateFingerprints));
    }

    #endregion
}