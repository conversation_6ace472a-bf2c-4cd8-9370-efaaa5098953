using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.General;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.VirtualTerminal;

public class VirtualTerminal_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new("1dd959ce-2af3-464c-a265-b936904cf5ab");

    public override string Name => "Virtual Terminal - Eligibility Strategy";

    public override string Description => "";


    public VirtualTerminal_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) : base(order,
        merchant,
        offSessionRetry)
    {
    }

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        throw new Exception("Not used for now");

        bool useSalePayment = Order.GetDesiredPaymentTransactionType() == DesiredPaymentTransactionType.Sale;
        bool useWidget = false; //order.GetEvaluationRequestType() == EvaluateRequestType.EVALUATE;

        workflow.Add(nameof(E0018_EnsureMerchantIsActiveAndNonLocked));
        workflow.Add(nameof(E0017_InitialTransactionChecks));

        workflow.Add(nameof(E0021_UpdatePaymentInstrumentInformation));
        workflow.Add(nameof(E1002_LuhnCheck));

        if (useWidget)
        {
            workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));
        }

        workflow.Add(nameof(E1001_BinCheck));
        workflow.Add(nameof(E2007_BlockByCardCountry));
        //workflow.Add(nameof(E2005_BlockByBinNumber));
        workflow.Add(nameof(E2006_SkipPaymentProvidersByBinResults));

        if (useSalePayment)
        {
            workflow.Add(nameof(E3006_TrySale));
        }
        else
        {
            workflow.Add(nameof(E3002_TryFullAuthorization));
        }

        if (useWidget)
        {
            workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));
        }

        workflow.Add(nameof(E5003_ProcessVirtualTerminalOrderResult));
    }

    #endregion
}