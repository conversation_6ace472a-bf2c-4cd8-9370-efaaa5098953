using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.EveriTerminal;

public class EveriTerminal_NotEligible_OrderProcessingStrategy : NotEligible_OrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new("18f1e1c3-c543-4164-86c0-e7d18d6e1a4c");

    public override string Name => "Everi - Not Eligible - Order Processing Strategy";

    public override string Description => "";


    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        workflow.Add(nameof(E0010_MIT_AskForDifferentCardForNotEligibleOffers));
        workflow.Add(nameof(E0008_ProcessNotEligibleOffer));

        workflow.Add(nameof(E3007_AccountUpdater));
    }

    #endregion
}