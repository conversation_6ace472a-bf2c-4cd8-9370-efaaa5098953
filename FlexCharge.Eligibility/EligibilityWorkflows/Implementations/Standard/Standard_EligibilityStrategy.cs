//#define ENABLE_EMPTY_BILLING_INFORMATION_TESTING
//#define FORCE_CREDIT_BUREAU_CHECK_IN_GHOST_MODE

// //#define ENABLE_SUBSCRIPTIONS_TESTING
// //#define ENABLE_RESPONSE_CODE_TESTING

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.General;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RequestModification;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.Standard;

public class Standard_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 14;

    public override Guid WorkflowId => _stableId;

    public override string Name => "Standard - Eligibility Strategy";

    public override string Description => "";

    // This is a temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("fd1cf96d-6bb9-4af0-a37f-224a4b7f6d9a");
    public static Guid StableId => _stableId;


    public Standard_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) : base(order,
        merchant,
        offSessionRetry)
    {
    }

    static IssuersToRemoveDeviceList _issuersToRemoveDevice = new();

    #region Strategy-Specific Parameters

    private IntegerValue _PspLoadBalancing_RoundRobinPercentage;
    private IntegerValue _PspLoadBalancing_WeightedPercentage;

    #endregion


    #region Workflow

    private void CreateWorkflowExternalControls(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        _PspLoadBalancing_RoundRobinPercentage = workflow.AddExternalControl(
            new IntegerValue(new Guid("bb91df3f-6b58-4479-9e4d-ec2c684f6198"),
                "Round Robin Strategy %",
                "Sets percentage of orders that will be processed using Round Robin strategy. The rest will be routing to other strategies.",
                ExternalControls.CascadeStrategyGroup.Name,
                ExternalControls.CascadeStrategyGroup.PaymentProcessorLoadBalancing.Name,
                ExternalControlOwnerType.Workflow,
                nameof(Standard_EligibilityStrategy),
                defaultValue: 100,
                minValue: 0, maxValue: 100));

        _PspLoadBalancing_WeightedPercentage = workflow.AddExternalControl(
            new IntegerValue(new Guid("dc0a5a2e-ae72-4aa7-b119-127cfd65be7e"),
                "Weighted Load Balancing Strategy %",
                "Sets percentage of orders that will be processed using Weighted Load Balancing strategy. The rest will be routed to random load balancer",
                ExternalControls.CascadeStrategyGroup.Name,
                ExternalControls.CascadeStrategyGroup.PaymentProcessorLoadBalancing.Name,
                ExternalControlOwnerType.Workflow,
                nameof(Standard_EligibilityStrategy),
                defaultValue: 0,
                minValue: 0, maxValue: 100));
    }


    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        CreateWorkflowExternalControls(workflow);

        await _issuersToRemoveDevice.LoadAsync(skipLoading: true);


        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        bool useSalePayment = Order.GetDesiredPaymentTransactionType() == DesiredPaymentTransactionType.Sale;

        //!!!!!!DO NOT INSERT CURES ABOVE THIS!!!!!!
        if (Order.IsMIT() && OffSessionRetry)
        {
            //!!! MUST BE THE FIRST CURE, OTHERWISE ANOTHER CURE WILL
            //AUTOMATICALLY STOP THE SEQUENCE BECAUSE ORDER IS NOT ELIGIBLE!!!
            workflow.Add(nameof(E0013_MIT_ResetNotEligibleStatusOnRetry));
        }
        else
        {
            // For MIT orders EVALUATION_STARTED is set above
            workflow.Add(nameof(E0015_SetOfferState_EvaluationStarted));
        }

        workflow.Add(nameof(E0018_EnsureMerchantIsActiveAndNonLocked));

        workflow.Add(nameof(E2012_EvaluateRequestRewriter));

        workflow.Add(nameof(E0017_InitialTransactionChecks));

        workflow.Add(nameof(E0021_UpdatePaymentInstrumentInformation));
        workflow.Add(nameof(E1002_LuhnCheck));


        workflow.Add(nameof(E0019_OrderAmountCheck));

        // if (!EnvironmentHelper.IsInProduction)
        // {
        //     workflow.Add(nameof(E9006_TestBlockEditor));
        //     workflow.Add(nameof(E9007_TestIfBlockEditor));
        // }

        //sequence.Add(nameof(E2003_RetainPaymentInstrument));
        workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));

        workflow.Add(nameof(E0022_RequestNetworkTokenOnCardChange));

        workflow.Add(nameof(E2010_LoadFingerprints));
        workflow.Add(nameof(E2002_LoadFingerprintStatistics));
        workflow.Add(nameof(E2008_FingerprintsCheck));
        workflow.Add(nameof(EligibilityChecks.Implementations.TEMPORARY
            .E9902_TemporarilyDisableNonVisaOrMastercardCards));
        workflow.Add(nameof(E2004_BlockByFingerprintLists));

        // if (order.IsMIT())
        // {
        //     workflow.Add(nameof(E0014_MIT_StopDuplicateSubscriptionTransaction));
        // }

        #region Testing Support

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
#if ENABLE_RESPONSE_CODE_TESTING
            workflow.Add(nameof(EligibilityChecks.Implementations.Testing
                .E9002_CheckIfInResponseCodeTesting));
#endif

#if ENABLE_SUBSCRIPTIONS_TESTING
            workflow.Add(nameof(EligibilityChecks.Implementations.Testing.E9003_CheckIfInSubscriptionTesting));
#endif

#if ENABLE_EMPTY_BILLING_INFORMATION_TESTING
            workflow.Add(nameof(EligibilityChecks.Implementations.Testing.E9008_TestEmptyBillingInformation));
#endif
        }

        #endregion

        if (Order.IsMIT() && OffSessionRetry)
        {
            workflow.Add(nameof(E0011_MIT_ResetAlreadyExecutedCures));
        }

        //workflow.Add(nameof(E2009_BlockByBinGroup));

        workflow.Add(nameof(E1001_BinCheck));

        workflow.Add(nameof(E2013_BlockByDisputeOptimization));


        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            workflow.Add(nameof(E9005_ReplacePaymentInstrumentCountryWithUS));
        }

        workflow.Add(nameof(E2007_BlockByCardCountry));
        //workflow.Add(nameof(E2005_BlockByBinNumber));

        workflow.Add(nameof(E2006_SkipPaymentProvidersByBinResults));
        //sequence.Add(nameof(EligibilityChecks.Implementations.TEMPORARY.E9901_TemporarilyDisableNonVisaCards));

        workflow.Add(nameof(E2011_FingerprintsOpenOffersCheck));
        workflow.Add(nameof(E2014_FingerprintsWrittenOffOrdersCheck));

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            workflow.Add(nameof(E9004_ReplacePaymentInstrumentTypeWithCREDIT));
        }

        // using (workflow.StartParallelBlock())
        // {
        //     //sequence.Add(nameof(E3004_TryVerifyAmount));
        //     workflow.Add(nameof(E3003_Try3DSFrictionlessAuthentication));
        // }

        var parallelBlockBuilder1 = workflow.AddParallelBlock();

        //parallelBlockBuilder1.AddChild(nameof(E3004_TryVerifyAmount));
        parallelBlockBuilder1.Add(nameof(E3003_Try3DSFrictionlessAuthentication));

        var modifyAuthorizationSwitchBlockBuilder = CreateModifyAuthorizationSubFlow(workflow);

        // CREATE DYNAMIC AMOUNT AUTHORIZATION SUB FLOW
        var dynamicAmountAuthorizationABTestBlockBuilder =
            CreateDynamicAmountAuthorizationSubFlow(workflow, Merchant);
        // Connect multiple outs block's terminal blocks to the next block in the sequence as we now we have next block as current
        modifyAuthorizationSwitchBlockBuilder.ContinueWith(dynamicAmountAuthorizationABTestBlockBuilder.Block);

        // REACT AVS ON INCOMPLETE BILLING INFORMATION SUB FLOW
        var redactAvsOnIncompleteBillingInformationSubFlow =
            CreateRedactAvs_OnIncompleteBillingInformation_SubFlow(workflow);
        // Connect multiple outs block's terminal blocks to the next block in the sequence
        dynamicAmountAuthorizationABTestBlockBuilder.ContinueWith(redactAvsOnIncompleteBillingInformationSubFlow.Block);

        // REACT AVS AFTER ANY INVALID AVS RESULT SUB FLOW
        var redactAvsAfterAnyFailedAvsCheckSubFlow = CreateRedactAvs_AfterAnyFailedAvsCheck_SubFlow(workflow);
        // Connect multiple outs block's terminal blocks to the next block in the sequence
        redactAvsOnIncompleteBillingInformationSubFlow.ContinueWith(redactAvsAfterAnyFailedAvsCheckSubFlow.Block);

        // REDACT SCHEME TRANSACTION ID SUB FLOW 
        var redactSchemeTransactionIdConditionalBlockBuilder = CreateRedactSchemeTransactionIdSubFlow(workflow);
        // Connect multiple outs block's terminal blocks to the next block in the sequence
        redactAvsAfterAnyFailedAvsCheckSubFlow.ContinueWith(redactSchemeTransactionIdConditionalBlockBuilder.Block);

        // REDACT IP IF DISABLED ON MERCHANT SUB FLOW
        var redactIpIfDisabledOnMerchantSubFlow = CreateRedactIp_IfDisabledOnMerchant_SubFlow(workflow);
        // Connect multiple outs block's terminal blocks to the next block in the sequence
        redactSchemeTransactionIdConditionalBlockBuilder.ContinueWith(redactIpIfDisabledOnMerchantSubFlow.Block);


        // CREATE DETERMINE PAYMENT ROUTING STRATEGY SUB FLOW
        var determinePaymentRoutingStrategySubFlow =
            CreateDeterminePaymentRoutingStrategySubFlow(workflow, Merchant);
        // Connect multiple outs block's terminal blocks to the next block in the sequence as we now we have next block as current
        redactIpIfDisabledOnMerchantSubFlow.ContinueWith(determinePaymentRoutingStrategySubFlow.Block);


        if (useSalePayment)
        {
            workflow.Add(nameof(E3006_TrySale));
        }
        else
        {
            workflow.Add(nameof(E3002_TryFullAuthorization));
        }

        // Connect multiple outs block's terminal blocks to the next block in the sequence as we now we have next block as current
        determinePaymentRoutingStrategySubFlow.ContinueWith(workflow.CurrentBlock);


        workflow.Add(nameof(E3005_TryACHDebitPayment));

        workflow.Add(nameof(E2016_BlockStolenCards));
        //workflow.Add(nameof(E2015_BlockNSFCodeForPrepaidCards));

        workflow.Add(nameof(E0003_SetOfferState_Stage1Passed));
        workflow.Add(nameof(E0002_RuleEngineStage2));
        workflow.Add(nameof(E0007_OfferNSFRequestsThrottlingLimiting));


#if FORCE_CREDIT_BUREAU_CHECK_IN_GHOST_MODE
        sequence.Add(nameof(EligibilityChecks.Implementations.GhostMode
            .E9905_EnsureExperianCreditBuroCalledInGhostMode));
#endif

//         using (workflow.StartParallelBlock())
//         {
//             workflow.Add(nameof(E2001_FraudCheck));
// #if DEBUG
//             if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
//             {
//                 // eligibilitySequence.AddCheck(nameof(EligibilityChecks.Implementations.Testing.E9001_EnsureExperianCreditBuroCalled));\
//             }
// #endif
//
//             workflow.Add(nameof(E4001_CreditBureauCheck));
//         }

        var parallelBlockBuilder2 = workflow.AddParallelBlock();
        parallelBlockBuilder2.Add(nameof(E2001_FraudCheck));
        parallelBlockBuilder2.Add(nameof(E4001_CreditBureauCheck));
// #if DEBUG
//         if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
//         {
//            // parallelBlockBuilder2.Add(nameof(EligibilityChecks.Implementations.Testing.E9001_EnsureExperianCreditBuroCalled));\
//        }
//#endif


        workflow.Add(nameof(E0004_SetOfferState_Stage2Passed));
        workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));
        workflow.Add(nameof(E0025_RuleEngineStage3));
        workflow.Add(nameof(E0023_DetermineRecycleStrategy));

        bool orderInTerminalMode = Order.IsInTerminalOrKioskMode();
        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            if (orderInTerminalMode)
            {
                workflow.Add(nameof(E9006_TestTerminal_CITCuresOnCheckoutPage));
            }
        }

        workflow.Add(nameof(E5001_ProcessEligibilityResult));

        if (orderInTerminalMode)
        {
            workflow.Add(nameof(E5004_SendCheckoutLinkToConsumer));
        }

        //eligibilitySequence.Add(nameof(E5002_UpdateFingerprints));
    }

    private static AbTestBlockMultipleExitsBuilder<EligibilityCheckContext> CreateDynamicAmountAuthorizationSubFlow(
        WorkflowBuilder<EligibilityCheckContext> workflow, Entities.Merchant merchant)
    {
        double testExecutionPercentage = 0;
        if (merchant.DynamicAuthorizationDiscountThrottlePercentage.HasValue)
        {
            var discountThrottlePercentage = merchant.DynamicAuthorizationDiscountThrottlePercentage.Value;

            if (discountThrottlePercentage < 0 || discountThrottlePercentage > 1)
                throw new FlexChargeException(
                    $"{nameof(merchant.DynamicAuthorizationDiscountThrottlePercentage)} must be between 0 and 1");

            testExecutionPercentage = ((double) discountThrottlePercentage) * 100.0;
        }


        var dynamicAmountAuthorizationABTestBlockBuilder =
            workflow.AddABTestBlock("DynamicAuthorizationAmountDiscount", context => testExecutionPercentage);

        var transformBlock = new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        #region Modifiying Authorization by Adding a Random Discount in range of 1-10 cents

        int discountAmount = new Random().Next(1, 11);

        transformBlock.SetTransformation("ModifyNextAuthorization_DynamicDiscountAmount",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization =>
                {
                    authorization.DynamicAmountDiscount = discountAmount;
                });
            });

        #endregion

        dynamicAmountAuthorizationABTestBlockBuilder.AddCaseA(transformBlock);

        return dynamicAmountAuthorizationABTestBlockBuilder;
    }

    private IfBlockMultipleExitsBuilder<EligibilityCheckContext>
        CreateDeterminePaymentRoutingStrategySubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow, Entities.Merchant merchant)
    {
        var selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder =
            workflow.AddABTestBlock("RoundRobinLoadBalancingOrOther",
                context => context.GetExternalParameterValue(_PspLoadBalancing_RoundRobinPercentage));

        #region Blocks to select strategies

        var useRoundRobinStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useRoundRobinStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.RoundRobinLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.RoundRobinLoadBalancing);
            });

        var useWeightedLoadBalancingStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useWeightedLoadBalancingStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.WeightedLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.WeightedLoadBalancing);
            });

        var useRandomLoadBalancingStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useRandomLoadBalancingStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.RandomLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.RandomLoadBalancing);
            });

        #endregion

        selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.AddCaseA(useRoundRobinStrategyTransformBlock);

        var selectWeightedOrRandomStrategyABTestBlockBuilder =
            workflow.AddABTestBlock("WeightedOrRandomLoadBalancing",
                context => context.GetExternalParameterValue(_PspLoadBalancing_WeightedPercentage));

        selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.AddCaseB(selectWeightedOrRandomStrategyABTestBlockBuilder
            .Block);

        selectWeightedOrRandomStrategyABTestBlockBuilder.AddCaseA(useWeightedLoadBalancingStrategyTransformBlock);
        selectWeightedOrRandomStrategyABTestBlockBuilder.AddCaseB(useRandomLoadBalancingStrategyTransformBlock);


        var ifPaymentRoutingStrategyNotDetermined =
            workflow
                .IF("PaymentRoutingStrategy not determined?",
                    context => string.IsNullOrWhiteSpace(context.Order.PaymentRoutingStrategy))
                .TRUE(t => t
                    .BLOCK(selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.Block));

        return ifPaymentRoutingStrategyNotDetermined;
    }

    private static IfBlockMultipleExitsBuilder<EligibilityCheckContext>
        CreateRedactAvs_OnIncompleteBillingInformation_SubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        var conditionalBlock =
            workflow.AddIfBlock("Redact AVS if no AddressLine1 and no Zip in Billing Information", (context) =>
            {
                bool redactAvs = string.IsNullOrWhiteSpace(context.OrderPayload.BillingInformation?.AddressLine1) &&
                                 string.IsNullOrWhiteSpace(context.OrderPayload.BillingInformation?.Zipcode);

                return redactAvs;
            });

        #region RedactAvsTransformBlock

        var redactAvsTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactAvsTransformBlock.SetTransformation("ModifyNextAuthorization_RedactAvs_OnIncompleteBillingInformation",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization => { authorization.RedactAvs = true; });
            });

        #endregion


        conditionalBlock.AddTrueCase(redactAvsTransformBlock);

        return conditionalBlock;
    }

    private static IfBlockMultipleExitsBuilder<EligibilityCheckContext>
        CreateRedactAvs_AfterAnyFailedAvsCheck_SubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        var conditionalBlock =
            workflow.AddIfBlock("Redact AVS if any previous AVS check failed", (context) =>
            {
                bool anyAvsCheckFailed = context.Order.GetPaymentTransactionsResponses()
                    .Any(response =>
                        response.TransactionType != nameof(TransactionType.InitialDecline) &&
                        response.GetAvsResponseType() == AvsResponseType.NoMatch);


                bool redactAvs = anyAvsCheckFailed;

                return redactAvs;
            });

        #region RedactAvsTransformBlock

        var redactAvsTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactAvsTransformBlock.SetTransformation("ModifyNextAuthorization_RedactAvs_AfterAnyFailedAvsCheck",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization => { authorization.RedactAvs = true; });
            });

        #endregion


        conditionalBlock.AddTrueCase(redactAvsTransformBlock);

        return conditionalBlock;
    }

    private static IfBlockMultipleExitsBuilder<EligibilityCheckContext> CreateRedactSchemeTransactionIdSubFlow(
        WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        var conditionalBlock =
            workflow.AddIfBlock("Redact SchemeTransactionId if feature is disabled for merchant", (context) =>
            {
                bool redactSchemeTransactionId = !context.Merchant.SchemeTransactionIdEnabled;

                return redactSchemeTransactionId;
            });

        #region RedactSchemeTransactionIdTransformBlock

        var redactSchemeTransactionIdTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactSchemeTransactionIdTransformBlock.SetTransformation("ModifyNextAuthorization_SchemeTransactionId",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization =>
                {
                    authorization.RedactSchemeTransactionId = true;
                });
            });

        #endregion


        conditionalBlock.AddTrueCase(redactSchemeTransactionIdTransformBlock);

        return conditionalBlock;
    }

    private static IfBlockMultipleExitsBuilder<EligibilityCheckContext>
        CreateRedactIp_IfDisabledOnMerchant_SubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        var conditionalBlock =
            workflow.AddIfBlock("Redact IP if IP disabled on merchant",
                (context) => { return context.Merchant.RedactIpEnabled; });

        #region RedactIpTransformBlock

        var redactIpTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactIpTransformBlock.SetTransformation("ModifyNextAuthorization_RedactIp",
            context => { context.Order.ModifyNextAuthorization(authorization => { authorization.RedactIp = true; }); });

        #endregion


        conditionalBlock.AddTrueCase(redactIpTransformBlock);

        return conditionalBlock;
    }


    private static SwitchBlockBuilder<EligibilityCheckContext>
        CreateModifyAuthorizationSubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        HashSet<string> issuersToRedactCVV = new HashSet<string>
        {
            // "EVOLVE BANK & TRUST",
            // "BANK OF AMERICA NATIONAL ASSOCIATION",
            // "BANK OF AMERICA - CONSUMER CREDIT",
            // "GOLDMAN SACHS BANK USA",
        };

        #region Modifying Authorization Payload by Card BIN

        var modifyAuthorizationSwitchBlockBuilder =
            workflow.AddSwitchBlock("Modifying next Authorization by card BIN");

        #region RedactCvvTransformBlock

        var redactCvvTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactCvvTransformBlock.SetTransformation("ModifyNextAuthorization_RedactCvv",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization => { authorization.RedactCvv = true; });
            });

        #endregion

        #region RedactCvvAndDeviceTransformBlock

        var redactCvvAndDeviceTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactCvvAndDeviceTransformBlock.SetTransformation("ModifyNextAuthorization_RedactCvv_RedactDevice",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization =>
                {
                    authorization.RedactCvv = true;
                    authorization.RedactDevice = true;
                });
            });

        #endregion

        #region RedactDeviceTransformBlock

        var redactDeviceTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();
        redactDeviceTransformBlock.SetTransformation("ModifyNextAuthorization_RedactDevice",
            context =>
            {
                context.Order.ModifyNextAuthorization(authorization => { authorization.RedactDevice = true; });
            });

        #endregion

        modifyAuthorizationSwitchBlockBuilder
            //
            // Bin is in _issuersToRemoveDeviceList and issuersToRedactCVV list? -> Redact CVV and Device Bins?
            //
            .AddConditionCase("Should redact CVV and Device?", (context) =>
                    !string.IsNullOrWhiteSpace(context.OrderPayload.PaymentMethod?.CardBinNumber) &&
                    int.TryParse(context.OrderPayload.PaymentMethod?.CardBinNumber, out var bin) &&
                    _issuersToRemoveDevice.Map.TryGetValue(bin, out var issuer) &&
                    issuersToRedactCVV.Contains(issuer.ToUpper()),
                redactCvvAndDeviceTransformBlock)
            //
            // Bin is only in _issuersToRemoveDeviceList list? -> Redact Device
            //
            .AddConditionCase("Should redact Device?", (context) =>
                    !string.IsNullOrWhiteSpace(context.OrderPayload.PaymentMethod?.CardBinNumber) &&
                    int.TryParse(context.OrderPayload.PaymentMethod?.CardBinNumber, out var bin) &&
                    _issuersToRemoveDevice.Map.TryGetValue(bin, out var issuer),
                redactDeviceTransformBlock)
            //
            // Any failed authorization/sale with no CVV match -> Redact CVV
            //
            .AddConditionCase("Should redact CVV?", (context) =>
                {
                    if (context.PaymentInstrumentInformation.CvvInputValidationResult ==
                        CvvInputValidationResult.Empty ||
                        context.PaymentInstrumentInformation.CvvInputValidationResult ==
                        CvvInputValidationResult.NotPassed)
                    {
                        if (!MerchantSpecificOverrides.DisableRedactCvvOnCvvInputValidationError(context.Merchant))
                            return true;
                    }

                    // We rely on the first authorization response to determine if CVV should be redacted
                    // because if it's already redacted, there is no indication of CVV match on subsequent responses
                    var firstAuthorizationResponse = context.Order.GetFirstPaymentTransactionResponse();
                    var cvvResponseType = firstAuthorizationResponse?.GetCvvResponseType();

                    switch (cvvResponseType)
                    {
                        case null:
                        case CvvResponseType.Match:
                        case CvvResponseType.NotProcessed:
                            return false;
                        case CvvResponseType.NoMatch:
                        case CvvResponseType.NotProvided:
                        default:
                            return true;
                    }
                },
                redactCvvTransformBlock);

        #endregion

        return modifyAuthorizationSwitchBlockBuilder;
    }

    #endregion

    public T GetExternalParameterValue<T>(ExternalControlBase<T> externalControl)
        where T : struct
    {
        return externalControl.Value ?? externalControl.DefaultValue;
    }
}