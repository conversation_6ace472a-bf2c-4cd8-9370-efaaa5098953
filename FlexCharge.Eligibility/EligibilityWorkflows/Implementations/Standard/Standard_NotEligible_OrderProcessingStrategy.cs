using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.Standard;

public class Standard_NotEligible_OrderProcessingStrategy : NotEligible_OrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => _stableId;

    public override string Name => "Standard - Not Eligible - Order Processing Strategy";

    public override string Description => "";

    // This is a temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("ed70abe8-c671-4715-8240-31f3bd7d002d");
    public static Guid StableId => _stableId;

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        workflow.Add(nameof(E0010_MIT_AskForDifferentCardForNotEligibleOffers));
        workflow.Add(nameof(E0008_ProcessNotEligibleOffer));

        workflow.Add(nameof(E3007_AccountUpdater));
    }

    #endregion
}