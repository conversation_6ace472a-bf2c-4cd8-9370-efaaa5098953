using System;
using System.Collections.Generic;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.Eligibility.Services.BinNumberValidationServices;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.Eligibility.Services.RiskManagement;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityChecks;

public record NotEligiblieEverOrderProcessingInputData : IWorkflowInputData
{
}

public class
    NotEligiblieEverOrderProcessingContext : ExecutionContextBase<NotEligiblieEverOrderProcessingInputData,
    NoExecutionResult>
{
    public override Guid TenantId => Merchant.Mid;
    public override Guid CorrelationId => Order.Id;

    public Merchant Merchant { get; }
    public Order Order { get; }

    protected override IExecutionTracer Tracer { get; } = new DummyExecutionTracer();

    public NotEligiblieEverOrderProcessingContext(Merchant merchant, Order order)
    {
        Merchant = merchant;
        Order = order;
    }
}