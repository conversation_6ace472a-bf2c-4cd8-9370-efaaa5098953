using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.OrdersRecyclingEngine.Strategies;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public abstract class
    NotEligibleEver_OrderProcessingWorkflowBase : CodeFirstWorkflowBase<NotEligiblieEverOrderProcessingContext>
{
    public override string Domain => "Eligibility";

    public override bool IsReadonly => true;
}