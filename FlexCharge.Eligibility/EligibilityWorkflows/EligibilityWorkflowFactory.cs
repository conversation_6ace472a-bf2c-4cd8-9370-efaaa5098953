using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityWorkflows.Implementations.EveriTerminal;
using FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ExternalTokenDunning;
using FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ProductionTest;
using FlexCharge.Eligibility.EligibilityWorkflows.Implementations.Standard;
using FlexCharge.Eligibility.EligibilityWorkflows.Implementations.VirtualTerminal;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.ActivityService;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public partial class EligibilityWorkflowFactory : IEligibilityWorkflowFactory
{
    private readonly IActivityService _activityService;

    public EligibilityWorkflowFactory(IActivityService activityService)
    {
        _activityService = activityService;
    }

    #region Observability

    async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order order,
        string eventName = null,
        //object meta = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await _activityService.AddActivityAsync(activityNameEnum, order, eventName, data, subcategory, meta);
    }

    private async Task AddWorkflowCreatedActivityAsync(Order order, WorkflowDescription workflowDescription)

    {
        await AddActivityAsync(WorkflowActivities.Workflow_Created,
            order,
            meta: meta => meta
                .SetValue("WorkflowId", workflowDescription.Id)
                .SetValue("WorkflowVersion", workflowDescription.Version)
                .SetValue("WorkflowDomain", workflowDescription.Domain)
                .SetValue("WorkflowName", workflowDescription.Name)
                .SetValue("WorkflowDescription", workflowDescription.Description)
        );
    }

    #endregion

    #region Evaluate Eligibility Sequences

    public async Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        Create_EvaluateEligibility_WorkflowAsync(
            Entities.Merchant merchant,
            Order order, EvaluateRequest evaluateRequest, bool offSessionRetry)
    {
        using var workspan = Workspan.Start<EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        EligibilityStrategyWorkflowBase strategyWorkflow = null;

        if (evaluateRequest.OrderSource?.ToLower() == nameof(OrderSource.vterminal))
        {
            #region Virtual Terminal Sequence

            if (!merchant.VirtualTerminalEnabled)
            {
                #region Observability

                await AddActivityAsync(EligibilityErrorActivities.VirtualTerminalOrder_NotEnabledForMerchant, order);

                #endregion

                throw new NotEligibleException();
            }

            if (merchant.CITEvaluateAsync)
            {
                #region Observability

                await AddActivityAsync(
                    EligibilityErrorActivities.VirtualTerminalOrder_NotCompatibleWithMerchantAsyncMode, order);

                #endregion

                throw new NotEligibleException();
            }

            #region Observability

            await AddActivityAsync(EligibilityActivities.VirtualTerminalOrder_EnteredVirtualTerminalMode, order);

            #endregion

            strategyWorkflow = new VirtualTerminal_EligibilityStrategy(order, merchant, offSessionRetry);

            #endregion
        }
        else
        {
            if (IS_PRODUCTION_TEST_MODE_SEQUENCE(merchant, order))
            {
                // Always show challenge for test orders with amount of 2.00$ 
                if (order.Amount == 2_00)
                {
                    strategyWorkflow =
                        new ProductionTest_Challenge_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }
                else
                {
                    strategyWorkflow =
                        new ProductionTest_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }

                workspan.Log.Warning("Creating production test eligibility strategy workflow: {Name}",
                    strategyWorkflow.GetType().Name);
            }
            else if (!EnvironmentHelper.IsInProduction && IS_NON_PRODUCTION_TEST_MODE_SEQUENCE(merchant, order))
            {
                if (EnvironmentHelper.IsInProduction)
                {
                    workspan.Log.Fatal("Cannot create non-production test workflow in production environment");

                    throw new FlexChargeException(
                        "Cannot create non-production test workflow in production environment");
                }

                // Always show challenge for test orders with amount of 2.00$ 
                if (order.Amount == 2_00 ||
                    order.IsInKioskMode()) // for now - to always show some UI in kiosk mode
                {
                    strategyWorkflow =
                        new ProductionTest_Challenge_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }
                else
                {
                    strategyWorkflow =
                        new ProductionTest_Approve_EligibilityStrategy(order, merchant, offSessionRetry);
                }

                workspan.Log.Warning("Creating non-production test eligibility strategy workflow: {Name}",
                    strategyWorkflow.GetType().Name);
            }
            else if (merchant.EligibilityStrategyWorkflowId != null)
            {
                workspan.Log.Information("Creating workflow: {WorkflowId}",
                    merchant.EligibilityStrategyWorkflowId);

                switch (merchant.EligibilityStrategyWorkflowId.ToString())
                {
                    case "fd1cf96d-6bb9-4af0-a37f-224a4b7f6d9a":
                        strategyWorkflow = new Standard_EligibilityStrategy(order, merchant, offSessionRetry);
                        break;
                    case "c4d39df4-01a8-406d-9c75-d283ad62a0e8":
                        strategyWorkflow =
                            new ExternalTokenDunning_EligibilityStrategy(order, merchant, offSessionRetry);
                        break;
                    case "b5b932e1-a941-4d19-b621-be3a252090b8":
                        strategyWorkflow =
                            new EveriTerminal_EligibilityStrategy(order, merchant, offSessionRetry);
                        break;
                    default:
                        workspan.Log.Fatal("Unknown eligibility strategy workflow: {WorkflowId}",
                            merchant.EligibilityStrategyWorkflowId);
                        throw new NotImplementedException(
                            $"Unknown eligibility strategy workflow: {merchant.EligibilityStrategyWorkflowId}");
                }
            }
            else
            {
                workspan.Log.Information("Creating default eligibility strategy workflow");
                strategyWorkflow = new Standard_EligibilityStrategy(order, merchant, offSessionRetry);
            }
        }


        var workflow = await strategyWorkflow.CreateWorkflowAsync();
        var workflowDescription = strategyWorkflow.ToWorkflowDescription();

        await AddWorkflowCreatedActivityAsync(order, workflowDescription);

        return (workflow, workflowDescription);
    }


    // private async Task PopulateEligibilityStrategyWorkflowsAsync()
    // {
    //     if (_availableEligibilityStrategyWorkflows != null)
    //         return; //!!!
    //
    //     lock (_availableEligibilityStrategyWorkflowsLock)
    //     {
    //         if (_availableEligibilityStrategyWorkflows != null)
    //             return; //!!!
    //
    //
    //         _availableEligibilityStrategyWorkflows = new HashSet<Guid>();
    //     }
    // }

    #endregion

    #region Not Eligible Order Processing Workflow

    public async Task<(WorkflowBuilder<EligibilityCheckContext> Workflow, WorkflowDescription Description)>
        Create_NotEligible_OrderProcessing_WorkflowAsync(Order order, Entities.Merchant merchant)
    {
        using var workspan = Workspan.Start<EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        NotEligible_OrderProcessingWorkflowBase notEligibleOrderProcessingWorkflow = null;

        if (merchant.NotEligibleOrderProcessingWorkflowId != null)
        {
            workspan.Log.Information("Creating workflow: {WorkflowId}",
                merchant.NotEligibleOrderProcessingWorkflowId);

            switch (merchant.NotEligibleOrderProcessingWorkflowId.ToString())
            {
                case "ed70abe8-c671-4715-8240-31f3bd7d002d":
                    notEligibleOrderProcessingWorkflow =
                        new Standard_NotEligible_OrderProcessingStrategy();
                    break;
                case "17e9354b-f722-49de-ad42-14ee181a8ef2":
                    notEligibleOrderProcessingWorkflow =
                        new ExternalTokenDunning_NotEligible_OrderProcessingStrategy();
                    break;
                case "18f1e1c3-c543-4164-86c0-e7d18d6e1a4c":
                    notEligibleOrderProcessingWorkflow =
                        new EveriTerminal_NotEligible_OrderProcessingStrategy();
                    break;
                default:
                    workspan.Log.Fatal("Unknown process not eligible order workflow: {WorkflowId}",
                        merchant.NotEligibleOrderProcessingWorkflowId);
                    throw new NotImplementedException(
                        $"Unknown process not eligible order strategy workflow: {merchant.NotEligibleOrderProcessingWorkflowId}");
            }
        }
        else
        {
            workspan.Log.Information("Creating standard process not eligible order strategy workflow");
            notEligibleOrderProcessingWorkflow =
                new Standard_NotEligible_OrderProcessingStrategy();
        }

        var workflow = await notEligibleOrderProcessingWorkflow.CreateWorkflowAsync();
        var workflowDescription = notEligibleOrderProcessingWorkflow.ToWorkflowDescription();

        await AddWorkflowCreatedActivityAsync(order, workflowDescription);

        return (workflow, workflowDescription);
    }

    #endregion

    #region Not Eligible Ever Order Processing Workflow

    public async
        Task<(WorkflowBuilder<NotEligiblieEverOrderProcessingContext> Workflow, WorkflowDescription Description)>
        Create_NotEligibleEver_OrderProcessing_WorkflowAsync(Order order, Entities.Merchant merchant)
    {
        using var workspan = Workspan.Start<EligibilityWorkflowFactory>()
            .Baggage("MerchantId", merchant.Mid)
            .Baggage("OrderId", order.Id);

        NotEligibleEver_OrderProcessingWorkflowBase notEligibleOrderProcessingWorkflow = null;

        if (merchant.NotEligibleEverOrderProcessingWorkflowId != null)
        {
            workspan.Log.Information("Creating workflow: {WorkflowId}",
                merchant.NotEligibleEverOrderProcessingWorkflowId);

            switch (merchant.NotEligibleEverOrderProcessingWorkflowId.ToString())
            {
                case "dae3e65d-d836-4945-b55d-d147cbd092a5":
                    notEligibleOrderProcessingWorkflow =
                        new ExternalTokenDunning_DoNothing_NotEligibleEver_OrderProcessingStrategy();
                    break;
                case "ff6bcd9a-5b33-48d1-a533-1819c92d3e8d":
                    notEligibleOrderProcessingWorkflow =
                        new ExternalTokenDunning_CancelSubscription_NotEligibleEver_OrderProcessingStrategy();
                    break;
                default:
                    workspan.Log.Fatal("Unknown process not eligible ever order workflow: {WorkflowId}",
                        merchant.NotEligibleOrderProcessingWorkflowId);
                    throw new NotImplementedException(
                        $"Unknown process not eligible ever order strategy workflow: {merchant.NotEligibleOrderProcessingWorkflowId}");
            }

            var workflow = await notEligibleOrderProcessingWorkflow.CreateWorkflowAsync();
            var workflowDescription = notEligibleOrderProcessingWorkflow.ToWorkflowDescription();

            await AddWorkflowCreatedActivityAsync(order, workflowDescription);

            return (workflow, workflowDescription);
        }
        else
        {
            workspan.Log.Information("No process not eligible ever order strategy workflow is set");

            return (null, null);
        }
    }

    #endregion
}