using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class CardDetailsToDisputeClassMap : ValidatableDataMapBase<CardDetailsToDisputeClass, string, int>
{
    protected override string SourceFilePath =>
        $"./Resources/Lists/DisputeOptimization/CardDetailsToDisputeClassMap.csv";

    protected override void ImportRecord(Dictionary<string, int> map, CardDetailsToDisputeClass record)
    {
        if (string.IsNullOrWhiteSpace(record.CardDetails) || record.DisputeClass <= 0)
        {
            Workspan.Current?.Log.Fatal("Invalid DisputeClass {DisputeClass} or Issuer: {CareDetails}",
                record.DisputeClass, record.CardDetails);
            return;
        }

        TryAddMapItem(map, record.CardDetails, record.DisputeClass);
    }
}

record CardDetailsToDisputeClass
{
    public string CardDetails { get; set; }
    public int DisputeClass { get; set; }
}