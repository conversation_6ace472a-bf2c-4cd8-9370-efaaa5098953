using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class BinToDisputeClassMap : ValidatableDataMapBase<BinToDisputeClass, string, string>
{
    protected override string SourceFilePath => $"./Resources/Lists/DisputeOptimization/BinToDisputeClassMap.csv";

    protected override void ImportRecord(Dictionary<string, string> map, BinToDisputeClass record)
    {
        if (string.IsNullOrWhiteSpace(record.Bin) || string.IsNullOrWhiteSpace(record.DisputeClass))
        {
            Workspan.Current?.Log.Fatal("Invalid DisputeClass {DisputeClass} or Issuer: {Bin}", record.DisputeClass,
                record.Bin);
            return;
        }

        if (record.Bin.Length != 6 && record.Bin.Length != 8)
        {
            Workspan.Current?.Log.Fatal("Invalid Bin length (musty be 6 or 8): {Bin}", record.Bin);
            return;
        }

        TryAddMapItem(map, record.Bin, record.DisputeClass);

        // Adding fallback 6 digit bin
        TryAddMapItemIfNotExist(map, record.Bin.Truncate(6), record.DisputeClass);
    }
}

record BinToDisputeClass
{
    public string Bin { get; set; }
    public string DisputeClass { get; set; }
}