using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class IssuerClassToSubclassMap : ValidatableDataMapBase<IssuerClassToSubclass, string, int>
{
    protected override string SourceFilePath =>
        $"./Resources/Lists/DisputeOptimization/IssuerClassToSubclassMap.csv";

    protected override void ImportRecord(Dictionary<string, int> map, IssuerClassToSubclass record)
    {
        if (string.IsNullOrWhiteSpace(record.Issuer) || string.IsNullOrWhiteSpace(record.Class))
        {
            Workspan.Current?.Log.Fatal("Invalid Issuer {Issuer} or Class: {Class}",
                record.Issuer, record.Class);
            return;
        }

        TryAddMapItem(map, CreateMapKey(record.Issuer, record.Class), record.Subclass);
    }


    public string CreateMapKey(string issuer, string disputeClass) => $"{issuer}__{disputeClass}";
}

record IssuerClassToSubclass
{
    public string Issuer { get; set; }
    public string Class { get; set; }
    public int Subclass { get; set; }
}