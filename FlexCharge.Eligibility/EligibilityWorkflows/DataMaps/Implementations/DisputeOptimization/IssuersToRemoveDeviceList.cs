using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class IssuersToRemoveDeviceList : ValidatableDataMapBase<IssuerToBinListRecord, int, string>
{
    protected override string SourceFilePath => $"./Resources/Lists/BinLists/Remove_Device_BinToIssuerList.csv";

    protected override void ImportRecord(Dictionary<int, string> map, IssuerToBinListRecord record)
    {
        if (string.IsNullOrWhiteSpace(record.BIN) || !int.TryParse(record.BIN, out var bin) ||
            string.IsNullOrWhiteSpace(record.Issuer))
        {
            Workspan.Current?.Log.Warning("Invalid BIN {Bin} or Issuer: {Issuer}", record.BIN, record.Issuer);
            return;
        }

        TryAddMapItem(map, bin, record.Issuer);
    }
}

record IssuerToBinListRecord
{
    public string Issuer { get; set; }
    public string Network { get; set; }

    public string BIN { get; set; }
    //
    // [CsvHelper.Configuration.Attributes.Ignore]
    // [CsvHelper.Configuration.Attributes.Name("#Evaluates")]
    // public string Evaluates { get; set; }
}