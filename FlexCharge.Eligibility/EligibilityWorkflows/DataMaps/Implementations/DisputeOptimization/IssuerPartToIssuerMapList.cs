using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class IssuerPartToIssuerMapValidatableDataMapList : ValidatableDataMapListBase<IssuerPartToIssuer, string, string>
{
    protected override string SourceFilePath => $"./Resources/Lists/DisputeOptimization/IssuerPartToIssuerMap.csv";

    protected override void ImportRecord(Dictionary<string, string> map, IssuerPartToIssuer record)
    {
        if (string.IsNullOrWhiteSpace(record.IssuerPart) || string.IsNullOrWhiteSpace(record.Issuer))
        {
            Workspan.Current?.Log.Fatal("Invalid IssuerPart {IssuerPart} or Issuer: {Issuer}", record.IssuerPart,
                record.Issuer);
            return;
        }

        TryAddMapItem(map, record.IssuerPart, record.Issuer);
    }
}

record IssuerPartToIssuer
{
    public string IssuerPart { get; set; }
    public string Issuer { get; set; }
}