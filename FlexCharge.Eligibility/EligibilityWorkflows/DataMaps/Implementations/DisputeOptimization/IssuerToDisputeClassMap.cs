using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;

class IssuerToDisputeClassMap : ValidatableDataMapBase<IssuerToDisputeClass, string, int>
{
    protected override string SourceFilePath => $"./Resources/Lists/DisputeOptimization/IssuerToDisputeClassMap.csv";

    protected override void ImportRecord(Dictionary<string, int> map, IssuerToDisputeClass record)
    {
        if (string.IsNullOrWhiteSpace(record.Issuer) || record.DisputeClass <= 0)
        {
            Workspan.Current?.Log.Fatal("Invalid DisputeClass {DisputeClass} or Issuer: {Issuer}", record.DisputeClass,
                record.Issuer);
            return;
        }

        TryAddMapItem(map, record.Issuer, record.DisputeClass);
    }
}

record IssuerToDisputeClass
{
    public string Issuer { get; set; }
    public int DisputeClass { get; set; }
}