using System.Collections.Generic;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.RetryStrategies;

class ErrorCodeToRetryStrategyParametersMap : ValidatableDataMapBase<ProviderResponseToRetryStrategyParameters, string,
    RetryStrategyParameters>
{
    protected override string SourceFilePath =>
        $"./Resources/Lists/RetryStrategies/ErrorCodeToRecycleStrategyParametersMap.csv";

    protected override void ImportRecord(Dictionary<string, RetryStrategyParameters> map,
        ProviderResponseToRetryStrategyParameters record)
    {
        if (string.IsNullOrWhiteSpace(record.ErrorCode) || record.MaxRetries < 0)
        {
            Workspan.Current?.Log.Fatal("Invalid ErrorCode {ErrorCode} or MaxRetries: {MaxRetries}",
                record.ErrorCode,
                record.MaxRetries);
            return;
        }

        TryAddMapItem(map, record.ErrorCode, new RetryStrategyParameters(
            record.ErrorCode, record.ErrorGroup, record.ErrorMessage, record.MaxRetries));
    }
}

record ProviderResponseToRetryStrategyParameters
{
    public string ErrorCode { get; set; }
    public string ErrorGroup { get; set; }
    public string ErrorMessage { get; set; }
    public int MaxRetries { get; set; }
};

record RetryStrategyParameters(string ErrorCode, string ErrorGroup, string ErrorMessage, int MaxRetries);