using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Mvc.Validators;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Shared.Payments;

namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies;

class ProviderNormalizedResponseToActionMap : ValidatableDataMapBase
    <ProviderNormalizedResponseToActionRecord, string, Action>
{
    protected override bool ThrowExceptionOnDuplicateKey => true;

    protected override string SourceFilePath =>
        $"./Resources/Lists/EligibilityStrategies/NormalizedResponseToActionMap.csv";

    protected override void ImportRecord(Dictionary<string, Action> map,
        ProviderNormalizedResponseToActionRecord record)
    {
        ValidateByDataAnnotationAttributesAndThrow(record);

        TryAddMapItem(map, record.ResponseCode,
            new Action(record.CitAction, record.MitAction, record.When));

        if (!InternalResponseMapper.IndexTable.TryGetValue(record.ResponseCode, out var mappedResponse))
        {
            throw new FlexChargeException(
                $"Response message is not found in the internal response mapper: '{record.ResponseMessage}'");
        }

        if (record.ResponseCode == "0")
        {
            if ((record.CitAction != CitAction.Approve || record.MitAction != MitAction.Approve))
                throw new FlexChargeException("APPROVED action is not defined for the response code 0");
        }
        else
        {
            if (record.CitAction == CitAction.Approve || record.MitAction == MitAction.Approve)
                throw new FlexChargeException("APPROVED action defined for the response code <> 0");
        }
    }

    protected override void ValidateLoadedMap(Dictionary<string, Action> map)
    {
        base.ValidateLoadedMap(map);

        if (!map.TryGetValue("0", out var action))
        {
            throw new FlexChargeException("No APPROVED action is defined");
        }
    }
}

record ProviderNormalizedResponseToActionRecord
{
    [Required] public string ResponseMessage { get; init; }
    public string ResponseCode { get; init; }
    public string ResponseGroup { get; init; }
    public string DeclineType { get; init; }

    public CitAction CitAction { get; init; }
    public MitAction MitAction { get; init; }

    [RequiredIf(nameof(MitAction), MitAction.Cascade)]
    public When? When { get; init; }

    public string? Comments { get; init; }
}

public enum CitAction
{
    Approve = 100,
    Cascade,
    DoNotTryAgain,
}

public enum MitAction
{
    Approve = 100,
    Cascade,
    DoNotTryAgain,
}

public enum When
{
    Now = 100,
    Later,
}

public record Action(CitAction CitAction, MitAction MitAction, When? When);