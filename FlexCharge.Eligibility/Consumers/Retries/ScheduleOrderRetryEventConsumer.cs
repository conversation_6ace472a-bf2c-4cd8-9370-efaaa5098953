using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.OffSessionRetryService;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.Retries;

public class ScheduleOrderRetryEventConsumerDefinition
    : ConsumerDefinitionBase<ScheduleOrderRetryEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_RETRY_INTERVAL_IN_HOURS = 1;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*20 + 60
        r.Exponential(3,
            TimeSpan.FromSeconds(60), // First retry attempt delay
            TimeSpan.FromMinutes(
                5), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(20)); // Increment multiplier between retries

        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

public class ScheduleOrderRetryEventConsumer : IdempotentEventConsumer<ScheduleOrderRetryEvent>
{
    private readonly IActivityService _activityService;
    private readonly IOffSessionRetrySchedulerService _offSessionRetrySchedulerService;

    public ScheduleOrderRetryEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IActivityService activityService,
        IOffSessionRetrySchedulerService offSessionRetrySchedulerService) : base(serviceScopeFactory)
    {
        _activityService = activityService;
        _offSessionRetrySchedulerService = offSessionRetrySchedulerService;
    }

    protected override async Task ConsumeEvent(ScheduleOrderRetryEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("OrderId", message.OrderId);

        try
        {
            await _offSessionRetrySchedulerService.ScheduleOrderRetryAsync(message.OrderId);
        }
        catch (ConcurrentEvaluationCannotObtainLockException e)
        {
            Workspan.RecordFatalException(e, "Cannot schedule order retry due to concurrent evaluation lock");

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed scheduling order retry", e);
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e, "Failed to schedule order retry");
        }
    }
}