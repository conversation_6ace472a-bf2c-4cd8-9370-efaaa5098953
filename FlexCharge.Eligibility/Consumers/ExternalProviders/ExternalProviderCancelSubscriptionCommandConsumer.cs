using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.PaymentsService;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.ExternalProviders;

#region Retry Configuration

public class ExternalProviderCancelSubscriptionCommandConsumerDefinition
    : ConsumerDefinitionBase<ExternalProviderCancelSubscriptionCommandConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24 * 7;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class ExternalProviderCancelSubscriptionCommandConsumer
    : IdempotentCommandConsumer<ExternalProviderCancelSubscriptionCommand>
{
    private readonly IPaymentsService _paymentsService;
    private readonly IOrderService _orderService;

    public ExternalProviderCancelSubscriptionCommandConsumer(IServiceScopeFactory serviceScopeFactory,
        IPaymentsService paymentsService, IOrderService orderService) : base(serviceScopeFactory)
    {
        _paymentsService = paymentsService;
        _orderService = orderService;
    }

    protected override async Task ConsumeCommand(ExternalProviderCancelSubscriptionCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("ExternalAccountId", command.ExternalAccountId)
                .LogEnterAndExit();

            var orderAndMerchant = await _orderService.GetOrderAndMerchantOrDefaultAsync(command.OrderId);

            if (orderAndMerchant == null)
                throw new FlexChargeException("Order not found");

            var (order, merchant) = orderAndMerchant.Value;

            if (!order.IsMIT())
                throw new FlexChargeException("Order is not MIT");

            var cancellationResult =
                await _paymentsService.CancelExternalSubscriptionAsync(order, merchant,
                    command.CanCancelActiveSubscription);

            if (cancellationResult?.Message?.Success != true)
            {
                if (cancellationResult?.Message?.CanBeRetried == false)
                {
                    Workspan.Log.Fatal("Cannot retry subscription cancellation");
                }
                else
                {
                    throw new FlexChargeException("Failed cancelling external subscription");
                }
            }
            else
            {
                if (cancellationResult?.Message?.CancellationSkippedForActiveSubscription == true)
                {
                    Workspan.Log.Information("External subscription cancellation skipped for active subscription");
                }
                else
                {
                    Workspan.Log.Information("External subscription cancelled successfully");
                }
            }
        }
        catch (Exception e)
        {
            Workspan
                .Tag("RetryCount", Context.GetRetryCount())
                .Tag("RedeliveryCount", Context.GetRedeliveryCount())
                .RecordFatalException(e,
                    "Failed cancelling external subscription. Returning message to retry queue.");

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed cancelling external subscription - initiating retry", e);
        }
    }
}