using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Enums;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Consumers;

public static class MerchantUpdateHelper
{
    public static void UpdateMerchant(Merchant merchant, MerchantCreatedEvent message)
    {
        UpdateMerchantInternal(merchant, message);
    }

    public static void UpdateMerchant(Merchant merchant, MerchantUpdatedEvent message)
    {
        UpdateMerchantInternal(merchant, message);
    }

    private static void UpdateMerchantInternal(Merchant merchant, MerchantCreateOrUpdateEventBase message)
    {
        var validIntegrationType = Utils.EnumHelpers.TryParseEnum(message.IntegrationType,
            out MerchantIntegrationTypes integrationType);

        if (!validIntegrationType)
        {
            Workspan.Current!
                .Tag("MerchantId", message.MerchantId)
                .Tag("IntegrationType", message.IntegrationType)
                .Log.Fatal(
                    "Invalid merchant IntegrationType");
        }

        merchant.IntegrationPartnerId = message.IntegrationPartnerId;

        merchant.IntegrationType = validIntegrationType ? integrationType.ToString() : null;


        merchant.Mid = message.MerchantId;
        merchant.Pid = message.PartnerId;
        merchant.Aid = message.AccountId;

        merchant.Pcidss = message.Pcidss;

        // Not editable yet on Portal
        // if (validIntegrationType)
        // {
        //     merchant.IntegrationType = integrationType.ToString();
        // }

        merchant.IsActive = message.IsActive;
        merchant.IsLocked = message.IsLocked;

        merchant.GhostModeThrottlePercentage = message.GhostModeThrottlePercentage;
        merchant.OfferRequestsRateLimitIntervalMS = message.OfferRequestsRateLimitIntervalMS;
        merchant.OfferRequestsRateLimitCount = message.OfferRequestsRateLimitCount;
        merchant.OfferRequestsMaxPerDay = message.OfferRequestsMaxPerDay;
        merchant.OfferRequestsThrottlePercentage = message.OfferRequestsThrottlePercentage;
        merchant.Offer_NSF_RequestsThrottle_Percentage = message.Offer_NSF_RequestsThrottle_Percentage;
        merchant.Orders_MaxMonthlyAmount = message.Orders_MaxMonthlyAmount;

        merchant.IsBureauActiveForProduction = message.IsBureauProductionActive;
        merchant.IsMitEnabled = message.IsMitEnabled;

        merchant.CITEvaluateAsync = message.IsCitEvaluateAsync;
        merchant.MITEvaluateAsync = message.IsMitEvaluateAsync;

        merchant.Dba = message.Dba;
        merchant.Mcc = message.Mcc;

        merchant.RiskLevel = message.RiskLevel;
        merchant.RiskLevel_Visa = message.RiskLevel_Visa;

        merchant.SupportedCountries = message.SupportedCountries != null
            ? string.Join(',', message.SupportedCountries).ToUpperInvariant()
            : null;

        merchant.CustomerSupportName = message.CustomerSupportName;
        merchant.CustomerSupportEmail = message.CustomerSupportEmail;
        merchant.CustomerSupportPhone = message.CustomerSupportPhone;
        merchant.CustomerSupportLink = message.CustomerSupportLink;

        merchant.VirtualTerminalEnabled = message.VirtualTerminalEnabled;
        merchant.MITGetSiteByDynamicDescriptorEnabled = message.MITGetSiteByDynamicDescriptorEnabled;
        merchant.MITConsumerNotificationsEnabled = message.MITConsumerNotificationsEnabled;
        merchant.MITConsumerCuresEnabled = message.MITConsumerCuresEnabled;
        merchant.SenseJSOptional = message.IsSenseJsOptional;
        merchant.UIWidgetOptional = message.UIWidgetOptional;
        merchant.CITConsumerNotificationsEnabled = message.CITConsumerNotificationsEnabled;

        merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = message.UseDefaultSiteForUnknownMerchantUrlsEnabled;

        merchant.BillingInformationOptional = message.BillingInformationOptional;
        merchant.Global3DSEnabled = message.Global3DSEnabled;

        merchant.MinOrderAmount = message.MinOrderAmount;
        merchant.MaxOrderAmount = message.MaxOrderAmount;

        merchant.DynamicAuthorizationDiscountThrottlePercentage =
            message.DynamicAuthorizationDiscountThrottlePercentage;

        merchant.CaptureRequired = message.CaptureRequired;

        merchant.SchemeTransactionIdEnabled = message.SchemeTransactionIdEnabled;

        merchant.MITImmediateRetryEnabled = message.MITImmediateRetryEnabled;

        merchant.IsAvsRequired = message.IsAvsRequired;
        merchant.IsCvvRequired = message.IsCvvRequired;

        merchant.EnableGlobalNetworkTokenization = message.EnableGlobalNetworkTokenization;
        merchant.AccountUpdaterEnabled = message.AccountUpdaterEnabled;

        merchant.PayerEnabled = message.PayerEnabled;

        merchant.RedactIpEnabled = message.RedactIpEnabled;


        merchant.RecyclingStrategyWorkflowId = message.RecyclingStrategyWorkflowId;
        merchant.EligibilityStrategyWorkflowId = message.EligibilityStrategyWorkflowId;
        merchant.NotEligibleOrderProcessingWorkflowId = message.NotEligibleOrderProcessingWorkflowId;
        merchant.NotEligibleEverOrderProcessingWorkflowId = message.NotEligibleEverOrderProcessingWorkflowId;
    }
}