using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Services.StripeServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.PaymentProviders;

#region RetryConfiguration

public class PaymentProviderSynchronizeOrdersEventConsumerDefinition
    : ConsumerDefinitionBase<PaymentProviderSynchronizeOrdersCommandConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class PaymentProviderSynchronizeOrdersCommandConsumer
    : IdempotentCommandConsumer<PaymentProviderSynchronizeOrdersCommand>
{
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;

    public PaymentProviderSynchronizeOrdersCommandConsumer(IServiceScopeFactory serviceScopeFactory,
        IPublishEndpoint publisher, IActivityService activityService) :
        base(serviceScopeFactory)
    {
        _publisher = publisher;
        _activityService = activityService;
    }

    protected override async Task ConsumeCommand(PaymentProviderSynchronizeOrdersCommand command,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.Mid)
            .Baggage("AccountId", command.AccountId)
            .Baggage("Provider", command.Provider);

        int failedInvoicesProcessed = 0;
        try
        {
            using var serviceScope = ServiceScopeFactory.CreateScope();

            IAsyncEnumerable<InvoiceDescription> failedInvoicesIterator;

            #region Observability

            await _activityService.CreateActivityAsync(
                Activities.ExternalPaymentProviderActivities.ExternalOrdersSynchronization_Started,
                set: set =>
                    set.TenantId(command.Mid)
                        .Meta(meta => meta
                            .ServiceProvider(command.Provider)
                            .SetValue("AccountId", command.AccountId))
            );

            #endregion


            switch (command.Provider)
            {
                case "Stripe":
                    var stripeReportsService =
                        serviceScope.ServiceProvider.GetRequiredService<IStripeSubscriptionsService>();
                    failedInvoicesIterator = stripeReportsService.GetFailedInvoicesForActiveSubscriptionsAsync(
                        command.Mid,
                        command.AccountId);
                    break;

                default:
                    throw new NotImplementedException($"Provider {command.Provider} is not supported");
            }

            //IProcessDeclineService<Invoice>

            //int failedInvoicesCount = failedInvoices.Count;
            await foreach (var failedInvoice in failedInvoicesIterator)
            {
                await _publisher.RunIdempotentCommandWithoutResponseAsync(
                    new PaymentProviderCreateOrderForFailedInvoiceCommand(
                        command.Mid, command.Provider, command.AccountId,
                        failedInvoice.Id, failedInvoice.SubscriptionId));

                failedInvoicesProcessed++;
            }

            #region Observability

            await _activityService.CreateActivityAsync(
                Activities.ExternalPaymentProviderActivities.ExternalOrdersSynchronization_InvoiceProcessingCompleted,
                set: set =>
                    set.TenantId(command.Mid)
                        .Meta(meta => meta
                            .ServiceProvider(command.Provider)
                            .SetValue("AccountId", command.AccountId)
                            .SetValue("FailedInvoicesCount", failedInvoicesProcessed)));

            #endregion
        }
        catch (Exception e)
        {
            #region Observability

            Workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(
                Activities.ExternalPaymentProviderErrorActivities.ExternalOrdersSynchronization_Error, data: e,
                set: set => set
                    .TenantId(command.Mid)
                    .Meta(meta => meta
                        .ServiceProvider(command.Provider)
                        .SetValue("AccountId", command.AccountId)
                        .SetValue("FailedInvoicesCount", failedInvoicesProcessed)));

            #endregion


            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed synchronizing orders with external provider", e);
        }
    }
}