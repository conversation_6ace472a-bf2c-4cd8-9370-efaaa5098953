using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Adapters.Common.Ingestion.Webhooks;
using FlexCharge.Eligibility.Adapters.Common.Services.ProcessDeclineService;
using FlexCharge.Eligibility.Services.StripeServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Stripe;

namespace FlexCharge.Eligibility.Consumers.PaymentProviders;

#region RetryConfiguration

public class PaymentProviderCreateOrderForFailedInvoiceEventConsumerDefinition
    : ConsumerDefinitionBase<PaymentProviderCreateOrderForFailedInvoiceEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 96;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(1,
            TimeSpan.FromMinutes(1), // First retry attempt delay
            TimeSpan.FromMinutes(
                5), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class PaymentProviderCreateOrderForFailedInvoiceEventConsumer
    : IdempotentCommandConsumer<PaymentProviderCreateOrderForFailedInvoiceCommand>
{
    protected override bool LogEnterAndExit => false;

    private readonly IStripeInvoicesService _stripeInvoicesService;
    private readonly IActivityService _activityService;

    public PaymentProviderCreateOrderForFailedInvoiceEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IStripeInvoicesService stripeInvoicesService, IActivityService activityService) :
        base(serviceScopeFactory)
    {
        _stripeInvoicesService = stripeInvoicesService;
        _activityService = activityService;
    }

    protected override async Task ConsumeCommand(PaymentProviderCreateOrderForFailedInvoiceCommand command,
        CancellationToken cancellationToken)
    {
        var orderToBeCreatedId = Guid.NewGuid();

        Workspan
            .Baggage("Mid", command.Mid)
            .Baggage("AccountId", command.AccountId)
            .Baggage("Provider", command.Provider)
            .Baggage("OrderId", orderToBeCreatedId)
            .LogEnterAndExit();

        try
        {
            using var serviceScope = ServiceScopeFactory.CreateScope();

            #region Observability

            Workspan.Log.Information("Order creation started");

            // await _activityService.CreateActivityAsync(
            //     ExternalPaymentProviderActivities.ExternalInvoiceProcessing_OrderCreation_Started,
            //     set: set => set
            //         .TenantId(command.Mid)
            //         .CorrelationId(orderToBeCreatedId)
            //         .Meta(meta => meta
            //             .ServiceProvider(command.Provider)
            //             .SetValue("AccountId", command.AccountId)
            //             .SetValue("InvoiceId", command.InvoiceId)));

            #endregion

            switch (command.Provider)
            {
                case "Stripe":
                    var processDeclineService =
                        serviceScope.ServiceProvider.GetRequiredService<IProcessDeclineService<Invoice>>();

                    var processingContext =
                        new DeclineProcessingContext(new MerchantConfiguration(command.Mid, null), command.AccountId,
                            command.Provider);

                    var invoice = await _stripeInvoicesService.GetInvoiceAsync(command.AccountId, command.InvoiceId);

                    var orderCreationResult =
                        await processDeclineService.TryCreateOrderAsync(orderToBeCreatedId, invoice, processingContext);

                    switch (orderCreationResult)
                    {
                        case OrderCreationResult.OrderCreated:

                            #region Observability

                            Workspan.Log.Information("Order created successfully");

                            await _activityService.CreateActivityAsync(
                                ExternalPaymentProviderActivities.ExternalInvoiceProcessing_OrderCreated,
                                set: set => set
                                    .TenantId(command.Mid)
                                    .CorrelationId(orderToBeCreatedId)
                                    .Meta(meta => meta
                                        .ServiceProvider(command.Provider)
                                        .SetValue("AccountId", command.AccountId)
                                        .SetValue("InvoiceId", command.InvoiceId)));

                            #endregion

                            break;

                        case OrderCreationResult.NotCreated_NotCompatible:

                            #region Observability

                            Workspan.Log.Information("Order not created - not compatible");

                            // await _activityService.CreateActivityAsync(
                            //     ExternalPaymentProviderActivities.ExternalInvoiceProcessing_OrderNotCreated,
                            //     set: set => set
                            //         .TenantId(command.Mid)
                            //         .Meta(meta => meta
                            //             .ServiceProvider(command.Provider)
                            //             .SetValue("AccountId", command.AccountId)
                            //             .SetValue("InvoiceId", command.InvoiceId)));

                            #endregion

                            break;

                        case OrderCreationResult.NotCreated_AlreadyInProcessing:

                            #region Observability

                            Workspan.Log.Information("Order not created - already in processing");

                            // await _activityService.CreateActivityAsync(
                            //     ExternalPaymentProviderActivities.ExternalInvoiceProcessing_OrderNotCreated_AlreadyCreated,
                            //     set: set => set
                            //         .TenantId(command.Mid)
                            //         .Meta(meta => meta
                            //             .ServiceProvider(command.Provider)
                            //             .SetValue("AccountId", command.AccountId)
                            //             .SetValue("InvoiceId", command.InvoiceId)));

                            #endregion

                            break;

                        case OrderCreationResult.NotCreated_MerchantIsInactive:

                            #region Observability

                            Workspan.Log.Information("Order not created - merchant is inactive");

                            // await _activityService.CreateActivityAsync(
                            //     ExternalPaymentProviderActivities
                            //         .ExternalInvoiceProcessing_OrderNotCreated_MerchantInactive,
                            //     set: set => set
                            //         .TenantId(command.Mid)
                            //         .Meta(meta => meta
                            //             .ServiceProvider(command.Provider)
                            //             .SetValue("AccountId", command.AccountId)
                            //             .SetValue("InvoiceId", command.InvoiceId)));

                            #endregion

                            break;

                        case OrderCreationResult.Error:
                            throw new FlexChargeException("Error creating order for failed invoice");

                        default:
                            throw new NotImplementedException(
                                $"Order creation result {orderCreationResult} is not supported");
                    }

                    break;

                default:
                    throw new NotImplementedException($"Provider {command.Provider} is not supported");
            }
        }
        catch (Exception e)
        {
            #region Observability

            Workspan.RecordFatalException(e, "Error creating order for failed invoice");

            await _activityService.CreateActivityAsync(
                ExternalPaymentProviderErrorActivities.ExternalInvoiceProcessing_OrderCreation_Error,
                set: set => set
                    .TenantId(command.Mid)
                    .CorrelationId(orderToBeCreatedId)
                    .Meta(meta => meta
                        .ServiceProvider(command.Provider)
                        .SetValue("AccountId", command.AccountId)
                        .SetValue("InvoiceId", command.InvoiceId)));

            #endregion

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed creating order for failed invoice from external provider", e);
        }
    }
}