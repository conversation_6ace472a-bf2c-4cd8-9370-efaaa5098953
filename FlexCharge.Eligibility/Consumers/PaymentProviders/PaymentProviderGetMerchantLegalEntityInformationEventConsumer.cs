using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.Services.StripeServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.PaymentProviders;

#region RetryConfiguration

public class PaymentProviderGetMerchantLegalEntityInformationEventConsumerDefinition
    : ConsumerDefinitionBase<PaymentProviderGetMerchantLegalEntityInformationEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class PaymentProviderGetMerchantLegalEntityInformationEventConsumer
    : IdempotentCommandConsumer<PaymentProviderGetMerchantLegalEntityInformationCommand>
{
    private readonly IPublishEndpoint _publisher;

    public PaymentProviderGetMerchantLegalEntityInformationEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IPublishEndpoint publisher) :
        base(serviceScopeFactory)
    {
        _publisher = publisher;
    }

    protected override async Task ConsumeCommand(PaymentProviderGetMerchantLegalEntityInformationCommand command,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.Mid)
            .Baggage("AccountId", command.AccountId)
            .Baggage("Provider", command.Provider);

        try
        {
            using var serviceScope = ServiceScopeFactory.CreateScope();

            MerchantAccountInformation accountInfo;

            IStripeMerchantInformationService stripeMerchantInformationReportsService;
            switch (command.Provider)
            {
                case "Stripe":
                    stripeMerchantInformationReportsService = serviceScope.ServiceProvider
                        .GetRequiredService<IStripeMerchantInformationService>();

                    break;
                default:
                    throw new NotImplementedException($"Provider {command.Provider} is not supported");
            }

            accountInfo =
                await stripeMerchantInformationReportsService.GetMerchantAccountInformationAsync(command.Mid,
                    command.AccountId);


            await _publisher.Publish(
                new ExternalProviderMerchantAccountInformationRetrievedEvent(command.Mid, command.AccountId)
                {
                    BusinessType = accountInfo.BusinessType,
                    CompanyStructure = accountInfo.CompanyStructure,
                    Mcc = accountInfo.Mcc,
                    BusinessName = accountInfo.BusinessName,
                    BusinessUrl = accountInfo.BusinessUrl,
                    AccountCountry = accountInfo.AccountCountry,
                    AccountDefaultCurrency = accountInfo.AccountDefaultCurrency,
                    AccountType = accountInfo.AccountType,
                    AccountEmail = accountInfo.AccountEmail,
                    SupportEmail = accountInfo.SupportEmail,
                    SupportPhone = accountInfo.SupportPhone,
                    SupportUrl = accountInfo.SupportUrl,
                    SupportAddress = new Address()
                    {
                        City = accountInfo.SupportAddress?.City,
                        CountryCode = accountInfo.SupportAddress?.CountryCode,
                        Address1 = accountInfo.SupportAddress?.Line1,
                        Address2 = accountInfo.SupportAddress?.Line2,
                        Zip = accountInfo.SupportAddress?.PostalCode,
                        StateCode = accountInfo.SupportAddress?.StateCode
                    },
                    PaymentsDescriptor = accountInfo.PaymentsDescriptor,
                    CardPaymentsDescriptorPrefix = accountInfo.CardPaymentsDescriptorPrefix
                });
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed retrieving merchant configuration from external provider", e);
        }
    }
}