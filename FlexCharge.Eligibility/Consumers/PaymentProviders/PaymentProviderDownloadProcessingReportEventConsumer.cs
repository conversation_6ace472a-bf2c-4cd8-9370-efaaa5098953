using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Services.StripeReportsService;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers.PaymentProviders;

public class PaymentProviderDownloadProcessingReportEventConsumer
    : IdempotentCommandConsumer<PaymentProviderDownloadProcessingReportCommand>
{
#if DEBUG
    const int RETRY_INTERVAL_IN_SECONDS = 60;
#else
    const int RETRY_INTERVAL_IN_SECONDS = 60 * 15;
#endif

    public PaymentProviderDownloadProcessingReportEventConsumer(IServiceScopeFactory serviceScopeFactory) : base(
        serviceScopeFactory)
    {
    }

    protected override async Task ConsumeCommand(PaymentProviderDownloadProcessingReportCommand command,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.Mid)
            .Baggage("AccountId", command.AccountId)
            .Baggage("Provider", command.Provider)
            .Baggage("ReportRunId", command.ReportRunId)
            .Baggage("RetryAttempt", command.RetryAttempt)
            .Baggage("ReportRunTime", command.ReportRunTime);

        bool reportDownloadedSuccessfully = false;

        try
        {
            using var serviceScope = ServiceScopeFactory.CreateScope();

            Workspan.Log.Information($"Trying to download report");

            switch (command.Provider)
            {
                case "Stripe":

                    var stripeReportsService = serviceScope.ServiceProvider.GetRequiredService<IStripeReportsService>();

                    reportDownloadedSuccessfully =
                        await stripeReportsService.TryToDownloadReportAsync(command.Mid, command.AccountId,
                            command.ReportName, command.ReportRunId);

                    break;

                default:
                    throw new NotImplementedException($"Provider {command.Provider} is not supported");
            }

            if (!reportDownloadedSuccessfully)
            {
                Workspan.Log.Information($"Report is not ready yet - retrying");
            }
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
        }

        if (!reportDownloadedSuccessfully)
        {
            if (DateTime.UtcNow - command.ReportRunTime >= TimeSpan.FromHours(24))
            {
                Workspan
                    .Log.Fatal($"Report is not ready after 24 hours - stopping");
            }
            else
            {
                Workspan.Log.Information($"Report is not downloaded - retrying");

                // Requeue message with delay
                await Context.ScheduleSend(
                    TimeSpan.FromSeconds(RETRY_INTERVAL_IN_SECONDS),
                    command with
                    {
                        MessageIdempotencyKey = Guid.NewGuid(), // To allow retry 
                        RetryAttempt = command.RetryAttempt + 1
                    });
            }
        }
    }
}