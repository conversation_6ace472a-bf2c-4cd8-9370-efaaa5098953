using System;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.EligibilityService;

public static class WorkflowFactory
{
    public static async Task<Workflow<TContext>> BuildWorkflowAsync<TContext>(
        IServiceScopeFactory serviceScopeFactory,
        (WorkflowBuilder<TContext> Workflow, WorkflowDescription Description) workflowToBuild,
        TContext workflowContext, ValueStorage valueStorage,
        string workflowsExternalParameters, bool serializeWorkflow)
        where TContext : IExecutionContext
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var workflow = await workflowToBuild.Workflow.BuildAsync(
            workflowToBuild.Description,
            workflowContext, valueStorage, serviceScopeFactory, workflowsExternalParameters);

        if (serializeWorkflow)
        {
            #region Workflow Serialization

            try
            {
                using var serviceScope = serviceScopeFactory.CreateScope();

                // Important!!! Use scoped workflowDbContext for SerializeWorkflow() call to avoid problems
                var workflowDbContext = serviceScope.ServiceProvider.GetRequiredService<WorkflowPostgreSQLDbContext>();
                var workflowService = serviceScope.ServiceProvider.GetRequiredService<IWorkflowService>();

                await workflowService.SerializeWorkflow(workflow, workflowToBuild.Description,
                    workflowDbContext,
                    workflowDbContext.Workflows, true);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Cannot serialize workflow");
            }

            #endregion
        }

        return workflow;
    }
}