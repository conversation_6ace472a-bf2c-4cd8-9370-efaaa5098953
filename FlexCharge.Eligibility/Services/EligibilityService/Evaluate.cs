#define ENABLE_CURE_TESTING
//#define ENABLE_VIRTUAL_TERMINAL_MODE_TESTING

//#define AUTO_ENABLE_VTERMINAL_FOR_MERCHANTS_WITH_VIRTUAL_TERMINAL_ENABLED


using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DistributedCache;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.Orders.OrderStates;
using FlexCharge.Utils;
using FlexCharge.WorkflowEngine;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;


namespace FlexCharge.Eligibility.Services.EligibilityService;

public partial class EligibilityService
{
    // private const int ELIGIBILITY_REQUEST_TIMEOUT_IN_SECONDS = 20;
    // private const int ELIGIBILITY_REQUEST_TIMEOUT_IN_SECONDS_DEVELOPMENT_ONLY = 60 * 60 * 24;


    #region ProcessEvaluateRequest

    public async Task<EvaluationResult> RunEvaluateRequestAsync(EvaluateRequest request, Guid? partnerID,
        Guid transmitActivityToCreateOrUpdateId,
        EvaluateProcessingType processingType,
        EvaluateRequestType requestType,
        CancellationToken cancellationToken,
        bool isInternalEvaluation,
        Dictionary<string, string> meta = null)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", transmitActivityToCreateOrUpdateId)
            .Tag("Meta", meta)
            .LogEnterAndExit();

        //TODO D: Check id - duplicate order update?

        _normalizeDataService.Normalize(request);

        if (partnerID == Guid.Empty) partnerID = null;

        //Check if merchant exists and alive
        Merchant merchant = null;
        if (request.SiteId == null)
        {
            merchant = await GetMerchantOrDefaultAsync(request.Mid.Value);
        }
        else
        {
            var merchantAndSite = await GetMerchantAndSiteOrDefaultAsync(request.Mid.Value, request.SiteId.Value);

            #region Validating Site. It should exist and belong to this merchant

            if (merchantAndSite.Merchant != null && merchantAndSite.Site == null)
            {
                await _activityService.CreateActivityAsync(EligibilityErrorActivities.Evaluate_UnknownSite,
                    data: request.SiteId,
                    set: set => set.TenantId(request.Mid).CorrelationId(transmitActivityToCreateOrUpdateId));

                workspan.Log
                    .Fatal("Site {SiteId} is unknown for merchant {MerchantId}", request.SiteId, request.Mid);

                throw new NotEligibleException();
            }

            #endregion

            merchant = merchantAndSite.Merchant;
        }

        #region Validating Merchant and PartnerID

        if (merchant == null)
        {
            await _activityService.CreateActivityAsync(EligibilityErrorActivities.Evaluate_UnknownMerchant,
                data: request.Mid,
                set: set => set.TenantId(request.Mid).CorrelationId(transmitActivityToCreateOrUpdateId));
            throw new NotEligibleException();
        }

        #endregion

        partnerID = merchant.Pid;

        if (ShouldEvaluateAsynchronously(processingType, merchant, request))
        {
            try
            {
                var processEvaluateRequestCommand =
                    new ProcessEvaluateRequestCommand
                    {
                        MerchantId = merchant.Mid,
                        Request = JsonConvert.SerializeObject(request),
                        PartnerId = partnerID,
                        OfferId = transmitActivityToCreateOrUpdateId,
                        InternalEvaluation = isInternalEvaluation,
                        RequestType = requestType,
                        OrderMeta = meta
                    };

                // Do not honor cancellation token for async requests
                await _publisher
                    .RunIdempotentCommandWithoutResponseAsync(processEvaluateRequestCommand, default);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                await _activityService.CreateActivityAsync(EligibilityErrorActivities.Eligibility_Error, e,
                    set: set => set.TenantId(request.Mid).CorrelationId(transmitActivityToCreateOrUpdateId));
            }

            var evaluationResult = new EvaluationResult(
                new EvaluateResponse
                {
                    Status = nameof(EligibilityStatusCodes.SUBMITTED),
                    OrderSessionKey = transmitActivityToCreateOrUpdateId
                });

            return await Task.FromResult(evaluationResult);
        }
        else
        {
            try
            {
                var result = await ExecuteRequestAsync(
                    asynchronousEvaluation: false,
                    evaluateRequest: request,
                    partnerId: partnerID, offerId: transmitActivityToCreateOrUpdateId,
                    internalEvaluation: isInternalEvaluation,
                    requestType: requestType,
                    orderMeta: meta,
                    cancellationToken: cancellationToken);

                if (result.EvaluateResponse.Status !=
                    null) // in case of early decline (e.g. due to website blacklist there can be no status -> Decline or Submitted)
                {
                    return new EvaluationResult(result.EvaluateResponse)
                    {
                        ResponseCode = result.ResponseCode,
                        ResponseMessage = result.ResponseMessage
                    };
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.Eligibility_Error, e,
                    set: set => set.TenantId(request.Mid).CorrelationId(transmitActivityToCreateOrUpdateId));
            }

            // If we are here - we do not have a response from the evaluation service
            // So return DECLINED for CIT and SUBMITTED for MIT
            var evaluationResult = new EvaluationResult(
                new EvaluateResponse
                {
                    Status = request.IsMIT == true
                        ? nameof(EligibilityStatusCodes.SUBMITTED) // MIT order can be retried
                        : nameof(EligibilityStatusCodes.DECLINED),
                    OrderSessionKey = transmitActivityToCreateOrUpdateId
                });

            return await Task.FromResult(evaluationResult);
        }

        //return await ProcessEvaluateRequestAsync(merchant, request, partnerID, transmitActivityToCreateOrUpdateId);
    }

    private static bool ShouldEvaluateAsynchronously(EvaluateProcessingType forceAsynchronousProcessing,
        Merchant merchant,
        EvaluateRequest evaluateRequest)
    {
        bool evaluateAsync;

        switch (forceAsynchronousProcessing)
        {
            case EvaluateProcessingType.Synchronous:
                evaluateAsync = false;
                break;

            case EvaluateProcessingType.Asynchronous:
                evaluateAsync = true;
                break;

            case EvaluateProcessingType.AutoSelect:
                bool isVirtualTerminalMode = merchant.VirtualTerminalEnabled &&
                                             evaluateRequest.OrderSource?.ToLower() == nameof(OrderSource.vterminal);

                if (isVirtualTerminalMode)
                {
                    evaluateAsync = false;
                }
                else
                {
                    if (evaluateRequest.IsMIT == true)
                    {
                        evaluateAsync = merchant.MITEvaluateAsync;
                    }
                    else // CIT
                    {
                        evaluateAsync = merchant.CITEvaluateAsync;
                    }
                }

                break;

            default:
                throw new ArgumentOutOfRangeException(nameof(forceAsynchronousProcessing), forceAsynchronousProcessing,
                    null);
        }

        return evaluateAsync;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="asynchronousEvaluation"></param>
    /// <param name="evaluateRequest"></param>
    /// <param name="partnerId"></param>
    /// <param name="offerId"></param>
    /// <param name="internalEvaluation"></param>
    /// <param name="requestType"></param>
    /// <param name="externalAccountId">
    ///     If order is processing on another system, it's account id in this system.
    ///     For example, when recycling Stripe MIT orders
    /// </param>
    /// <param name="externalSubscriptionService"></param>
    /// <param name="orderMeta"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<EvaluationResult> ExecuteRequestAsync(bool asynchronousEvaluation,
        EvaluateRequest evaluateRequest, Guid? partnerId, Guid offerId,
        bool internalEvaluation, EvaluateRequestType requestType,
        Dictionary<string, string>? orderMeta,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>();


        Stopwatch processingTimeStopwatch = Stopwatch.StartNew();
        try
        {
            var result = await ProcessEvaluateRequestAsync(
                asynchronousEvaluation,
                evaluateRequest, partnerId, offerId, requestType,
                internalEvaluation, orderMeta,
                cancellationToken);

            return result;
        }
        catch (NotEligibleException nee)
        {
            workspan.RecordFatalException(nee);
            await _activityService.CreateActivityAsync(
                EligibilityErrorActivities.Eligibility_Error,
                data: nee,
                set => set
                    .TenantId(evaluateRequest.Mid)
                    .CorrelationId(offerId));

            return new EvaluationResult(await NotEligible(new EvaluateResponse()));
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(
                EligibilityErrorActivities.Eligibility_Error,
                data: e,
                set => set
                    .TenantId(evaluateRequest.Mid)
                    .CorrelationId(offerId));

            throw;
        }
        finally
        {
            processingTimeStopwatch.Stop();
            await _activityService.CreateActivityAsync(
                EligibilityActivities.Evaluation_EvaluationRequestEnded,
                set => set
                    .TenantId(evaluateRequest.Mid)
                    .CorrelationId(offerId)
                    .Meta(meta => meta
                        .ProcessingTime(processingTimeStopwatch.ElapsedMilliseconds)
                        .SetValue("MIT", evaluateRequest.IsMIT == true)
                    ));
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="asynchronousEvaluation"></param>
    /// <param name="request"></param>
    /// <param name="partnerID"></param>
    /// <param name="orderToCreateId"></param>
    /// <param name="requestType"></param>
    /// <param name="isInternalEvaluation"></param>
    /// <param name="externalAccountId">
    ///     If order is processing on another system, it's account id in this system.
    ///     For example, when recycling Stripe MIT orders
    /// </param>
    /// <param name="externalSubscriptionService"></param>
    /// <param name="orderMeta"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="NotEligibleException"></exception>
    private async Task<EvaluationResult> ProcessEvaluateRequestAsync(bool asynchronousEvaluation,
        EvaluateRequest request,
        Guid? partnerID,
        Guid orderToCreateId,
        EvaluateRequestType requestType,
        bool isInternalEvaluation,
        Dictionary<string, string>? orderMeta,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == request.Mid);

        bool isMITRequest = request.IsMIT == true;


        #region Security Checks 2

        if (!await CheckIfMerchantExistsActiveAsync(merchant, request.Mid,
                orderToCreateId))
            return new EvaluationResult(await NotEligible(new EvaluateResponse()));

        #endregion

        #region Creating Activity

        Guid activityId = orderToCreateId;

        var transmitActivity = await _activityService.CreateActivityAsync(
            TransmitActivities.Transmit_Evaluate,
            request,
            activityId: activityId,
            set: set => set
                .TenantId(request.Mid)
                .CorrelationId(activityId)
                .Meta(meta =>
                {
                    meta
                        .SetValue("Amount", request.Transaction.Amount)
                        .SetValue("ExternalOrderId", request.OrderId)
                        .SetValue("MIT", request.IsMIT == true);

                    if (request.IsMIT != true)
                    {
                        // For CIT orders audit should be started after expiration (if no activity triggers it before)
                        // otherwise not all activities can be ready for audit (for example, when a user challenge requested)
                        meta
                            .AuditDelay(merchant.GetCITOrderExpirationInterval() +
                                        TimeSpan.FromMinutes(
                                            2)); // to ensure all activities arrived to database and expired order is already processed by an expiration mechanism 
                    }
                }));

        #endregion


        var eligibilityResponse = new EvaluateResponse();

        if (!await CheckAndCorrectTransactionTypeAsync(request, merchant))
            throw new NotEligibleException();

        #region Creating Offer

        var order = await CreateOrderAsync(request, merchant, activityId, requestType, isInternalEvaluation, orderMeta);
        workspan.Baggage("OrderId", order.Id);

        await ProcessInitialDeclineTransactionResultAsync(request, order);
        await _orderStateMachine.FireEligibilityOfferStatusUpdatedEventsAsync(order, null, false);

        #endregion

        EvaluationResult evaluationResult;
        try
        {
#if AUTO_ENABLE_VTERMINAL_FOR_MERCHANTS_WITH_VIRTUAL_TERMINAL_ENABLED
            if (merchant.VirtualTerminalEnabled)
            {
                request.OrderSource = nameof(OrderSource.vterminal);
            }
#endif

            #region Virtual Terminal Mode Testing Support

#if DEBUG

#if ENABLE_VIRTUAL_TERMINAL_MODE_TESTING
            request.OrderSource = nameof(OrderSource.vterminal);
#endif

#endif

            #endregion


            eligibilityResponse.OrderSessionKey = order.Id;

            bool virtualTerminalMode = request.OrderSource?.ToLower() == nameof(OrderSource.vterminal);

            var orderIsInTerminalMode = order.IsInTerminalOrKioskMode();

            var isAnyUserChallengeExecuted = !order.IsAnyUserChallengeExecuted();

            bool initialEvaluationOnTerminal = orderIsInTerminalMode && isAnyUserChallengeExecuted;

            if (orderIsInTerminalMode)
            {
                if (initialEvaluationOnTerminal)
                {
                    workspan.Log.Information("Evaluation started from terminal");
                    await AddActivityAsync(EligibilityActivities.TerminalMode_EvaluationStartedFromTerminal, order);
                }
                else
                {
                    workspan.Log.Information("Evaluation after user challenge");
                    await AddActivityAsync(EligibilityActivities.TerminalMode_EvaluationAfterUserChallenge, order);
                }
            }

            if (merchant.EnableGlobalNetworkTokenization)
                await _paymentsService.RequestNetworkTokenizationAsync(request, order);

            bool skipOriginWhiteListCheck = false;

            // SenseKey is available for CIT requests only, and not for MIT or virtual terminal requests
            // Virtual Terminals are used by merchant's staff, so widgets are useless
            if (!isMITRequest && !virtualTerminalMode)
            {
                #region Matching SenseKey to OrderSessionId

                try
                {
                    bool senseJsIsOptional = merchant.IsSenseJSOptional(order, request);

                    // Matching SenseKey to OrderSessionId (if OrderSessionId unknown)

                    (Guid? SenseKey, IDictionary<string, string> LocationInformation, string BrowserInformation)
                        sessionInformation = default;

                    bool skipSenseKeyMatching =
                        initialEvaluationOnTerminal; // SenseKey is not available for terminal mode

                    if (!skipSenseKeyMatching)
                    {
                        sessionInformation = await MatchOrderToSenseKeyWithRetriesAsync(
                            request, request.Mid.Value,
                            order, merchant, senseJsRequired: !senseJsIsOptional);
                    }
                    else
                    {
                        workspan.Log.Information("Skipping matching SenseKey to order");
                    }


                    // Session is not matched?
                    if (sessionInformation.SenseKey == null)
                    {
                        await FindMerchantSiteAndUpdateParametersOrThrowAsync(request, merchant, order);

                        if (senseJsIsOptional)
                        {
                            skipOriginWhiteListCheck = true;

                            if (!skipSenseKeyMatching)
                            {
                                // Try to match order by heartbeat data
                                // Needs to be called after setting SiteId on Order
                                sessionInformation = await MatchOrderByHeartbeatDataAsync(request, order,
                                    SessionMatchersEnum.CustomerIpSessionMatcher,
                                    SessionMatchersEnum.CustomerEmailSessionMatcher
                                );

                                // Session is still not matched?
                                if (sessionInformation.SenseKey == null)
                                {
                                    // Try to match order by emails
                                    // Needs to be called after setting SiteId on Order
                                    sessionInformation = await MatchOrderByEmailAsync(request, order,
                                        SessionMatchersEnum.CustomerEmailSessionMatcher
                                    );
                                }
                            }
                        }
                    }

                    // Session is matched?
                    if (sessionInformation.SenseKey != null)
                    {
                        eligibilityResponse.SenseKey = sessionInformation.SenseKey.Value.ToString();
                        await SetSiteIdOrderOriginAndMerchantDescriptorAsync(order, merchant, sessionInformation);

                        // SenseKey is matched -> we can request network tokenization with additional information
                        // from client (DeviceDetails). Consumer of this request should decide if it's needed
                        // or skip request if network token is already provisioned for this card
                        if (merchant.EnableGlobalNetworkTokenization)
                            await _paymentsService.RequestNetworkTokenizationAsync(request, order);
                    }

                    await _dbContext.SaveChangesAsync();
                }
                catch (NotEligibleException nee)
                {
                    if (nee is NotEligibleCannotDetermineSiteIdException)
                    {
                        await _orderStateMachine.SetOfferStateToOnHoldOrCancelled(order);
                    }
                    else
                    {
                        await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligible);
                    }

                    return new EvaluationResult(SanitizeEvaluateResponse(new EvaluateResponse()));
                }

                #endregion
            }
            else
            {
                workspan.Log.Information("Skipping matching order to SenseKey");
                await FindMerchantSiteAndUpdateParametersOrThrowAsync(request, merchant, order);

                await _dbContext.SaveChangesAsync();
            }

            var site = await _dbContext.Sites.SingleAsync(x => x.Id == order.SiteId);

            #region Storing IsGhostModeRequestFlag in Distributed Cache

            var isGhostModeFlagCacheKey_BySenseKey =
                CacheKeyFactory.CreateIsGhostModeRequestFlagKey_BySenseKey(order.SenseKey);
            await _distributedCache.SetStringAsync(isGhostModeFlagCacheKey_BySenseKey.Key, order.IsGhostMode.ToString(),
                isGhostModeFlagCacheKey_BySenseKey.CacheOptions);

            var isGhostModeFlagCacheKey_ByOrderId = CacheKeyFactory.CreateIsGhostModeRequestFlagKey_ByOrderId(order.Id);
            await _distributedCache.SetStringAsync(isGhostModeFlagCacheKey_ByOrderId.Key, order.IsGhostMode.ToString(),
                isGhostModeFlagCacheKey_ByOrderId.CacheOptions);

            #endregion


            if (!isMITRequest && // Consumer Origin is unknown for MIT orders as SenseKey is not available
                !merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled &&
                !skipOriginWhiteListCheck)
            {
                #region Checking Origin is in Site Whitelist

                try
                {
                    if (EnvironmentHelper.IsInSandbox)
                    {
                        await AddActivityAsync(SecurityActivities.WebsiteWhitelist_SkippedInSandbox, order,
                            data: order.Origin);
                    }
                    else
                    {
                        await _securityCheckService.EnsureCallMadeFromWhitelistedDomainAsync(order.Mid, order.Origin);
                        await AddActivityAsync(
                            SecurityActivities.WebsiteWhitelist_EligibilityCalledFromWhitelistedWebsite,
                            order,
                            data: order.Origin, meta: meta => meta
                                .SetValue("SiteUrl", order.Origin));
                    }
                }
                catch (EligibilityCalledFromWrongHostException ex)
                {
                    workspan.Log.Information("Eligibility called from not white-listed website: {Origin}",
                        order.Origin);
                    await AddActivityAsync(SecurityCriticalActivities.EligibilityCalledFromNotWhitelistedWebsite, order,
                        data: order.Origin, meta: meta => meta
                            .SetValue("SiteUrl", order.Origin));


                    await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligible);
                    return new EvaluationResult(SanitizeEvaluateResponse(new EvaluateResponse()));
                }

                #endregion
            }

            #region Request Limiting #1

            if (await ShouldSkipThisRequestDueToLimitingAsync(request, merchant, order))
            {
                //await SetOrderStateNotEligibleThresholdLimit(order);
                await _orderStateMachine.SetOfferStateToOnHoldOrCancelled(order);
                return new EvaluationResult(SanitizeEvaluateResponse(new EvaluateResponse()));
            }

            #endregion


            #region Cure Testing Support

#if ENABLE_CURE_TESTING

            bool inCureTestingMode = await IsInCureTestingMode(false, order, request);
            if (inCureTestingMode)
            {
                #region Commented

                // //Retaining payment instrument (Spreedly only) so it can be used in cures
                //
                // await _publisher.Publish<RetainPaymentInstrumentCommand>(new
                // {
                //     Mid = request.Mid,
                //     PaymentInstrumentID = order.PaymentInstrumentToken, //request.PaymentMethod?.CardNumber
                // });

                #endregion

                if (order.IsMIT() || order.IsInTerminalOrKioskMode())
                {
                    var merchantInfo = await GetMerchantInformationByOrderAsync(order);

                    workspan.Log.Information("Cure merchant hosted URL: {Url}",
                        UserChallengeBase.GenerateMerchantHostedCureUrl(order, merchantInfo.SiteId));
                    workspan.Log.Information("Cure self hosted URL: {Url}",
                        UserChallengeBase.GenerateSelfHostedCureUrl(order, merchantInfo.SiteId));
                }

                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.ConditionalConsumerInteraction);

                var testEvaluationResult = new EvaluationResult(SanitizeEvaluateResponse(eligibilityResponse));
                testEvaluationResult.EvaluateResponse.Status = nameof(EligibilityStatusCodes.CHALLENGE);

                try
                {
                    var merchantConfiguration =
                        await _merchantsService.GetMerchantSiteConfigurationAsync(order.Mid, order.SiteId!.Value);
                    order.LogoUrl = merchantConfiguration.Logo;
                    await _dbContext.SaveChangesAsync();
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e);
                }

                return testEvaluationResult;
            }

#endif

            #endregion


            //Evaluate immediately?
            bool evaluateRightNow = order.IsCIT ||
                                    asynchronousEvaluation != true ||
                                    (order.IsMIT() && merchant.MITImmediateRetryEnabled);

            // Ensures that if lock is acquired, it will be released 
            await using var orderLock = new OrderEvaluationDistributedLock();

            if (evaluateRightNow)
            {
                await EvaluateOrderAsync(orderLock, merchant, order, site, request, false, cancellationToken);
            }
            else
            {
                await _offSessionRetrySchedulerService.ScheduleOrderRetryAsync(orderLock, merchant, order);
            }
        }
        catch (NotEligibleException nee)
        {
            if (nee is NotEligibleCannotDetermineSiteIdException)
            {
                await _orderStateMachine.SetOfferStateToOnHoldOrCancelled(order);
            }
        }
        catch (Exception ex)
        {
            // We need to return correct response even if evaluation failed
            workspan.RecordFatalException(ex);
            await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: ex);
        }
        finally
        {
            // THIS CODE IS VERY IMPORTANT - WE NEED TO RETURN CORRECT RESPONSE TO MERCHANTS EVEN IF EVALUATION FAILED
            // THERE CAN BE A CASE, WHEN SOME EXCEPTION IS THROWN IN EVALUATION, BUT ORDER HAS BEEN ALREADY APPROVED
            if (asynchronousEvaluation == false)
            {
                // Synchronous transactions return result immediately to merchant
                var response = SanitizeEvaluateResponse(eligibilityResponse);

                if (request.IsMIT == true)
                {
                    if (order.IsApproved())
                    {
                        response.Status = nameof(EligibilityStatusCodes.APPROVED);
                    }
                    else if (order.CannotBeEligibleEver())
                    {
                        response.Status = nameof(EligibilityStatusCodes.DECLINED);
                    }
                    else
                    {
                        response.Status = nameof(EligibilityStatusCodes.SUBMITTED);
                    }
                }
                else //CIT
                {
                    if (order.IsApproved())
                    {
                        response.Status = nameof(EligibilityStatusCodes.APPROVED);
                    }
                    else if (order.IsConditionalConsumerInteraction())
                    {
                        response.Status = nameof(EligibilityStatusCodes.CHALLENGE);
                    }
                    else if (order.IsInCaptureRequiredState())
                    {
                        response.Status = nameof(EligibilityStatusCodes.CAPTUREREQUIRED);
                    }
                    else if (order.IsWaitingForApproval())
                    {
                        response.Status = nameof(EligibilityStatusCodes.WAITING_FOR_APPROVAL);
                    }
                    else
                    {
                        response.Status = nameof(EligibilityStatusCodes.DECLINED);
                    }
                }


                evaluationResult = new EvaluationResult(response);
            }
            else
            {
                evaluationResult = new EvaluationResult(SanitizeEvaluateResponse(eligibilityResponse));
            }


            evaluationResult.ResponseCode =
                order.GetLastTransactionResultOrDefault( /*false*/)?.NormalizedResponseCode;
            evaluationResult.ResponseMessage = GetFlexChargeReasonCodeForTransaction(evaluationResult.ResponseCode);
        }

        return evaluationResult;
    }

    public async Task<bool> CheckAndCorrectTransactionTypeAsync(EvaluateRequest request, Merchant merchant)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        //Capture Required Support (For now, we override TransactionType according to Merchant settings)

        if (merchant.CaptureRequired == true)
        {
            //Do not override TransactionType if it's already set 
            if (string.IsNullOrWhiteSpace(request.TransactionType))
            {
                request.TransactionType = nameof(TransactionType.authorization);
            }
        }
        else
        {
            if (request.TransactionType == nameof(TransactionType.authorization))
            {
                //Authorization is not allowed for this merchant
                await _activityService.CreateActivityAsync(EligibilityErrorActivities.CaptureIsNotEnabledForMerchant,
                    set: set => set
                        .TenantId(request.Mid)
                        .Meta(meta => meta
                            .SetValue("Amount", request.Transaction.Amount)
                            .SetValue("Currency", request.Transaction.Currency)));

                workspan.Log.Fatal("Authorization is not allowed for this merchant");

                return false;
            }

            request.TransactionType = nameof(TransactionType.purchase);
        }

        return true;
    }

    private async Task FindMerchantSiteAndUpdateParametersOrThrowAsync(EvaluateRequest request, Merchant merchant,
        Order order)
    {
        #region SiteId is not provided? -> Try to determine it

        if (request.SiteId == null)
        {
            #region MIT only - Try to find site by dynamic descriptor

            if (order.IsMIT())
            {
                if (request.SiteId == null && merchant.MITGetSiteByDynamicDescriptorEnabled)
                {
                    request.SiteId = await GetSiteIdByDynamicDescriptorAsync(request, order, false);
                }
            }

            #endregion

            #region Try to find site by URL

            if (request.SiteId == null)
            {
                // if (!string.IsNullOrWhiteSpace(request.SiteUrl))
                // {
                var merchantDomain = await GetMerchantDomainByUrlAsync(order.Mid, order, request.SiteUrl,
                    merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled);
                request.SiteId = merchantDomain?.SiteId;
                //}
            }

            #endregion

            // ATTENTION: Temporary solution - let CIT orders to get site by dynamic descriptor too
            if (order.IsCIT)
            {
                if (request.SiteId == null && merchant.MITGetSiteByDynamicDescriptorEnabled)
                {
                    request.SiteId = await GetSiteIdByDynamicDescriptorAsync(request, order, false);
                }
            }


            if (request.SiteId == null) // SiteId is still not provided?
            {
                await AddActivityAsync(EligibilityErrorActivities.CannotDetermineSiteId, order);
                throw new NotEligibleCannotDetermineSiteIdException();
            }
        }

        #endregion

        await SetOrderParametersFromSiteIdAsync(order, request.SiteId.Value);
    }

    public async Task EvaluateOrderAsync(OrderEvaluationDistributedLock orderLock, Merchant merchant, Order order,
        Site site,
        EvaluateRequest request, bool offSessionRetry,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", merchant.Mid)
            .Baggage("OrderId", order.Id);

        DataChangeFlags changedData;

        if (order.IsMIT() && order.EvaluationCount > 0)
        {
            // On retry of MIT order data is not changed or only payment method is changed
            // (payment method change will be determined in FingerprintPaymentInstrument method down the stream)
            changedData = DataChangeFlags.None;
        }
        else
        {
            // For CIT order or first evaluation of MIT order all data is new
            changedData = DataChangeFlags.All;
        }

        await EvaluateInternalAsync(orderLock, merchant, order, site, request, isReEvaluation: false,
            offSessionRetry: offSessionRetry, false,
            changedData: changedData,
            isRecursiveEvaluationCall: false,
            cancellationToken);
    }

    private async Task SetSiteIdOrderOriginAndMerchantDescriptorAsync(Order order,
        Merchant merchant,
        (Guid? SenseKey, IDictionary<string, string> LocationInformation, string BrowserInformation) sessionInformation)
    {
        if (!sessionInformation.LocationInformation.TryGetValue("origin", out var origin))
        {
            await AddActivityAsync(EligibilityErrorActivities.OriginIsMissingInLocationInformation, order,
                data: sessionInformation);
            throw new NotEligibleException();
        }

        await SetOfferParametersFromSiteUrlAsync(order, origin,
            merchant.UseDefaultSiteForUnknownMerchantUrlsEnabled);

        order.SetMeta("Location", JsonConvert.SerializeObject(sessionInformation.LocationInformation));
    }

    private async Task<Guid?> GetSiteIdByDynamicDescriptorAsync(EvaluateRequest request, Order order,
        bool throwOnNotFound = true)
    {
        if (string.IsNullOrWhiteSpace(request.Transaction.DynamicDescriptor))
        {
            if (throwOnNotFound)
            {
                await AddActivityAsync(EligibilityErrorActivities.DynamicDescriptorIsMissing, order);
                throw new NotEligibleException();
            }
            else return null;
        }

        var siteTag = await _dbContext.SiteTags.Where(x =>
            x.Tag.Trim().ToUpper() == request.Transaction.DynamicDescriptor.Trim().ToUpper() &&
            x.MerchantId == order.Mid
        ).FirstOrDefaultAsync();

        MerchantDomain merchantDomain = null;

        if (siteTag != null)
        {
            merchantDomain = await _dbContext.MerchantDomains.FirstOrDefaultAsync(x =>
                x.SiteId == siteTag.SiteId &&
                x.MerchantId == order.Mid);

            if (merchantDomain is not null)
                await AddActivityAsync(EligibilityActivities.MerchantDomainFoundByTag, order,
                    data: siteTag.Tag);
        }

        if (merchantDomain == null)
        {
            if (throwOnNotFound)
            {
                await AddActivityAsync(EligibilityErrorActivities.MerchantDomainNotFoundByDynamicDescriptor, order);
                throw new NotEligibleException();
            }
            else return null;
        }

        return merchantDomain.SiteId;
    }


    private async Task SetOrderParametersFromSiteIdAsync(Order order, Guid siteId)
    {
        var site = await _dbContext.Sites.SingleOrDefaultAsync(x => x.Id == siteId);
        if (site == null)
        {
            await AddActivityAsync(EligibilityErrorActivities.UnknownMerchantSiteUrl, order, data: order.Origin,
                meta: meta => meta.SetValue("SiteUrl", order.Origin));
            throw new NotEligibleException();
        }

        order.SiteId = siteId;

        // if (string.IsNullOrWhiteSpace(order.Origin))
        //     order.Origin = site.Domain;

        //if (string.IsNullOrWhiteSpace(order.MerchantDescriptor))
        order.MerchantDescriptor = site.Descriptor;
    }

    private async Task SetOfferParametersFromSiteUrlAsync(Order order, string siteUrl,
        bool useDefaultSiteForUnknownUrls)
    {
        var merchantDomain =
            await GetMerchantDomainByUrlAsync(order.Mid, order, siteUrl, useDefaultSiteForUnknownUrls);

        if (merchantDomain == null)
        {
            await AddActivityAsync(EligibilityErrorActivities.UnknownMerchantSiteUrl, order, data: siteUrl,
                meta: meta => meta.SetValue("SiteUrl", siteUrl));
            throw new NotEligibleException();
        }

        order.SiteId = merchantDomain.SiteId;
        order.Origin = siteUrl;
        order.MerchantDescriptor = merchantDomain.Descriptor;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="mid"></param>
    /// <param name="url">If null, default merchant domain will be returned, if any</param>
    /// <returns></returns>
    private async Task<MerchantDomain> GetMerchantDomainByUrlAsync(Guid mid, Order order, string url,
        bool returnDefaultDomainIfNotFound = true)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        //TODO: Cache results in distributed cache
        if (url != null)
        {
            var uri = UriHelper.CreateHttpsUri(url);
            var domain = uri.IsAbsoluteUri ? uri.Host.Trim().ToLowerInvariant() : uri.OriginalString;

            var merchantDomain = await _dbContext.MerchantDomains
                .Where(x => x.MerchantId == mid)
                .FirstOrDefaultAsync(x => x.Domain == domain);

            if (merchantDomain != null)
                return merchantDomain;
        }

        workspan.Log.Warning("Merchant domain not whitelisted: {Url}", url);

        if (returnDefaultDomainIfNotFound)
        {
            //Get first created domain - it's default one
            var merchantDomain = await _dbContext.MerchantDomains
                .Where(x => x.MerchantId == mid)
                .OrderBy(x => x.CreatedOn)
                .FirstOrDefaultAsync();

            if (merchantDomain != null)
            {
                workspan.Log.Warning("Using {Domain} merchant domain as fallback", merchantDomain.Domain);

                await AddActivityAsync(EligibilityActivities.MerchantDomainUsingDefaultFallback, order,
                    data: url,
                    meta: meta => meta
                        .SetValue("UsedUrl", merchantDomain.Domain)
                        .SetValue("MerchantUrl", url));
            }

            return merchantDomain;

            #region Commented

            // //TODO: This is done in memory because Npgsql doesn't support Linq to Entities DistinctBy
            // var merchantDomains = (await _dbContext
            //         .MerchantDomains.Where(x => x.MerchantId == mid).ToListAsync())
            //     .DistinctBy(x => x.SiteId).Take(2).ToList();
            // if (merchantDomains.Count == 1)
            // {
            //     //Only one domain is configured for the merchant -> use it as default
            //     return merchantDomains[0];
            // }

            #endregion
        }

        return null;
    }

    #endregion

    #region Sanitizing response (removing possible sensitive error data)

    private static EvaluateResponse SanitizeEvaluateResponse(EvaluateResponse internalResponse)
    {
        // if (!internalResponse.Success || internalResponse.Result != OrderState.ELIGIBILITY_STAGE1_PASSED.ToString())
        // {
        //     workspan.LogEligibility.Information($"NOT_ELIGIBLE");
        //
        //     return new EvaluateResponse()
        //     {
        //         OrderSessionKey = null,
        //         //OrderSessionKey = internalResponse.OrderSessionKey, //no internal OrderSessionKey returned for not eligible requests
        //         SenseKey = request.SenseKey,
        //         Result = "NOT_ELIGIBLE"
        //     };
        // }
        // else
        // {
        return new EvaluateResponse()
        {
            OrderSessionKey = internalResponse.OrderSessionKey,
            //SenseKey = internalResponse.SenseKey
        };
        //}
    }

    #endregion

    #region Evaluate

    private async Task EvaluateInternalAsync(
        OrderEvaluationDistributedLock orderLock,
        Merchant merchant,
        Order order, Site site,
        EvaluateRequest evaluateRequest,
        bool isReEvaluation, bool offSessionRetry,
        bool reEvaluationAfterUserChallenge,
        DataChangeFlags changedData,
        bool isRecursiveEvaluationCall,
        CancellationToken cancellationToken,
        EligibilityCheckContext previousContextInThisEvaluationSession = null)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        using var localCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        #region Ensuring that order can be evaluated

        if (!isRecursiveEvaluationCall)
        {
            await orderLock.AcquireOrderLock(order.Id, merchant.Mid, _distributedLockService, _activityService);

            // Check if order is already in processing on another machine
            if (order.SenseKey != null && evaluateRequest.SenseKey != order.SenseKey)
            {
                await AddActivityAsync(EligibilityActivities.Evaluation_OfferIsAlreadyInProcessing, order,
                    data: "SenseKey mismatch");
                throw new ConcurrentEvaluationSenseKeyMismatchException();
            }

            if (order.IsApproved())
            {
                await AddActivityAsync(EligibilityActivities.Evaluation_OfferIsAlreadyInProcessing, order,
                    data: "Order is already approved");

                throw new ConcurrentEvaluationOrderAlreadyApprovedException();
            }
        }

        #endregion

        var mid = merchant.Mid;
        Stopwatch reEvaluationProcessingTimeStopwatch = null;

        EligibilityCheckContext eligibilityContext = null;
        ValueStorage valueStorage = new ValueStorage();
        try
        {
            #region Check if order expired

            if (order.IsExpired(merchant))
            {
                workspan.Log.Error("Cannot evaluate expired order");
                await AddActivityAsync(EligibilityErrorActivities.Evaluate_CannotEvaluateExpiredOrder, order);
                throw new NotEligibleExpiredException();
            }

            #endregion

            #region Check if order cannot be eligible

            if (order.CannotBeEligibleEver() || order.IsOnHold())
            {
                workspan.Log.Error("Cannot evaluate order that cannot be eligible");
                await AddActivityAsync(EligibilityErrorActivities.Evaluate_CannotEvaluateOrderThatCannotBeEligible,
                    order);

                throw new NotEligibleAlreadyCancelledOrOnHoldException();
            }

            #endregion

            #region Normalizing request

            // Additional normalization. In case we've added some new normalization after order was created in our system
            _normalizeDataService.Normalize(evaluateRequest);

            #endregion

            // site can be missing in merchant properties on first evaluation
            // and be set by administrators later, so retries will work            
            if (order.IsMIT() && order.SiteId == null)
            {
                await FindMerchantSiteAndUpdateParametersOrThrowAsync(evaluateRequest, merchant, order);
                site = await _dbContext.Sites.SingleAsync(x => x.Id == order.SiteId);
                await _dbContext.SaveChangesAsync();
            }

            var merchantConfiguration =
                await _merchantsService.GetMerchantSiteConfigurationAsync(mid, order.SiteId!.Value);
            UserExperienceFlowType userExperienceFlowType =
                (UserExperienceFlowType) merchantConfiguration.ConsentFlowConfiguration;
            order.LogoUrl = merchantConfiguration.Logo;


            if (!isReEvaluation)
            {
                order.EvaluationCount++;
                await _dbContext.SaveChangesAsync();

                #region Adding evaluation timeout (from options) to cancellation token

                // We only set SLA timeout for CIT orders
                if (order.IsCIT)
                {
                    localCancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(
                        _eligibilityOptions.Value.CITEvaluationTimeout));
                }

                cancellationToken = localCancellationTokenSource.Token;

                #endregion

                await AddActivityAsync(EligibilityActivities.Evaluation_EvaluationStarted, order,
                    meta: meta =>
                    {
                        meta
                            .SetValue("SiteId", order.SiteId)
                            .SetValue("MIT", order.IsMIT());

                        if (order.IsCIT)
                        {
                            // For CIT orders audit should be started after expiration (if no activity triggers it before)
                            // otherwise not all activities can be ready for audit (for example, when a user challenge requested)
                            meta
                                .AuditDelay(
                                    (order.CreatedOn + merchant.GetCITOrderExpirationInterval() - DateTime.UtcNow) +
                                    TimeSpan.FromMinutes(
                                        2)); // to ensure all activities arrived to database and expired order is already processed by an expiration mechanism
                        }
                    }
                );
            }
            else
            {
                reEvaluationProcessingTimeStopwatch = Stopwatch.StartNew();

                ThrowIfOrderCannotBeEligible(order);
                await ResetNonActualCheckResults(changedData, order, evaluateRequest);

                await AddActivityAsync(EligibilityActivities.Evaluation_ReEvaluationStarted, order);

                // Ensure that token is the same as token in CardNumber of Evaluate request
                // it can be changed in cures
                order.PaymentInstrumentToken = evaluateRequest.PaymentMethod?.CardNumber;
            }

            if (reEvaluationAfterUserChallenge)
            {
                bool onCheckoutPage = order.IsMIT() || order.IsInTerminalOrKioskMode();
                if (onCheckoutPage && string.IsNullOrEmpty(order.SenseKey))
                {
                    //SenseKey should be available for MIT and Terminal order after user challenge
                    var sessionMatchResult =
                        await TryToMatchOrderToSenseKeyWithRetriesAsync(evaluateRequest, mid, order, merchant);
                    if (sessionMatchResult.Matched)
                    {
                        order.SenseKey = sessionMatchResult.SenseKey.Value.ToString();
                        order.SetBrowserInformation(sessionMatchResult.BrowserInformation);

                        await _publisher.Publish(new OrderSessionMatchedEvent()
                        {
                            OrderId = order.Id,
                            Mid = order.Mid,
                            BrowserInformation = sessionMatchResult.BrowserInformation
                        });
                    }

                    //WHAT IF NO MATCH FOR TERMINAL ORDER???
                }
            }

            #region [Commented]Checking for parallel offer

            // if (!isReEvaluation)
            // {
            //     await EnsureNoAnotherActiveOrderExists(order, evaluateRequest);
            // }

            #endregion

            #region [Commented] Ensure Not Prepaid Card Type Specified In Initial Request

            // #region EnsureNotPrepaidCardTypeSpecifiedInInitialRequest
            //
            // await EnsureNotPrepaidCardTypeSpecifiedInInitialRequest(evaluateRequest, order);
            //
            // #endregion

            #endregion

            #region Commented

            //bool isAutoConsentUIFlow; // We don't need user consent to process offer?

            #endregion


            eligibilityContext = new EligibilityCheckContext(merchant, site, order,
                evaluateRequest, changedData, isReEvaluation, offSessionRetry,
                _fingerprintService, userExperienceFlowType, null,
                previousContextInThisEvaluationSession);

// #if DEBUG
//             // TOOL: REMOVE TEST CODE
//             eligibilityContext.ExternallyControlledParameters.SetParameterValue(
//                 new Guid("bf41774a-bfbc-470f-bd0d-889c54fab3c3"), "BlockByBinNumber", "true");
// #endif

            #region Building and Running Eligibility Workflow

            var eligibilityWorkflow =
                await _eligibilityWorkflowFactory.Create_EvaluateEligibility_WorkflowAsync(merchant, order,
                    evaluateRequest, offSessionRetry);

            await WorkflowFactory.BuildWorkflowAsync(_serviceScopeFactory,
                eligibilityWorkflow, eligibilityContext, valueStorage,
                merchant.WorkflowsExternalParameters, serializeWorkflow: true);

            await _dbContext.SaveChangesAsync(); //Saving changes as Order can be updated in pre-executions steps

            try
            {
                await eligibilityWorkflow.Workflow.Run(eligibilityWorkflow.Description, _serviceScopeFactory);
            }
            finally
            {
                eligibilityContext.ProcessExecutionResults();
                await _dbContext.SaveChangesAsync();
            }


            changedData =
                DataChangeFlags.None; // Starting tracking for data changes in subsequent user challenge cures

            // Ensuring that offer state is send as event and written to Redis cache for GetOrderSession:
            await _orderStateMachine.SetOrderStateAsync(order, order.State.ToOrderState(), saveChangesToDatabase: true,
                forceUpdateEvent: false);

            #endregion

            await ThrowNotEligibleIfCancelledAsync(order, cancellationToken);

            if (eligibilityContext.CureExecutionResult == null) // no cures requested
            {
                #region Ensure offer state is ELIGIBILITY_STAGE3_PASSED

                if (!(order.State.ToOrderState() is OrderState.ELIGIBILITY_STAGE3_PASSED))
                {
                    await AddActivityAsync(EligibilityErrorActivities.IncorrectOfferState, order);

                    throw new Exception(
                        $"Incorrect offer state: {order.State}. Must be ELIGIBILITY_STAGE3_PASSED.");
                }

                #endregion
            }
            else if (eligibilityContext.CureExecutionResult == CureExecutionResult.Stop)
            {
                throw new NotEligibleException();
            }
            else if (eligibilityContext.CureExecutionResult == CureExecutionResult.Cured)
            {
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.EligibilityStage3Passed);
            }
            else // a cure has been executed
            {
                switch (eligibilityContext.CureExecutionResult)
                {
                    case CureExecutionResult.ChallengeCustomer:
                        await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.ConditionalConsumerInteraction);
                        break;
                    case CureExecutionResult.ReEvaluate:
                        if (reEvaluationProcessingTimeStopwatch != null)
                        {
                            // we do not want to count internal re-evaluation time
                            reEvaluationProcessingTimeStopwatch.Stop();
                        }


                        try
                        {
                            await EvaluateInternalAsync(orderLock,
                                merchant, order, site, evaluateRequest,
                                isReEvaluation: true, offSessionRetry: offSessionRetry, false,
                                changedData: changedData,
                                isRecursiveEvaluationCall: true,
                                cancellationToken,
                                previousContextInThisEvaluationSession: eligibilityContext);
                        }
                        finally
                        {
                            if (reEvaluationProcessingTimeStopwatch != null)
                            {
                                // resume stopwatch after recursive re-evaluation completed
                                reEvaluationProcessingTimeStopwatch.Start();
                            }
                        }

                        await ThrowNotEligibleIfCancelledAsync(order, cancellationToken);

                        return;
                    default:
                        throw new Exception(
                            $"Wrong cure execution result: {eligibilityContext.CureExecutionResult}");
                }
            }

            if (order.State.ToOrderState() == OrderState.ELIGIBILITY_STAGE3_PASSED)
            {
                if (order.IsExpired(merchant)) throw new NotEligibleExpiredException();

                bool autoApproveOrder;
                if (order.IsInRequireCaptureMode(evaluateRequest, merchant))
                {
                    autoApproveOrder = false;

                    #region Commented

                    // if (order.IsInTerminalMode(evaluateRequest))
                    // {
                    //     if (order.IsAnyUserChallengeExecuted() && order.IsInRequireCaptureMode(evaluateRequest))
                    //     {
                    //         // We need merchant-requested consent in case any user challenge has been shown
                    //         // as challenges can be completed on user's device and both parties should be aware of it
                    //         autoApproveOrder = false;
                    //     }
                    //     else
                    //     {
                    //         // No challenges were shown to the user, so all processing is done on merchant side
                    //         // so we can auto-approve the order
                    //         autoApproveOrder = true;
                    //     }
                    // }

                    #endregion
                }
                else if (evaluateRequest.IsMIT == true) // MIT orders are auto-approved
                {
                    autoApproveOrder = true;
                }
                else
                {
                    //Only CIT orders can get here
                    autoApproveOrder =
                        // synchronous evaluation -> auto-approve
                        merchant.CITEvaluateAsync == false ||
                        // no UI widget -> no consumer-initiated consent possible
                        merchant.UIWidgetOptional == true ||
                        // no SenseJS -> no consumer-initiated consent required
                        merchant.SenseJSOptional == true;
                }


                if (autoApproveOrder)
                {
                    await ConsentApprove<ConsentApproveRequest, ConsentApproveResponse>(
                        orderLock,
                        new ConsentApproveRequest()
                        {
                            Mid = merchant.Mid,
                            OrderSessionKey = order.Id,
                            SenseKey = order.SenseKey,
                            IsExplicit = false
                        }, merchant.Mid, false, false, cancellationToken);
                }
                else
                {
                    if (order.IsInRequireCaptureMode(evaluateRequest, merchant))
                    {
                        await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.CaptureRequired);
                    }
                    else
                    {
                        await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.WaitingForApproval);
                    }
                }
            }

            await ThrowNotEligibleIfCancelledAsync(order, cancellationToken);
        }
        catch (NotEligibleException nee)
        {
            if (nee is NotEligibleAlreadyCancelledOrOnHoldException)
            {
                // Nothing to do here
                workspan.Log.Information("Order is already cancelled or on hold");
            }
            else if (nee is NotEligibleCannotDetermineSiteIdException)
            {
                await _orderStateMachine.SetOfferStateToOnHoldOrCancelled(order);
            }
            else if (nee is NotEligibleThresholdLimitException te)
            {
                if (te.CancelOrder)
                {
                    await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleThresholdLimit);
                }
                else await _orderStateMachine.SetOfferStateToOnHoldOrCancelled(order);
            }
            else if (nee is NotEligibleDuplicateOrderException)
            {
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleDuplicateOrder);
            }
            else if (nee is NotEligibleExpiredException)
            {
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleExpired);
            }
            else if (order.IsMIT() && order.ExpiryDate == null)
            {
                // Single-try MIT order without expiry date
                workspan.Log.Information("Cancelling single-try MIT order without expiry date");
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleCancelled);
            }
            else if (nee is NotEligibleCancelledException)
            {
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleCancelled);
            }
            else
            {
                await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligible);
            }
        }
        catch (ConcurrentEvaluationException cex)
        {
            workspan.Log.Warning(cex, "Concurrent evaluation exception");
            await AddActivityAsync(EligibilityErrorActivities.Evaluate_ConcurrentEvaluationDetected, order, data: cex);

            throw;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e,
                $"Failed evaluate request {JsonConvert.SerializeObject(evaluateRequest)}");

            await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: e);

            //return await NotEligible(eligibilityResponse, order, Consts.OrderNotEligible);

            await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligible);
        }
        finally
        {
            //DO NOT REMOVE GLOBAL TRY-CATCH!!! It's required to ensure than some not critical exceptions are not thrown to the caller 
            try
            {
                try
                {
                    #region Voiding authorization / Cancel ACH debit for fully authorized, but not eligible offer

                    if (order != null && order.CannotBeEligibleNow())
                    {
                        await _paymentsService.VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(order);
                        await _dbContext.SaveChangesAsync();
                    }

                    #endregion
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e);
                    await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: e);
                }

                try
                {
                    if (eligibilityContext != null && order.CannotBeEligibleNow() &&
                        !isRecursiveEvaluationCall) // to avoid processing not eligible order twice or more (on recursive calls to EvaluateInternalAsync)
                    {
                        await ProcessNotEligibleOrderAsync(eligibilityContext, valueStorage, order, merchant);
                    }
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e);
                    await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: e);
                }


                if (eligibilityContext != null && order.IsConditionalConsumerInteraction())
                {
                    if (order.IsMIT() == true || order.IsInTerminalOrKioskMode())
                    {
                        var merchantInfo = await GetMerchantInformationByOrderAsync(order);

                        workspan.Log.Information("Cure merchant hosted URL: {Url}",
                            UserChallengeBase.GenerateMerchantHostedCureUrl(order, merchantInfo.SiteId));
                        workspan.Log.Information("Cure self hosted URL: {Url}",
                            UserChallengeBase.GenerateSelfHostedCureUrl(order, merchantInfo.SiteId));
                    }
                }

                try
                {
                    // Firing last order status to ensure it's synchronized in other microservices
                    await _orderStateMachine.FireEligibilityOfferStatusUpdatedEventsAsync(order, null, true);
                }
                catch (Exception e)
                {
                    workspan.RecordException(e);
                }


                if (isReEvaluation)
                {
                    double reEvaluationProcessingTime = 0;
                    if (reEvaluationProcessingTimeStopwatch != null)
                    {
                        reEvaluationProcessingTimeStopwatch.Stop();

                        reEvaluationProcessingTime = reEvaluationProcessingTimeStopwatch.ElapsedMilliseconds;
                    }


                    await _activityService.CreateActivityAsync(
                        EligibilityActivities.Evaluation_ReEvaluationEnded, order,
                        set => set
                            .CorrelationId(order.Id)
                            .TenantId(order.Mid)
                            .Meta(meta => meta
                                .ProcessingTime(reEvaluationProcessingTime)
                                .SetValue("MIT", order.IsMIT())
                            ));
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: e);
            }
        }
    }

    private async Task ThrowNotEligibleIfCancelledAsync(Order order, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            Workspan.Current?.Log.Warning("Evaluation timeout encountered. MIT: {Mit}", order?.IsMIT());
            await AddActivityAsync(EligibilityErrorActivities.Evaluate_Timeout_Error, order);

#if DEBUG
            Workspan.Current?.Log.Warning("Skipping evaluation timeout in DEBUG mode");
#else
            bool canProceedWithTimeoutException = true;

            if (order != null)
            {
                // If order is approved , we cannot throw NotEligibleEvaluationTimeoutException
                if (order.State.ToOrderState() == OrderState.ORDER_APPROVED) 
                    canProceedWithTimeoutException = false;
                
                // If order payment is captured, we cannot throw NotEligibleEvaluationTimeoutException
                if (order.IsPaymentCaptured == true) 
                    canProceedWithTimeoutException = false;
            }

            if (canProceedWithTimeoutException)
            {
                throw new NotEligibleEvaluationTimeoutException();
            }
            else
            {
                // It's too late to make order not eligible!!!
                Workspan.Current?.Log.Error("Skipping evaluation timeout for APPROVED or CAPTURED order");
            }
#endif
        }
    }

    private async Task ProcessNotEligibleOrderAsync(EligibilityCheckContext eligibilityContext,
        ValueStorage valueStorage, Order order, Merchant merchant)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var notEligibleOrderProcessingWorkflow = await _eligibilityWorkflowFactory
            .Create_NotEligible_OrderProcessing_WorkflowAsync(order, merchant);

        var workflow = await notEligibleOrderProcessingWorkflow.Workflow.BuildAsync(
            notEligibleOrderProcessingWorkflow.Description,
            eligibilityContext, valueStorage,
            _serviceScopeFactory, merchant.WorkflowsExternalParameters);

        #region Workflow Serialization

        try
        {
            using var serviceScope = _serviceScopeFactory.CreateScope();

            // Important!!! Use scoped workflowDbContext for SerializeWorkflow() call to avoid problems
            var workflowDbContext = serviceScope.ServiceProvider.GetRequiredService<WorkflowPostgreSQLDbContext>();
            var workflowService = serviceScope.ServiceProvider.GetRequiredService<IWorkflowService>();

            await workflowService.SerializeWorkflow(workflow, notEligibleOrderProcessingWorkflow.Description,
                workflowDbContext,
                workflowDbContext.Workflows, true);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Cannot serialize workflow");
        }

        #endregion

        try
        {
            await notEligibleOrderProcessingWorkflow.Workflow.Run(notEligibleOrderProcessingWorkflow.Description,
                _serviceScopeFactory);
        }
        finally
        {
            eligibilityContext.ProcessExecutionResults();
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task ProcessNotEligibleEverOrderAsync(Order order)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var merchant = await _dbContext.Merchants.SingleAsync(m => m.Mid == order.Mid);

        var notEligibleEverOrderProcessingWorkflow = await _eligibilityWorkflowFactory
            .Create_NotEligibleEver_OrderProcessing_WorkflowAsync(order, merchant);

        if (notEligibleEverOrderProcessingWorkflow.Workflow == null)
        {
            workspan.Log.Information("Not eligible ever order processing workflow is not defined");
            return; //!!!
        }

        var workflowContext = new NotEligiblieEverOrderProcessingContext(merchant, order);
        var valueStorage = new ValueStorage();

        await WorkflowFactory.BuildWorkflowAsync(_serviceScopeFactory,
            notEligibleEverOrderProcessingWorkflow, workflowContext, valueStorage,
            merchant.WorkflowsExternalParameters, serializeWorkflow: true);

        #endregion

        try
        {
            await notEligibleEverOrderProcessingWorkflow.Workflow.Run(
                notEligibleEverOrderProcessingWorkflow.Description,
                _serviceScopeFactory);
        }
        finally
        {
            await _dbContext.SaveChangesAsync();
        }
    }


    private async Task PublishNetworkTokenRequestingEvent(EvaluateRequest evaluateRequest)
    {
        var billingInformation = evaluateRequest.BillingInformation;

        if (billingInformation is null)
            return; //!!!

        var billingAddress =
            new Address()
            {
                Address1 = billingInformation.AddressLine1,
                Address2 = billingInformation.AddressLine2,
                City = billingInformation.City,
                Phone = billingInformation.Phone,
                Zip = billingInformation.Zipcode,
                CountryCode = billingInformation.CountryCode,
                StateCode = billingInformation.State,
            };

        var networkTokenRequestedEvent = new NetworkTokenRequestedEvent()
        {
            SenseKey = evaluateRequest.SenseKey,
            BillingAddress = billingAddress,
            PaymentInstrumentToken = Guid.Parse(evaluateRequest.PaymentMethod.CardNumber),
        };

        await _publisher.Publish(networkTokenRequestedEvent);
    }


    #region Internal Evaluate Support Code

// private bool ShouldFireInternalEvaluateCompletedEvent(Order order)
// {
//     return order.CannotBeEligible()
// }
//
// async Task FireInternalEvaluateCompletedEventAsync(Order order)
// {
//     using var workspan = Workspan.Start<ProcessEvaluateRequestCommand>();
//
//     try
//     {
//         var status = order.IsApproved()
//             ? nameof(EligibilityStatusCodes.ELIGIBLE)
//             : nameof(EligibilityStatusCodes.NOT_ELIGIBLE);
//
//         
//         await _publisher.Publish(new InternalEvaluateCompletedEvent
//         {
//             Result = JsonConvert.SerializeObject(new EvaluateResponse()
//             {
//                 OrderSessionKey = order.Id,
//                 Status = status
//             }),
//         });
//     }
//     catch (Exception e)
//     {
//         workspan.RecordException(e);
//     }
// }

    #endregion
}