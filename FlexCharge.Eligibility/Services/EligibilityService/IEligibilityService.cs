using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.EligibilityService;

public interface IEligibilityService
{
    #region TransmitAPI

    /// <summary>
    /// Runs request using MassTransit command for load balancing 
    /// </summary>
    /// <param name="request"></param>
    /// <param name="partnerID"></param>
    /// <param name="transmitActivityToCreateOrUpdateId"></param>
    /// <param name="processingType"></param>
    /// <param name="requestType"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="isInternalEvaluation"></param>
    /// <param name="meta"></param>
    /// <returns></returns>
    Task<EvaluationResult> RunEvaluateRequestAsync(EvaluateRequest request,
        Guid? partnerID,
        Guid transmitActivityToCreateOrUpdateId, EvaluateProcessingType processingType,
        EvaluateRequestType requestType,
        CancellationToken cancellationToken,
        bool isInternalEvaluation = false,
        Dictionary<string, string> meta = null);

    Task ProcessTransmittedOrder(TransmitRequest payload, Guid transmitCorrelationId);

    #endregion

    #region TransactionAPI

    Task<PollResponse> GetOrderSessionAsync(PollRequest request, CancellationToken cancellationToken);

    Task<ConsentResponse> ConsentReject(ConsentRequest request, Guid mid);

    Task<TResponse> ConsentApprove<TRequest, TResponse>(OrderEvaluationDistributedLock orderLock, TRequest request,
        Guid mid, bool isExplicitConsent, bool isCalledFromClient, CancellationToken cancellationToken)
        where TRequest : IOrderSessionKey, new()
        where TResponse : BaseResponse, IOptionalEligibilitySessionIdPair, IOptionalExternalOrderReference, new();

    #endregion

    #region OutcomeAPI

    Task<OutcomeResponse> Outcome(OutcomeRequest payload, Guid mid);

    #endregion


    Task<ChallengeReEvaluateResponse> ReEvaluateWithUserChallengeResults(ChallengeReEvaluateRequest payload, Guid mid,
        CancellationToken cancellationToken);

    Task<ChallengeResponse> Challenge(ChallengeRequest payload, Guid mid);

    Task<(string PaymentDescriptor, string MerchantPublicName, Guid SiteId)>
        GetMerchantInformationByOrderAsync(Order order);

    Task EvaluateOrderAsync(OrderEvaluationDistributedLock orderLock, Merchant merchant, Order order, Site site,
        EvaluateRequest request, bool offSessionRetry, CancellationToken cancellationToken);

    Task<Merchant> GetMerchantOrDefaultAsync(Guid mid);
    Task<(Merchant Merchant, Site Site)> GetMerchantAndSiteOrDefaultAsync(Guid requestMid, Guid requestSiteId);

    Task<EvaluationResult> ExecuteRequestAsync(bool asynchronousEvaluation,
        EvaluateRequest evaluateRequest, Guid? partnerId, Guid offerId,
        bool internalEvaluation, EvaluateRequestType requestType,
        Dictionary<string, string>? orderMeta,
        CancellationToken cancellationToken);

    Task TokenizePaymentInstrumentAsync(EvaluateRequest evaluateRequest, Guid orderId, Guid mid,
        CancellationToken cancellationToken = default);

    Task<bool> CheckAndCorrectTransactionTypeAsync(EvaluateRequest request, Merchant merchant);

    Task ApproveOrderAsync(Order order, Merchant merchant, bool paymentCaptured, bool paidOutOfBand);

    Task ProcessNotEligibleEverOrderAsync(Order order);
}