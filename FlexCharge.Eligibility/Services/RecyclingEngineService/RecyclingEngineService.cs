using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Logging;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.OrdersRecyclingEngine;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.WorkflowEngine;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.RecyclingEngineService;

public class RecyclingEngineService : IRecyclingEngineService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public RecyclingEngineService(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task<(WorkflowDescription WorkflowDescription, RecyclingWorkflowContext WorkflowContext)>
        CreateAndRunRecyclingWorkflowAsync(Merchant merchant, Order order)
    {
        using var workspan = Workspan.Start<RecyclingEngineService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        try
        {
            using var serviceScope = _serviceScopeFactory.CreateScope();
            var activityService = serviceScope.ServiceProvider.GetRequiredService<IActivityService>();

            var workflowContext = new RecyclingWorkflowContext(merchant, order, DateTime.UtcNow);
            var valueStorage = new ValueStorage();

            #region Building and Running Workflow

            var recyclingWorkflow =
                await RecyclingWorkflowFactory.CreateWorkflowAsync(activityService, merchant, order);

            await WorkflowFactory.BuildWorkflowAsync(_serviceScopeFactory,
                recyclingWorkflow, workflowContext, valueStorage,
                merchant.WorkflowsExternalParameters, serializeWorkflow: true);


            try
            {
                await recyclingWorkflow.Workflow.Run(recyclingWorkflow.Description, _serviceScopeFactory);

                var trace = ((ITraceable) workflowContext).Tracer.GetTraceAsString();
                workspan
                    .Log
                    .Enrich("Trace", trace)
                    .Information("Recycling workflow execution trace");

                return (recyclingWorkflow.Description, workflowContext);
            }
            finally
            {
                //eligibilityContext.ProcessExecutionResults();
                //await _dbContext.SaveChangesAsync();
            }

            #endregion
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex);
            throw;
        }
    }
}