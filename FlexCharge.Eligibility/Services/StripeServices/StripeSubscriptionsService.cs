using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using Stripe;
using Stripe.TestHelpers;

namespace FlexCharge.Eligibility.Services.StripeServices;

public interface IStripeSubscriptionsService
{
    IAsyncEnumerable<InvoiceDescription> GetFailedInvoicesForActiveSubscriptionsAsync(Guid mid, string accountId);
}

public class StripeSubscriptionsService : IStripeSubscriptionsService
{
    private readonly ISecretsManager _secretsManager;

    public StripeSubscriptionsService(ISecretsManager secretsManager)
    {
        _secretsManager = secretsManager;
    }

    public async IAsyncEnumerable<InvoiceDescription> GetFailedInvoicesForActiveSubscriptionsAsync(Guid mid,
        string accountId)
    {
        using var workspan = Workspan.Start<Services.StripeServices.StripeMerchantInformationService>()
            .Baggage("Mid", mid)
            .Baggage("AccountId", accountId)
            .LogEnterAndExit();

        var stripeRequestOptions = new RequestOptions()
        {
            StripeAccount = accountId,
            ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
        };


        var subscriptionService = new SubscriptionService();
        var invoiceService = new InvoiceService();

        // In Stripe subscriptions that belong to a test clock can be retrieved only by passing the test clock id
        List<string> testClockIdsToGetSubscriptionsFor = new List<string>();
        testClockIdsToGetSubscriptionsFor.Add(null); // to include invoices without a test clock

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            var testClockService = new TestClockService();
            var testClockListOptions = new TestClockListOptions
            {
                Limit = 100
            };

            await foreach (var testClock in testClockService
                               .ListAutoPagingAsync(testClockListOptions, stripeRequestOptions))
            {
                testClockIdsToGetSubscriptionsFor.Add(testClock.Id);
            }
        }

        List<InvoiceDescription> failedInvoicesForActiveSubscriptions = new();

        foreach (var testClockName in testClockIdsToGetSubscriptionsFor)
        {
            await foreach (var failedInvoice in
                           GetFailedInvoicesForActiveSubscriptionsAsync(
                               testClockName, subscriptionService,
                               stripeRequestOptions, invoiceService))
            {
                yield return new InvoiceDescription(failedInvoice.Id, failedInvoice.SubscriptionId, accountId);
            }
        }
    }

    public static async IAsyncEnumerable<Invoice> GetFailedInvoicesForActiveSubscriptionsAsync(
        string testClockName, SubscriptionService subscriptionService,
        RequestOptions stripeRequestOptions, InvoiceService invoiceService)
    {
        using var workspan = Workspan.Start<Services.StripeServices.StripeMerchantInformationService>()
            .Baggage("TestClockName", testClockName)
            .LogEnterAndExit();

        //see: https://docs.stripe.com/api/subscriptions/list?shell=true&api=true
        var subscriptionOptions = new SubscriptionListOptions
        {
            Limit = 100,
            //By default, returns a list of subscriptions that have not been canceled
            Status = null, // all or filter to "active" and "past_due",
            TestClock = testClockName
        };

        //see: https://docs.stripe.com/api/pagination/auto
        await foreach (var notCancelledSubscription in subscriptionService
                           .ListAutoPagingAsync(subscriptionOptions, stripeRequestOptions))
        {
            // Do something with customer

            if (notCancelledSubscription.LatestInvoiceId == null)
                continue;

            var invoice = await invoiceService.GetAsync(notCancelledSubscription.LatestInvoiceId,
                requestOptions: stripeRequestOptions);

            if (IsInvoiceRetryable(invoice))
            {
                workspan
                    .Baggage("InvoiceId", invoice.Id)
                    .Baggage("SubscriptionId", notCancelledSubscription.Id)
                    .Baggage("TestClockName", testClockName)
                    .Log.Information("Found retryable invoice");

                if (!IsAlreadyInProcessingByFlex(invoice))
                {
                    workspan
                        .Log.Information("Invoice is not processed by Flex");

                    yield return invoice;
                }
                else
                {
                    workspan
                        .Log.Information("Invoice is already processed by Flex");
                }
            }

            #region Local functions

            bool IsInvoiceRetryable(Invoice invoice)
            {
                return (invoice.Status == "open" || invoice.Status == "uncollectible") &&
                       invoice.Paid == false;
            }

            bool IsAlreadyInProcessingByFlex(Invoice invoice)
            {
                return invoice.Metadata.ContainsKey(FlexFactorMetadata.FlexFactorOrderId);
            }

            #endregion
        }
    }
}