using System;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Telemetry;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using Stripe;

namespace FlexCharge.Eligibility.Services.StripeServices;

public interface IStripeMerchantInformationService
{
    Task<MerchantAccountInformation> GetMerchantAccountInformationAsync(Guid mid, string accountId);
}

public class StripeMerchantInformationService : IStripeMerchantInformationService
{
    private readonly ISecretsManager _secretsManager;

    public StripeMerchantInformationService(
        ISecretsManager secretsManager)
    {
        _secretsManager = secretsManager;
    }

    public async Task<MerchantAccountInformation> GetMerchantAccountInformationAsync(Guid mid, string accountId)
    {
        using var workspan = Workspan.Start<StripeMerchantInformationService>()
            .Baggage("Mid", mid)
            .Baggage("AccountId", accountId);

        try
        {
            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = accountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };


            var service = new AccountService();
            var account = service.Get(accountId, requestOptions: stripeRequestOptions);

            workspan
                .Tag("AccountInformation", account)
                .Log.Information("Successfully retrieved merchant information for Stripe account");


            // company / government_entity (US only) / individual / non_profit
            var businessType = account.BusinessType;

            var companyStructure = account.Company?.Structure;

            var mcc = account.BusinessProfile?.Mcc;
            var businessName = account.BusinessProfile?.Name;
            var businessUrl = account.BusinessProfile?.Url;

            var accountCountry = account.Country;

            //Three-letter ISO currency code representing the default currency for the account.
            //This must be a currency that Stripe supports in the account’s country.
            var accountDefaultCurrency = account.DefaultCurrency;

            //Can be standard, express, custom, or none.
            var accountType = account.Type;

            //An email address associated with the account. It’s not used for authentication and
            //Stripe doesn't market to this field without explicit approval from the platform.
            var accountEmail = account.Email;

            var supportEmail = account.BusinessProfile?.SupportEmail;
            var supportPhone = account.BusinessProfile?.SupportPhone;
            var supportUrl = account.BusinessProfile?.SupportUrl;
            var supportAddress = account.BusinessProfile?.SupportAddress;

            // Note: there are separate descriptor and prefix for Kana and Kanji characters 
            var paymentsDescriptor = account.Settings?.Payments?.StatementDescriptor;
            var cardPaymentsDescriptorPrefix = account.Settings?.CardPayments.StatementDescriptorPrefix;

            return new MerchantAccountInformation
            {
                BusinessType = businessType,
                CompanyStructure = companyStructure,
                Mcc = mcc,
                BusinessName = businessName,
                BusinessUrl = businessUrl,
                AccountCountry = accountCountry,
                AccountDefaultCurrency = accountDefaultCurrency,
                AccountType = accountType,
                AccountEmail = accountEmail,
                SupportEmail = supportEmail,
                SupportPhone = supportPhone,
                SupportUrl = supportUrl,
                SupportAddress = new MerchantAccountInformation.AddressInformation()
                {
                    City = supportAddress?.City,
                    CountryCode = supportAddress?.Country,
                    Line1 = supportAddress?.Line1,
                    Line2 = supportAddress?.Line2,
                    PostalCode = supportAddress?.PostalCode,
                    StateCode = supportAddress?.State
                },
                PaymentsDescriptor = paymentsDescriptor,
                CardPaymentsDescriptorPrefix = cardPaymentsDescriptorPrefix
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }
}