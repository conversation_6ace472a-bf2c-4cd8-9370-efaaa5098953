using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using Stripe;
using Stripe.TestHelpers;

namespace FlexCharge.Eligibility.Services.StripeServices;

public interface IStripeInvoicesService
{
    Task<Invoice> GetInvoiceAsync(string accountId, string invoiceId);
}

public class StripeInvoicesService : IStripeInvoicesService
{
    private readonly ISecretsManager _secretsManager;

    public StripeInvoicesService(ISecretsManager secretsManager)
    {
        _secretsManager = secretsManager;
    }

    public async Task<Invoice> GetInvoiceAsync(string accountId, string invoiceId)
    {
        using var workspan = Workspan.Start<Services.StripeServices.StripeMerchantInformationService>()
            .Baggage("AccountId", accountId)
            .Baggage("InvoiceId", invoiceId)
            .LogEnterAndExit();

        try
        {
            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = accountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };


            var invoiceService = new InvoiceService();

            var invoice = await invoiceService.GetAsync(invoiceId, requestOptions: stripeRequestOptions);

            return invoice;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }
}