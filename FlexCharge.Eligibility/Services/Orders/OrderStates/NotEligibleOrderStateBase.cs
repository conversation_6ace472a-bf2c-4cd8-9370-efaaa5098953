using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.EligibilityWorkflows;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.RiskManagement;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.Orders.OrderStates;

public abstract class NotEligibleOrderStateBase : TerminalOrderState
{
    public NotEligibleOrderStateBase(OrderState state) : base(state)
    {
    }

    public override async Task AfterStateActivatedAsync(IServiceProvider serviceProvider, Order order,
        StateOfOrder previousState)
    {
        if (order.CannotBeEligibleEver())
        {
            await ProcessNotEligibleEverOrderAsync(serviceProvider, order);
        }

        await ProcessOrderFingerprints(serviceProvider, order);

        await base.AfterStateActivatedAsync(serviceProvider, order, previousState);
    }

    private async Task ProcessNotEligibleEverOrderAsync(IServiceProvider serviceProvider, Order order)
    {
        using var workspan = Workspan.Start<NotEligibleOrderStateBase>();

        try
        {
            var eligibilityService = serviceProvider.GetRequiredService<IEligibilityService>();

            await eligibilityService.ProcessNotEligibleEverOrderAsync(order);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }

    protected virtual async Task ProcessOrderFingerprints(IServiceProvider serviceProvider, Order order)
    {
        using var workspan = Workspan.Start<NotEligibleOrderStateBase>();

        try
        {
            var fingerprintService = serviceProvider.GetRequiredService<IFingerprintService>();

            if (order.CannotBeEligibleEver())
            {
                workspan.Log.Information(
                    "Order cannot be eligible ever ({OrderState}) - Processing fingerprints",
                    order.State);

                await fingerprintService.ProcessOfferNotEligibleEverAsync(order);
            }
            else
            {
                workspan.Log.Information(
                    "Order can be eligible ({OrderState}) - Skipping fingerprints processing",
                    order.State);
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }
}