using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Services.Workflows;

public interface IBlockResolverService
{
    public IBlockImplementation? GetBlockImplementation(string blockId);
}

public class BlockResolverService : IBlockResolverService
{
    private readonly ServiceCollectionExtensions.WorkflowBlockResolver
        _workflowBlockResolver;

    public BlockResolverService(ServiceCollectionExtensions.WorkflowBlockResolver workflowBlockResolver)
    {
        _workflowBlockResolver = workflowBlockResolver;
    }

    public IBlockImplementation? GetBlockImplementation(string blockId)
    {
        if (!blockId.Contains('_'))
            return null; // Not an eligibility check block

        var blockName = WorkflowBuilder<EligibilityCheckContext>.GetBlockIdFromBlockName(blockId);
        var blockImplementation = _workflowBlockResolver(blockName) as IBlockImplementation<EligibilityCheckContext>;
        return blockImplementation;
    }
}