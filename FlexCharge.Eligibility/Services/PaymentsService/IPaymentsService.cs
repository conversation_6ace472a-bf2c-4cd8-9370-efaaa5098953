using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using MassTransit;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.PaymentsService;

public interface IPaymentsService
{
    public Task<FullyAuthorizePaymentCommandResponse> AuthorizeAsync(
        string paymentInstrumentToken,
        Order order, Merchant merchant,
        Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs,
        int? gatewayOrder,
        int? overrideDuplicateOrderCheckTimespan = null,
        PaymentAuthorizationModifiers authorizationModifiers = null);

    public Task<CapturePaymentCommandResponse> CaptureAsync(
        Order order,
        Guid? authorizationTransactionId);

    public Task<Response<DebitPaymentCommandResponse>> SaleAsync(Order order, Merchant merchant, Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs,
        int? gatewayOrder,
        int? overrideDuplicateOrderCheckTimespan = null,
        PaymentAuthorizationModifiers authorizationModifiers = null);

    public Task<bool> VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(Order order);

    public Task<AchVerifiedDebitCommandResponse> AchVerifiedDebit(Order order, ACHTokenType tokenType, string token,
        string accountId,
        string accountName, string accountType, string institutionName);

    public Task<Response<VerifyAmountPaymentCommandResponse>> VerifyAmountAsync(Order order, Merchant merchant,
        Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs,
        int? gatewayOrder,
        int? overrideDuplicateOrderCheckTimespan = null);

    Task RequestNetworkTokenizationAsync(EvaluateRequest evaluateRequest, Order order);
    bool ShouldTryRealTimeAccountUpdater(Merchant merchant, PaymentInstrumentInformation paymentInstrumentInformation);

    #region EXTERNAL TOKEN PAYMENTS

    Task<ChargePaymentCommandResponse> ChargeAsync(Order order, Merchant merchant, Site site);

    Task<bool> CanExternalTokenBeChargedAsync(Order order, Merchant merchant, Site site);

    Task<Response<CancelExternalSubscriptionCommandResponse>> CancelExternalSubscriptionAsync(
        Order order, Merchant merchant, bool canCancelActiveSubscription);

    Task<Response<MarkInvoiceAsPaidOutOfBandCommandResponse>> MarkInvoiceAsPaidOutOfBandAsync(
        string invoiceId, Order order, Merchant merchant);

    #endregion
}