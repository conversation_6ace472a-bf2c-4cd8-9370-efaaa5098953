using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Shared.Tracking.DeviceDetection;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Common;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using MassTransit;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;
using TransactionType = FlexCharge.Eligibility.Entities.Enums.TransactionType;

namespace FlexCharge.Eligibility.Services.PaymentsService;

public class PaymentsService : IPaymentsService
{
    private readonly IRequestClient<FullyAuthorizePaymentCommand> _fullyAuthorizeRequest;
    private readonly IRequestClient<VerifyAmountPaymentCommand> _verifyAmountRequest;
    private readonly IRequestClient<VoidPaymentCommand> _voidPaymentRequest;
    private readonly IRequestClient<AchVerifiedDebitCommand> _achDebitVerifiedRequest;
    private readonly IRequestClient<AchVerifiedCancelPaymentCommand> _achVerifiedCancelPaymentCommandRequest;
    private readonly IRequestClient<CapturePaymentCommand> _capturePaymentRequest;
    private readonly IRequestClient<DebitPaymentCommand> _debitPaymentRequest;
    private readonly IRequestClient<ChargePaymentCommand> _chargePaymentRequest;
    private readonly IRequestClient<CanChargeExternalTokenCommand> _canChargeExternalTokenRequest;
    private readonly IRequestClient<CancelExternalSubscriptionCommand> _cancelExternalSubscriptionRequest;
    private readonly IRequestClient<MarkInvoiceAsPaidOutOfBandCommand> _markInvoiceAsPaidOutOfBandRequest;
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;
    private readonly IDeviceDetectorService _deviceDetectorService;
    private readonly FlexCharge.Grpc.Payments.GrpcPaymentsService.GrpcPaymentsServiceClient _grpcPaymentsServiceClient;

    public PaymentsService(
        IRequestClient<FullyAuthorizePaymentCommand> fullyAuthorizeRequest,
        IRequestClient<VerifyAmountPaymentCommand> verifyAmountRequest,
        IRequestClient<VoidPaymentCommand> voidPaymentRequest,
        IRequestClient<AchVerifiedDebitCommand> achDebitVerifiedRequest,
        IRequestClient<AchVerifiedCancelPaymentCommand> achVerifiedCancelPaymentCommandRequest,
        IRequestClient<CapturePaymentCommand> capturePaymentRequest,
        IRequestClient<DebitPaymentCommand> debitPaymentRequest,
        IRequestClient<ChargePaymentCommand> chargePaymentRequest,
        IRequestClient<CanChargeExternalTokenCommand> canChargeExternalTokenRequest,
        IRequestClient<CancelExternalSubscriptionCommand> cancelExternalSubscriptionRequest,
        IRequestClient<MarkInvoiceAsPaidOutOfBandCommand> markInvoiceAsPaidOutOfBandRequest,
        IPublishEndpoint publisher,
        IActivityService activityService,
        IDeviceDetectorService deviceDetectorService,
        Grpc.Payments.GrpcPaymentsService.GrpcPaymentsServiceClient grpcPaymentsServiceClient)
    {
        _fullyAuthorizeRequest = fullyAuthorizeRequest;
        _verifyAmountRequest = verifyAmountRequest;
        _voidPaymentRequest = voidPaymentRequest;
        _activityService = activityService;
        _deviceDetectorService = deviceDetectorService;
        _grpcPaymentsServiceClient = grpcPaymentsServiceClient;
        _achDebitVerifiedRequest = achDebitVerifiedRequest;
        _achVerifiedCancelPaymentCommandRequest = achVerifiedCancelPaymentCommandRequest;
        _capturePaymentRequest = capturePaymentRequest;
        _debitPaymentRequest = debitPaymentRequest;
        _chargePaymentRequest = chargePaymentRequest;
        _canChargeExternalTokenRequest = canChargeExternalTokenRequest;
        _cancelExternalSubscriptionRequest = cancelExternalSubscriptionRequest;
        _markInvoiceAsPaidOutOfBandRequest = markInvoiceAsPaidOutOfBandRequest;
        _publisher = publisher;
    }

    #region Authorize

    public async Task<FullyAuthorizePaymentCommandResponse> AuthorizeAsync(
        string paymentInstrumentToken,
        Order order,
        Merchant merchant, Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs,
        int? gatewayOrder,
        int? overrideDuplicateOrderCheckTimespan,
        PaymentAuthorizationModifiers authorizationModifiers = null)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Tag("PaymentInstrumentToken", paymentInstrumentToken)
            .Tag("Amount", order.Amount)
            .Tag("GatewayOrder", gatewayOrder);

        FullyAuthorizePaymentCommandResponse authorizePaymentCommandResponse = null;

        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            bool useDynamicDescriptor = true;

            var descriptor = new Descriptor
            {
                Name = site.Descriptor,
                City = site.DescriptorCity,
                Phone = site.CustomerSupportPhone,
                Url = site.CustomerSupportLink
            };

            int amount = order.Amount;
            int discount = 0;

            #region Processing Authorization Modifiers (Cascading Payment)

            bool performCascadingPayment = false;
            if (authorizationModifiers?.PerformCascadingPayment == true)
            {
                performCascadingPayment = true;
                var previousAuthorizationTransaction = order.GetLastPaymentTransactionResponse();
                gatewayOrder = previousAuthorizationTransaction?.UsedGatewayOrder;
            }

            #endregion

            #region Processing Authorization Modifiers (Dynamic Discount)

            if (authorizationModifiers?.DynamicAmountDiscount > 0)
            {
                discount = authorizationModifiers.DynamicAmountDiscount;
                amount = amount - discount;
            }

            #endregion

            var paymentProviderBlackList = order.GetPaymentProviderBlackList().ToList();

            EnsureDynamicAuthorizationAmountDiscountValueIsValid(order, discount, amount);

            #region Observability

            if (discount > 0)
            {
                workspan.Log.Information("Discount applied: {Discount}", discount);

                await AddActivityAsync(EligibilityActivities.Discount_Applied, order, meta: meta => meta
                    .SetValue("Amount", discount)
                );
            }

            #endregion

            var fullyAuthorizePaymentCommand = new FullyAuthorizePaymentCommand
            {
                Mid = order.Mid,
                GatewayOrder = gatewayOrder,

                PerformCascadingPayment = performCascadingPayment,
                PaymentRoutingStrategy = order.PaymentRoutingStrategy ?? string.Empty,

                BlocklistedProviders = paymentProviderBlackList,

                PaymentInstrument = new PaymentInstrumentModel
                {
                    ScaAuthenticationToken = order.SCAAuthenticationToken,
                    PaymentInstrumentToken = paymentInstrumentToken,
                    Brand = evaluateRequest.PaymentMethod?.CardBrand,
                    Type = evaluateRequest.PaymentMethod?.CardType,
                    Bin = evaluateRequest.PaymentMethod?.CardBinNumber,
                    Last4 = evaluateRequest.PaymentMethod?.CardLast4Digits,
                    Country = evaluateRequest.PaymentMethod?.CardCountry
                },

                Currency = evaluateRequest.Transaction.Currency,
                Amount = amount,
                Discount = discount,

                UseDynamicDescriptor = useDynamicDescriptor,
                Descriptor = useDynamicDescriptor ? descriptor : null,

                OrderId = order.Id,
                PayerId = order.PayerId,

                OverrideDuplicateOrderCheckTimespan = overrideDuplicateOrderCheckTimespan,

                BillingAddress = evaluateRequest.BillingInformation.ToBillingAddress(),
                UseBillingAsShipping = evaluateRequest.ShippingInformation == null,

                ShippingAddress = evaluateRequest.ShippingInformation?.ToShippingAddress(),


                PayerEmail = evaluateRequest.Payer.Email,

                IsCit = order.IsCIT,

                SchemeTransactionId = evaluateRequest.Subscription?.SchemeTransactionId,

                DeviceInformation = GetDeviceInformation(order, evaluateRequest),

                UserDefinedFields = order.GetPaymentProviderUserDefinedFields(),

                TryUseAccountUpdater = ShouldTryRealTimeAccountUpdater(merchant, paymentInstrumentInformation),

                AlreadyTriedProviders = GetAlreadyTriedProviders(order),

                RiskTier = merchant.RiskTier
            };

            if (authorizationModifiers != null)
            {
                fullyAuthorizePaymentCommand.Modifiers = authorizationModifiers.ToPaymentModifiersModel();
            }

            if (threeDs != null)
            {
                fullyAuthorizePaymentCommand.ThreeDs = new ThreeDS()
                {
                    ThreeDsVersion = threeDs.ThreeDsVersion,
                    EcommerceIndicator = threeDs.EcommerceIndicator,
                    AuthenticationValue = threeDs.AuthenticationValue,
                    DirectoryServerTransactionId = threeDs.DirectoryServerTransactionId,
                    Xid = threeDs.Xid,
                    AuthenticationValueAlgorithm = threeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = threeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = threeDs.AuthenticationResponseStatus,
                    Enrolled = threeDs.Enrolled
                };
            }


            workspan.LogEligibility.Information(
                $"Eligibility > Initiating > Authorization {fullyAuthorizePaymentCommand}");
            await AddActivityAsync(EligibilityActivities.FullAuthorization_Starting, order,
                data: fullyAuthorizePaymentCommand);

            try
            {
                var response = await _grpcPaymentsServiceClient.FullyAuthorizePaymentAsync(new()
                {
                    Request = JsonConvert.SerializeObject(fullyAuthorizePaymentCommand)
                });

                authorizePaymentCommandResponse =
                    JsonConvert.DeserializeObject<FullyAuthorizePaymentCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                var fullyAuthorizePaymentCommandResponse =
                    await _fullyAuthorizeRequest
                        .RunIdempotentCommandAsync<FullyAuthorizePaymentCommand, FullyAuthorizePaymentCommandResponse>(
                            fullyAuthorizePaymentCommand);

                authorizePaymentCommandResponse = fullyAuthorizePaymentCommandResponse.Message;
            }

            workspan.LogEligibility.Information(
                $"Eligibility > Completed > Authorization >  Response: {JsonConvert.SerializeObject(authorizePaymentCommandResponse)}");

            await AddActivityAsync(EligibilityActivities.FullAuthorization_ResultReceived, order,
                data: authorizePaymentCommandResponse,
                meta: meta => meta
                    .TransactionReference(authorizePaymentCommandResponse.TransactionId)
                    .SetValue("AvsCode", authorizePaymentCommandResponse.AvsCode)
                    .SetValue("CvvCode", authorizePaymentCommandResponse.CvvCode));

            if (authorizePaymentCommandResponse.Success)
            {
                order.PaymentTransactionResult =
                    JsonConvert.SerializeObject(new PaymentTransactionResult(authorizePaymentCommandResponse));
            }
            else order.PaymentTransactionResult = null;

            order.AddPaymentTransactionResponse(new PaymentTransactionResponse
            (
                DateTime.UtcNow,
                TransactionType.FullAuthorization,
                authorizePaymentCommandResponse?.Provider,
                "",
                authorizePaymentCommandResponse.ProviderResponseCode,
                authorizePaymentCommandResponse.CvvCode,
                authorizePaymentCommandResponse.AvsCode,
                authorizePaymentCommandResponse.GatewayOrder,
                authorizePaymentCommandResponse.InternalResponseCode,
                authorizePaymentCommandResponse.InternalResponseMessage,
                authorizePaymentCommandResponse.InternalResponseGroup,
                authorizePaymentCommandResponse.GatewayId,
                authorizePaymentCommandResponse.SupportedGatewayId
            ));
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Cannot perform Authorization");
            await AddActivityAsync(EligibilityErrorActivities.FullAuthorization_Error, order, data: ex,
                meta: meta => meta
                    .Error(ex.Message));
        }

        if (authorizePaymentCommandResponse?.Success == true)
        {
            workspan.Log.Information("Authorization succeeded");
        }
        else
        {
            workspan.Log.Information("Authorization failed: {ResponseMessage}",
                JsonConvert.SerializeObject(authorizePaymentCommandResponse?.ResponseMessage));
        }

        workspan.Log.Information(
            $"Authorization Response: {JsonConvert.SerializeObject(authorizePaymentCommandResponse)}");

        return authorizePaymentCommandResponse;
    }

    #endregion

    private List<FullyAuthorizePaymentCommand.AlreadyTriedProvidersModel> GetAlreadyTriedProviders(Order order)
    {
        var allTransactionResponses = order.GetPaymentTransactionsResponses();

        string[] includeTransactionTypes =
        {
            nameof(TransactionType.FullAuthorization),
            nameof(TransactionType.Sale),
            // nameof(TransactionType.VerifyAmount),
            // nameof(TransactionType.ZeroVerification)
        };

        return allTransactionResponses
            .Where(x => includeTransactionTypes.Contains(x.TransactionType))
            .Where(x => x.SupportedGatewayId != null)
            .Select(x =>
                new FullyAuthorizePaymentCommand.AlreadyTriedProvidersModel(
                    SupportedGatewayId: x.SupportedGatewayId.Value, Timestamp: x.Time))
            .ToList();
    }


    public bool ShouldTryRealTimeAccountUpdater(Merchant merchant,
        PaymentInstrumentInformation paymentInstrumentInformation)
    {
        if (!merchant.AccountUpdaterEnabled)
        {
            Workspan.Current?.Log.Information("Skipping Account Updater - disabled");
            return false;
        }

        // Do not try account updater more than once per month
        TimeSpan accountUpdaterInterval = TimeSpan.FromDays(30);

        if ((paymentInstrumentInformation.AccountUpdaterStatus.LastResponseTime != null &&
             DateTime.UtcNow - paymentInstrumentInformation.AccountUpdaterStatus.LastResponseTime <
             accountUpdaterInterval) || // card update was checked recently (is null for newly updated card)
            (paymentInstrumentInformation.AccountUpdaterStatus.LastUpdateTime != null &&
             DateTime.UtcNow - paymentInstrumentInformation.AccountUpdaterStatus.LastUpdateTime <
             accountUpdaterInterval)) // card update was performed recently (is null if update requested, but no new card received)
        {
            Workspan.Current?.Log
                .Information(
                    "Skipping Account Updater - last response was less than {AccountUpdaterIntervalDays} days ago",
                    accountUpdaterInterval.Days);

            return false;
        }

        return true;
    }

    private DeviceInfo? GetDeviceInformation(Order order, EvaluateRequest evaluateRequest)
    {
        DeviceInfo? deviceInfo = null;
        // Use browser information to get device information
        var browserInfoFromTracking = order.GetBrowserInformationOrDefault();

        if (browserInfoFromTracking != null)
        {
            deviceInfo = _deviceDetectorService.GetDeviceData(browserInfoFromTracking);
        }
        else
        {
            // If no browser information present (user session not matched)
            // Use information from Evaluate request, if present
            deviceInfo =
                _deviceDetectorService.GetDeviceData(evaluateRequest.CustomerIp, evaluateRequest.DeviceDetails);
        }

        return deviceInfo;
    }


    #region Void

    public async Task<bool> VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(Order order)
    {
        using var workspan = Workspan.Start<PaymentsService>();

        if (order.State.ToOrderState() == OrderState.ORDER_APPROVED)
        {
            workspan.Log.Fatal("Trying to Void payment for approved order");
            await AddActivityAsync(EligibilityErrorActivities.VoidPayment_CannotVoidApprovedOrder_Error, order);

            return false;
        }

        if (order.IsPaymentCaptured == true)
        {
            workspan.Log.Fatal("Trying to Void payment for captured order");
            await AddActivityAsync(EligibilityErrorActivities.VoidPayment_CannotVoidCapturedOrder_Error, order);

            return false;
        }

        if (order.PaymentTransactionResult != null)
        {
            #region Void Authoriation

            PaymentTransactionResult paymentTransactionResult = null;
            try
            {
                paymentTransactionResult =
                    JsonConvert.DeserializeObject<PaymentTransactionResult>(order.PaymentTransactionResult);

                workspan.Tag("PaymentTransactionResult", paymentTransactionResult);

                if (paymentTransactionResult?.Success == false) return false; //full authorization was not successful 

                // Order is not approved, but already authorized -> void authorization

                var voidPaymentCommandParameters = new
                {
                    Mid = order.Mid,
                    TransactionId = paymentTransactionResult.TransactionId,
                    OrderId = order.Id
                };

                await AddActivityAsync(EligibilityActivities.FullAuthorization_VoidPaymentForNotApprovedOrder, order,
                    data: voidPaymentCommandParameters, meta: meta => meta
                        .TransactionReference(paymentTransactionResult.TransactionId));

                VoidPaymentCommandResponse voidPaymentCommandResponse;
                try
                {
                    var response = await _grpcPaymentsServiceClient.VoidPaymentAsync(new()
                    {
                        Request = JsonConvert.SerializeObject(voidPaymentCommandParameters)
                    });

                    voidPaymentCommandResponse =
                        JsonConvert.DeserializeObject<VoidPaymentCommandResponse>(response.Response);
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                    voidPaymentCommandResponse =
                        (await _voidPaymentRequest
                            .GetResponse<VoidPaymentCommandResponse>(voidPaymentCommandParameters)).Message;
                }

                if (voidPaymentCommandResponse.Success)
                {
                    workspan.Log.Information("Successfully Voided payment for not eligible order");

                    order.PaymentTransactionResult = null;

                    await AddActivityAsync(
                        EligibilityActivities.FullAuthorization_VoidPaymentForNotApprovedOrder_Succeeded, order,
                        data: voidPaymentCommandResponse, meta: meta => meta
                            .TransactionReference(paymentTransactionResult.TransactionId));

                    return true;
                }
                else
                {
                    workspan.Log.Fatal("Cannot Void payment for not eligible order");

                    await AddActivityAsync(
                        EligibilityErrorActivities.FullAuthorization_VoidPaymentForNotApprovedOrder_Failed,
                        order, data: voidPaymentCommandResponse, meta: meta => meta
                            .TransactionReference(paymentTransactionResult.TransactionId));
                }
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex, "Cannot perform Void Payment for not eligible order");
                await AddActivityAsync(
                    EligibilityErrorActivities.FullAuthorization_VoidPaymentForNotApprovedOrder_Error, order, data: ex,
                    meta: meta => meta
                        .TransactionReference(paymentTransactionResult?.TransactionId.ToString() ?? ""));
            }

            #endregion
        }

        if (order.ACHTransactionId != null)
        {
            #region Cancel ACH Debit Payment

            string achTransactionId = null;
            try
            {
                achTransactionId = order.ACHTransactionId;

                workspan.Tag("ACHToken", achTransactionId);

                // Order is not approved, but already debited -> cancel debit payment

                var achCancelPaymentCommand = new AchVerifiedCancelPaymentCommand
                {
                    Mid = order.Mid,
                    TransactionId = Guid.Parse(achTransactionId), // it's always a Guid
                    OrderId = order.Id
                };

                await AddActivityAsync(EligibilityActivities.AchVerifiedDebit_CancelPaymentForNotApprovedOrder, order,
                    data: achCancelPaymentCommand, meta: meta => meta
                        .TransactionReference(achTransactionId));

                var voidPaymentCommandResponse =
                    await _achVerifiedCancelPaymentCommandRequest.GetResponse<AchVerifiedCancelPaymentCommandResponse>(
                        achCancelPaymentCommand);

                if (voidPaymentCommandResponse.Message.Success)
                {
                    workspan.Log.Information("Successfully Cancelled Verified ACH payment for not eligible order");

                    order.ACHTransactionId = null;

                    return true;
                }
                else
                {
                    workspan.Log.Error("Cannot Cancel Verified ACH payment for not eligible order");

                    await AddActivityAsync(
                        EligibilityErrorActivities.AchVerifiedDebit_CancelPaymentForNotApprovedOrder_Failed,
                        order, data: voidPaymentCommandResponse.Message, meta: meta => meta
                            .TransactionReference(achTransactionId));
                }
            }
            catch (Exception ex)
            {
                workspan.RecordException(ex, "Cannot perform Verified Cancel ACH payment for not eligible order");
                await AddActivityAsync(
                    EligibilityErrorActivities.AchVerifiedDebit_CancelPaymentForNotApprovedOrder_Error, order, data: ex,
                    meta: meta => meta
                        .TransactionReference(achTransactionId));
            }

            #endregion
        }

        return false;
    }

    #endregion

    #region Capture

    public async Task<CapturePaymentCommandResponse> CaptureAsync(Order order,
        Guid? authorizationTransactionId)
    {
        using var workspan = Workspan.Start<PaymentsService>();

        if (authorizationTransactionId == null || authorizationTransactionId.Value == Guid.Empty)
        {
            string errorMessage = $"Empty authorizationTransactionId";
            workspan.Log.Error(errorMessage);

            await AddActivityAsync(EligibilityErrorActivities.Payment_Capture_Error, order,
                data: errorMessage,
                meta: meta => meta
                    .TransactionReference(authorizationTransactionId?.ToString() ?? ""));
        }

        var capturePaymentCommandResponse = default(CapturePaymentCommandResponse);

        try
        {
            var capturePaymentCommand = new CapturePaymentCommand
            {
                Mid = order.Mid,
                TransactionId = authorizationTransactionId.HasValue ? authorizationTransactionId.Value : Guid.Empty,
                OrderId = order.Id
            };

            try
            {
                var response = await _grpcPaymentsServiceClient.CapturePaymentAsync(new()
                {
                    Request = JsonConvert.SerializeObject(capturePaymentCommand)
                });

                capturePaymentCommandResponse =
                    JsonConvert.DeserializeObject<CapturePaymentCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                capturePaymentCommandResponse =
                    (await _capturePaymentRequest.GetResponse<CapturePaymentCommandResponse>(capturePaymentCommand))
                    .Message;
            }
        }
        catch (Exception ex)
        {
            await AddActivityAsync(EligibilityErrorActivities.Payment_Capture_Error, order,
                data: ex.Message,
                meta: meta => meta
                    .TransactionReference(authorizationTransactionId?.ToString() ?? ""));

            workspan.RecordException(ex, "Cannot perform Payment Capture");
        }

        workspan.Log.Information(
            $"Capture Payment Response: {JsonConvert.SerializeObject(capturePaymentCommandResponse)}");

        return capturePaymentCommandResponse;
    }

    #endregion

    #region Verify Amount

    public async Task<Response<VerifyAmountPaymentCommandResponse>> VerifyAmountAsync(Order order, Merchant merchant,
        Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs,
        int? gatewayOrder, int? overrideDuplicateOrderCheckTimespan = null)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Tag("Amount", order.Amount)
            .Tag("GatewayOrder", gatewayOrder);


        var verifyAmountPaymentCommandResponse = default(Response<VerifyAmountPaymentCommandResponse>);

        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            bool UseDynamicDescriptor = true;

            var descriptor = new Descriptor
            {
                Name = site.Descriptor,
                City = site.DescriptorCity,
                Phone = site.CustomerSupportPhone,
            };

            var verifyAmountPaymentCommand = new VerifyAmountPaymentCommand
            {
                Mid = order.Mid,
                GatewayOrder = gatewayOrder,

                PaymentInstrumentToken = order.PaymentInstrumentToken,
                ScaAuthenticationToken = order.SCAAuthenticationToken,

                Currency = evaluateRequest.Transaction.Currency,
                Amount = order.Amount,

                UseDynamicDescriptor = UseDynamicDescriptor,
                Descriptor = UseDynamicDescriptor ? descriptor : null,

                OrderId = order.Id,
                PayerId = order.PayerId,

                OverrideDuplicateOrderCheckTimespan = overrideDuplicateOrderCheckTimespan,

                BillingAddress = evaluateRequest.BillingInformation.ToBillingAddress(),

                TryUseAccountUpdater = ShouldTryRealTimeAccountUpdater(merchant, paymentInstrumentInformation),

                DeviceInformation = GetDeviceInformation(order, evaluateRequest)
            };

            if (threeDs != null)
            {
                verifyAmountPaymentCommand.ThreeDs = new ThreeDS()
                {
                    ThreeDsVersion = threeDs.ThreeDsVersion,
                    EcommerceIndicator = threeDs.EcommerceIndicator,
                    AuthenticationValue = threeDs.AuthenticationValue,
                    DirectoryServerTransactionId = threeDs.DirectoryServerTransactionId,
                    Xid = threeDs.Xid,
                    AuthenticationValueAlgorithm = threeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = threeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = threeDs.AuthenticationResponseStatus,
                    Enrolled = threeDs.Enrolled
                };
            }

            workspan.LogEligibility.Information(
                $"Eligibility > Initiating > VerifyAmount {JsonConvert.SerializeObject(verifyAmountPaymentCommand)}");
            await AddActivityAsync(EligibilityActivities.VerifyAmount_Starting, order,
                data: verifyAmountPaymentCommand);

            verifyAmountPaymentCommandResponse =
                await _verifyAmountRequest
                    .RunIdempotentCommandAsync<VerifyAmountPaymentCommand, VerifyAmountPaymentCommandResponse>(
                        verifyAmountPaymentCommand);

            workspan.LogEligibility.Information(
                $"Eligibility > Completed > VerifyAmount >  Response: {JsonConvert.SerializeObject(verifyAmountPaymentCommandResponse.Message)}");

            if (verifyAmountPaymentCommandResponse.Message.GatewayFound)
            {
                await AddActivityAsync(EligibilityActivities.VerifyAmount_ResultReceived, order,
                    data: verifyAmountPaymentCommandResponse,
                    meta: meta => meta
                        .TransactionReference(verifyAmountPaymentCommandResponse.Message.TransactionId)
                        .SetValue("AvsCode", verifyAmountPaymentCommandResponse.Message.AvsCode)
                        .SetValue("CvvCode", verifyAmountPaymentCommandResponse.Message.CvvCode));

                // if (verifyAmountPaymentCommandResponse.Message.Success)
                // {
                //     order.FullAuthorizationResult =
                //         JsonConvert.SerializeObject(new FullyAuthorizeResult(verifyAmountPaymentCommandResponse.Message));
                // }
                // else order.FullAuthorizationResult = null;

                order.AddPaymentTransactionResponse(new PaymentTransactionResponse(
                    DateTime.UtcNow,
                    TransactionType.VerifyAmount,
                    verifyAmountPaymentCommandResponse?.Message.Provider,
                    "",
                    verifyAmountPaymentCommandResponse.Message.ProviderResponseCode,
                    verifyAmountPaymentCommandResponse.Message.CvvCode,
                    verifyAmountPaymentCommandResponse.Message.AvsCode,
                    null,
                    verifyAmountPaymentCommandResponse.Message.InternalResponseCode,
                    verifyAmountPaymentCommandResponse.Message.InternalResponseMessage,
                    verifyAmountPaymentCommandResponse.Message.InternalResponseGroup
                ));
            }
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Cannot perform Verify Amount");
            await AddActivityAsync(EligibilityErrorActivities.VerifyAmount_Error, order, data: ex);
        }

        if (verifyAmountPaymentCommandResponse?.Message.Success == true)
        {
            workspan.Log.Information("Verify Amount succeeded");
        }
        else
        {
            workspan.Log.Information("Verify Amount failed: {Response}",
                JsonConvert.SerializeObject(verifyAmountPaymentCommandResponse?.Message));
        }

        workspan.Log.Information(
            $"Authorization Response: {JsonConvert.SerializeObject(verifyAmountPaymentCommandResponse?.Message)}");

        return verifyAmountPaymentCommandResponse;
    }

    #endregion

    #region Sale

    public async Task<Response<DebitPaymentCommandResponse>> SaleAsync(Order order, Merchant merchant, Site site,
        PaymentInstrumentInformation paymentInstrumentInformation,
        ThreeDSResult threeDs, int? gatewayOrder,
        int? overrideDuplicateOrderCheckTimespan, PaymentAuthorizationModifiers authorizationModifiers = null)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Tag("Amount", order.Amount)
            .Tag("GatewayOrder", gatewayOrder);


        var salePaymentCommandResponse = default(Response<DebitPaymentCommandResponse>);

        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            bool useDynamicDescriptor = true;

            var descriptor = new Descriptor
            {
                Name = site.Descriptor,
                City = site.DescriptorCity,
                Phone = site.CustomerSupportPhone,
            };

            int amount = order.Amount;
            int discount = 0;

            #region Processing Authorization Modifiers (Cascading Payment)

            bool performCascadingPayment = false;
            if (authorizationModifiers != null)
            {
                if (authorizationModifiers.PerformCascadingPayment)
                {
                    performCascadingPayment = true;
                    var previousAuthorizationTransaction = order.GetLastPaymentTransactionResponse();
                    gatewayOrder = previousAuthorizationTransaction?.UsedGatewayOrder;
                }
            }

            #endregion

            #region Processing Authorization Modifiers (Dynamic Discount)

            if (authorizationModifiers?.DynamicAmountDiscount > 0)
            {
                discount = authorizationModifiers.DynamicAmountDiscount;
                amount = amount - discount;
            }

            #endregion

            var paymentProviderBlackList = order.GetPaymentProviderBlackList().ToList();


            EnsureDynamicAuthorizationAmountDiscountValueIsValid(order, discount, amount);

            #region Observability

            if (discount > 0)
            {
                workspan.Log.Information("Discount applied: {Discount}", discount);

                await AddActivityAsync(EligibilityActivities.Discount_Applied, order, meta: meta => meta
                    .SetValue("Amount", discount)
                );
            }

            #endregion

            var salePaymentCommandParameters = new DebitPaymentCommand
            {
                Mid = order.Mid,
                GatewayOrder = gatewayOrder,

                PerformCascadingPayment = performCascadingPayment,
                PaymentRoutingStrategy = order.PaymentRoutingStrategy ?? string.Empty,

                BlocklistedProviders = paymentProviderBlackList,


                PaymentInstrument = new PaymentInstrumentModel
                {
                    ScaAuthenticationToken = order.SCAAuthenticationToken,
                    PaymentInstrumentToken = order.PaymentInstrumentToken,
                    Brand = evaluateRequest.PaymentMethod?.CardBrand,
                    Type = evaluateRequest.PaymentMethod?.CardType,
                    Bin = evaluateRequest.PaymentMethod?.CardBinNumber,
                    Last4 = evaluateRequest.PaymentMethod?.CardLast4Digits,
                    Country = evaluateRequest.PaymentMethod?.CardCountry
                },

                Currency = evaluateRequest.Transaction.Currency,
                Amount = amount,
                Discount = discount,

                UseDynamicDescriptor = useDynamicDescriptor,
                Descriptor = useDynamicDescriptor ? descriptor : null,

                OrderId = order.Id,
                PayerId = order.PayerId,

                OverrideDuplicateOrderCheckTimespan = overrideDuplicateOrderCheckTimespan,

                BillingAddress = evaluateRequest.BillingInformation.ToBillingAddress(),
                UseBillingAsShipping = evaluateRequest.ShippingInformation == null,

                ShippingAddress = evaluateRequest.ShippingInformation?.ToShippingAddress(),

                PayerEmail = evaluateRequest.Payer.Email,

                IsCit = order.IsCIT,

                SchemeTransactionId = evaluateRequest.Subscription?.SchemeTransactionId,

                DeviceInformation = GetDeviceInformation(order, evaluateRequest),

                UserDefinedFields = order.GetPaymentProviderUserDefinedFields(),

                TryUseAccountUpdater = ShouldTryRealTimeAccountUpdater(merchant, paymentInstrumentInformation),

                AlreadyTriedProviders = GetAlreadyTriedProviders(order)
            };

            if (authorizationModifiers != null)
            {
                salePaymentCommandParameters.Modifiers = authorizationModifiers.ToPaymentModifiersModel();
            }

            if (threeDs != null)
            {
                salePaymentCommandParameters.ThreeDs = new ThreeDS()
                {
                    ThreeDsVersion = threeDs.ThreeDsVersion,
                    EcommerceIndicator = threeDs.EcommerceIndicator,
                    AuthenticationValue = threeDs.AuthenticationValue,
                    DirectoryServerTransactionId = threeDs.DirectoryServerTransactionId,
                    Xid = threeDs.Xid,
                    AuthenticationValueAlgorithm = threeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = threeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = threeDs.AuthenticationResponseStatus,
                    Enrolled = threeDs.Enrolled
                };
            }


            workspan.LogEligibility.Information(
                $"Eligibility > Initiating > Sale {salePaymentCommandParameters}");
            await AddActivityAsync(EligibilityActivities.Sale_Starting, order,
                data: salePaymentCommandParameters);

            salePaymentCommandResponse =
                await _debitPaymentRequest
                    .RunIdempotentCommandAsync<DebitPaymentCommand, DebitPaymentCommandResponse>(
                        salePaymentCommandParameters);

            var saleResponse = salePaymentCommandResponse.Message;

            workspan.LogEligibility.Information(
                $"Eligibility > Completed > Sale > Response: {JsonConvert.SerializeObject(saleResponse)}");

            await AddActivityAsync(EligibilityActivities.Sale_ResultReceived, order,
                data: salePaymentCommandResponse,
                meta: meta => meta
                    .TransactionReference(saleResponse.TransactionId)
                    .SetValue("AvsCode", saleResponse.AvsCode)
                    .SetValue("CvvCode", saleResponse.CvvCode));

            if (saleResponse.Success)
            {
                order.PaymentTransactionResult =
                    JsonConvert.SerializeObject(new PaymentTransactionResult(saleResponse));
            }
            else order.PaymentTransactionResult = null;

            order.AddPaymentTransactionResponse(new PaymentTransactionResponse
            (
                DateTime.UtcNow,
                TransactionType.Sale,
                salePaymentCommandResponse?.Message.Provider,
                "",
                saleResponse.ProviderResponseCode,
                saleResponse.CvvCode,
                saleResponse.AvsCode,
                saleResponse.GatewayOrder,
                saleResponse.InternalResponseCode,
                saleResponse.InternalResponseMessage,
                saleResponse.InternalResponseGroup,
                saleResponse.GatewayId,
                saleResponse.SupportedGatewayId
            ));
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Cannot perform Sale");
            await AddActivityAsync(EligibilityErrorActivities.Sale_Error, order, data: ex);
        }

        if (salePaymentCommandResponse?.Message.Success == true)
        {
            workspan.Log.Information("Sale succeeded");
        }
        else
        {
            workspan.Log.Information("Sale failed: {ResponseMessage}",
                JsonConvert.SerializeObject(salePaymentCommandResponse?.Message.ResponseMessage));
        }

        workspan.Log.Information(
            $"Sale Response: {JsonConvert.SerializeObject(salePaymentCommandResponse?.Message)}");

        return salePaymentCommandResponse;
    }

    private static void EnsureDynamicAuthorizationAmountDiscountValueIsValid(Order order, int discount, int amount)
    {
        if (discount > 10)
            throw new FlexChargeException("Dynamic Discount cannot be more than 10 cents");

        if (amount < 0)
            throw new FlexChargeException("Amount cannot be less than 0 after applying Dynamic Discount");

        if (amount + discount != order.Amount)
            throw new FlexChargeException("Amount and Discount sum does not match the order amount");
    }

    #endregion

    #region AchVerifiedDebit

    public async Task<AchVerifiedDebitCommandResponse> AchVerifiedDebit(
        Order order, ACHTokenType tokenType, string token,
        string accountId, string accountName, string accountType, string institutionName)
    {
        //Get account information from plaid
        //generate new processing token
        //store payment instrument in the database
        //run a verified ach transaction
        //if successful, return UserChallengeCureResult.CURED

        using var workspan = Workspan.Start<PaymentsService>();

        try
        {
            var achVerifiedDebitCommandParameters = new AchVerifiedDebitCommand
            {
                Mid = order.Mid,
                TokenType = tokenType == ACHTokenType.PublicToken ? AchTokenType.PublicToken : AchTokenType.AccessToken,
                Token = token,
                Amount = order.Amount,
                Currency = "USD",
                OrderId = order.Id,
                PayerId = order.PayerId,
                AccountId = accountId,
                AccountName = accountName,
                AccountType = accountType,
                InstitutionName = institutionName,
            };


            workspan.LogEligibility.Information(
                "Eligibility > Initiating > AchVerifiedDebit {AchVerifiedDebitCommandParameters)}",
                JsonConvert.SerializeObject(achVerifiedDebitCommandParameters));
            await AddActivityAsync(EligibilityActivities.AchVerifiedDebit_Starting, order,
                data: achVerifiedDebitCommandParameters);

            var achVerifiedDebitCommandResponse =
                await _achDebitVerifiedRequest.GetResponse<AchVerifiedDebitCommandResponse>(
                    achVerifiedDebitCommandParameters);

            await AddActivityAsync(EligibilityActivities.AchVerifiedDebit_ResultReceived, order,
                data: achVerifiedDebitCommandResponse,
                meta: meta => meta
                    .TransactionReference(achVerifiedDebitCommandResponse.Message.TransactionId));

            order.AddPaymentTransactionResponse(new PaymentTransactionResponse(
                DateTime.UtcNow,
                TransactionType.AchVerifiedDebit,
                "SVB",
                "",
                achVerifiedDebitCommandResponse.Message.Success ? "100" : "",
                null,
                null
            ));

            if (!achVerifiedDebitCommandResponse.Message.Success)
            {
                workspan.Log.Information("Ach Verified Debit failed: {ResponseMessage}",
                    achVerifiedDebitCommandResponse?.Message.ResponseMessage);
            }
            else
            {
                workspan.Log.Information("Ach Verified Debit succeeded");
            }

            return achVerifiedDebitCommandResponse.Message;
        }

        catch (Exception ex)
        {
            workspan.RecordException(ex, "Cannot perform Ach Verified Debit");
            await AddActivityAsync(EligibilityErrorActivities.AchVerifiedDebit_Error, order, data: ex);
        }

        return null;
    }

    #endregion

    #region Observability

    protected async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order order,
        string eventName = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await _activityService.CreateActivityAsync(activityNameEnum,
            set => set
                .TenantId(order.Mid)
                .CorrelationId(order.Id)
                .Subcategory(subcategory)
                .EventRaised(eventName)
                .Data(data)
                .Meta(meta));
    }

    #endregion

    #region Network Tokenization

    public async Task RequestNetworkTokenizationAsync(EvaluateRequest evaluateRequest, Order order)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        var email = evaluateRequest.Payer?.Email;

        if (string.IsNullOrWhiteSpace(email))
        {
            workspan.Log.Information("No email provided for network tokenization");
            return;
        }

        if (evaluateRequest.PaymentMethod == null)
        {
            workspan.Log.Information("No payment method provided for network tokenization");
            return;
        }

        if (!Guid.TryParse(evaluateRequest.PaymentMethod.CardNumber, out var paymentInstrumentToken))
        {
            workspan.Log.Information("Invalid payment instrument token for network tokenization");
            return;
        }

        var billingAddress = evaluateRequest.BillingInformation.ToAddress();

        if (billingAddress == null)
        {
            workspan.Log.Information("No billing address provided for network tokenization");
            return;
        }

        workspan.Log.Information("Requesting network tokenization");

        string cardHolderFirstName;
        string cardHolderLastName;
        if (!Utils.NameHelpers.TrySplitToFirstAndLastName(evaluateRequest.PaymentMethod.HolderName,
                out cardHolderFirstName, out cardHolderLastName))
        {
            workspan.Log.Information("Cannot split card holder name for network tokenization");

            cardHolderFirstName = evaluateRequest.PaymentMethod.HolderName;
            cardHolderLastName = "";
        }

        //TODO: Store DeviceInfo on Order (if available) and send it to the network tokenization service. Use DeviceDetector Nuget?
        var networkTokenRequestedEvent = new NetworkTokenRequestedEvent
        {
            Mid = order.Mid,
            OrderId = order.Id,
            PaymentInstrumentToken = paymentInstrumentToken,
            Email = email,
            SenseKey = order.SenseKey,
            BillingAddress = billingAddress,
            CardHolderFirstName = cardHolderFirstName,
            CardHolderLastName = cardHolderLastName,
        };
        await _publisher.Publish(networkTokenRequestedEvent);

        await AddActivityAsync(EligibilityActivities.NetworkTokenization_Requested, order,
            data: networkTokenRequestedEvent);
    }

    #endregion

    #region Charge

    public async Task<ChargePaymentCommandResponse> ChargeAsync(Order order,
        Merchant merchant,
        Site site)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Tag("Amount", order.Amount);


        ChargePaymentCommandResponse chargeResponse = null;
        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            int amount = order.Amount;

            var chargePaymentCommandParameters = new ChargePaymentCommand
            {
                ExternalAccountId = order.GetExternalAccountIdOrDefault()!,
                Mid = order.Mid,

                ExternalPaymentInstrumentToken = order.PaymentInstrumentToken,

                Currency = evaluateRequest.Transaction.Currency,
                Amount = amount,

                OrderId = order.Id,
                PayerId = order.PayerId,

                IsCit = order.IsCIT,

                UserDefinedFields = order.GetPaymentProviderUserDefinedFields(),
            };


            workspan.LogEligibility.Information(
                $"Eligibility > Initiating > Charge {chargePaymentCommandParameters}");
            await AddActivityAsync(EligibilityActivities.Charge_Starting, order,
                data: chargePaymentCommandParameters);

            try
            {
                var response = await _grpcPaymentsServiceClient.ChargePaymentAsync(new()
                {
                    Request = JsonConvert.SerializeObject(chargePaymentCommandParameters)
                });

                chargeResponse =
                    JsonConvert.DeserializeObject<ChargePaymentCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                var chargePaymentCommandResponse =
                    await _chargePaymentRequest
                        .RunIdempotentCommandAsync<ChargePaymentCommand, ChargePaymentCommandResponse>(
                            chargePaymentCommandParameters);


                chargeResponse = chargePaymentCommandResponse.Message;
            }

            workspan
                .Tag("ChargeResponse", chargeResponse)
                .LogEligibility.Information(
                    $"Eligibility > Completed > Charge > Response received");

            await AddActivityAsync(EligibilityActivities.Charge_ResultReceived, order,
                data: chargeResponse,
                meta: meta => meta
                    .TransactionReference(chargeResponse.TransactionId)
                    .SetValue("AvsCode", chargeResponse.AvsCode)
                    .SetValue("CvvCode", chargeResponse.CvvCode));

            if (chargeResponse.Success)
            {
                order.PaymentTransactionResult =
                    JsonConvert.SerializeObject(new PaymentTransactionResult(chargeResponse));
            }
            else order.PaymentTransactionResult = null;

            order.AddPaymentTransactionResponse(new PaymentTransactionResponse
            (
                DateTime.UtcNow,
                TransactionType.Charge,
                chargeResponse?.Provider,
                "",
                chargeResponse.ProviderResponseCode,
                chargeResponse.CvvCode,
                chargeResponse.AvsCode,
                chargeResponse.GatewayOrder,
                chargeResponse.InternalResponseCode,
                chargeResponse.InternalResponseMessage,
                chargeResponse.InternalResponseGroup
            ));
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Charge error");
            await AddActivityAsync(EligibilityErrorActivities.Charge_Error, order, data: ex);
        }

        if (chargeResponse?.Success == true)
        {
            workspan.Log.Information("Charge succeeded");
        }
        else
        {
            workspan.Log.Information("Charge failed");
        }

        return chargeResponse;
    }


    public async Task<bool> CanExternalTokenBeChargedAsync(Order order, Merchant merchant, Site site)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Tag("Amount", order.Amount)
            .Tag("Token", order.PaymentInstrumentToken);

        CanChargeExternalTokenCommandResponse canChargeResponse = null;
        try
        {
            var canChargeExternalTokenCommandParameters = new CanChargeExternalTokenCommand
            {
                ExternalPaymentInstrumentToken = order.PaymentInstrumentToken,
                ExternalAccountId = order.GetExternalAccountIdOrDefault()!,
                Mid = order.Mid,

                OrderId = order.Id,
                PayerId = order.PayerId,
            };


            workspan.LogEligibility.Information(
                $"Eligibility > Initiating > CanChargeExternalToken {canChargeExternalTokenCommandParameters}");

            try
            {
                var response = await _grpcPaymentsServiceClient.CanChargeExternalTokenAsync(new()
                {
                    Request = JsonConvert.SerializeObject(canChargeExternalTokenCommandParameters)
                });

                canChargeResponse =
                    JsonConvert.DeserializeObject<CanChargeExternalTokenCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                var canChargeExternalTokenCommandResponse =
                    await _canChargeExternalTokenRequest
                        .RunIdempotentCommandAsync<CanChargeExternalTokenCommand,
                            CanChargeExternalTokenCommandResponse>(
                            canChargeExternalTokenCommandParameters);

                canChargeResponse = canChargeExternalTokenCommandResponse.Message;
            }


            workspan
                .Response(canChargeResponse);

            await AddActivityAsync(EligibilityActivities.CanChargeExternalToken_ResultReceived, order,
                data: canChargeResponse,
                meta: meta => meta
                    .SetValue("CanCharge", canChargeResponse?.CanCharge == true)
            );
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex, "Cannot perform CanChargeExternalToken");
            await AddActivityAsync(EligibilityErrorActivities.CanChargeExternalToken_Error, order, data: ex);
        }

        if (canChargeResponse?.Success == true)
        {
            workspan.Log.Information("CanChargeExternalToken succeeded");
        }
        else
        {
            workspan.Log.Fatal("CanChargeExternalToken failed");
        }


        return canChargeResponse?.CanCharge == true;
    }

    public async Task<Response<CancelExternalSubscriptionCommandResponse>> CancelExternalSubscriptionAsync(
        Order order, Merchant merchant, bool canCancelActiveSubscription)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid)
            .Tag("Amount", order.Amount)
            .LogEnterAndExit();


        var cancelExternalSubscriptionCommandResponse = default(Response<CancelExternalSubscriptionCommandResponse>);

        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            var cancelExternalSubscriptionCommandParameters = new CancelExternalSubscriptionCommand
            {
                CanCancelActiveSubscription = canCancelActiveSubscription,

                ExternalAccountId = order.GetExternalAccountIdOrDefault()!,
                ExternalSubscriptionId = evaluateRequest.Subscription.SubscriptionId,
                Mid = order.Mid,

                PaymentInstrumentExternalToken = order.PaymentInstrumentToken,

                OrderId = order.Id,
            };


            // await AddActivityAsync(EligibilityActivities.Charge_Starting, order,
            //     data: chargePaymentCommandParameters);

            cancelExternalSubscriptionCommandResponse =
                await _cancelExternalSubscriptionRequest
                    .RunIdempotentCommandAsync<CancelExternalSubscriptionCommand,
                        CancelExternalSubscriptionCommandResponse>(
                        cancelExternalSubscriptionCommandParameters);

            var cancelExternalSubscriptionResponse = cancelExternalSubscriptionCommandResponse.Message;

            workspan
                .Response(cancelExternalSubscriptionResponse)
                .Log.Information($"Cancel external subscription response");

            // await AddActivityAsync(EligibilityActivities.Charge_ResultReceived, order,
            //     data: cancelExternalSubscriptionCommandResponse);
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex, "Cannot cancel external subscription");
            // await AddActivityAsync(EligibilityErrorActivities.Charge_Error, order, data: ex);
        }

        if (cancelExternalSubscriptionCommandResponse?.Message.Success == true)
        {
            workspan.Log.Information("Cancel external subscription succeeded");
        }
        else
        {
            workspan
                .Log.Information("Cancel external subscription failed");
        }

        return cancelExternalSubscriptionCommandResponse;
    }

    public async Task<Response<MarkInvoiceAsPaidOutOfBandCommandResponse>> MarkInvoiceAsPaidOutOfBandAsync(
        string invoiceId, Order order, Merchant merchant)
    {
        using var workspan = Workspan.Start<PaymentsService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid)
            .Baggage("OrderId", order.Id)
            .Tag("Amount", order.Amount)
            .LogEnterAndExit();


        var markInvoiceAsPaidOutOfBandCommandResponse = default(Response<MarkInvoiceAsPaidOutOfBandCommandResponse>);

        try
        {
            EvaluateRequest evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            var markInvoiceAsPaidOutOfBandCommandParameters = new MarkInvoiceAsPaidOutOfBandCommand
            {
                InvoiceId = invoiceId,
                ExternalAccountId = order.GetExternalAccountIdOrDefault()!,

                Mid = order.Mid,
                OrderId = order.Id,
            };

            markInvoiceAsPaidOutOfBandCommandResponse =
                await _markInvoiceAsPaidOutOfBandRequest.RunIdempotentCommandAsync<MarkInvoiceAsPaidOutOfBandCommand,
                    MarkInvoiceAsPaidOutOfBandCommandResponse>(markInvoiceAsPaidOutOfBandCommandParameters);

            var markInvoiceAsPaidOutOfBandResponse = markInvoiceAsPaidOutOfBandCommandResponse.Message;

            workspan
                .Response(markInvoiceAsPaidOutOfBandResponse)
                .Log.Information($"Mark invoice as paid out of band response");

            // await AddActivityAsync(EligibilityActivities.Charge_ResultReceived, order,
            //     data: cancelExternalSubscriptionCommandResponse);
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex, "Cannot mark invoice as paid out of band");
            // await AddActivityAsync(EligibilityErrorActivities.Charge_Error, order, data: ex);
        }

        if (markInvoiceAsPaidOutOfBandCommandResponse?.Message.Success == true)
        {
            workspan.Log.Information("Mark invoice as paid out of band succeeded");
        }
        else
        {
            workspan
                //.Tag("ResponseMessage", markInvoiceAsPaidOutOfBandCommandResponse?.Message.ResponseMessage)
                .Log.Information("Mark invoice as paid out of band failed");
        }

        return markInvoiceAsPaidOutOfBandCommandResponse;
    }

    #endregion
}