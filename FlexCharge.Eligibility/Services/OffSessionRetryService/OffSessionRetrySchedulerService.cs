using System;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Logging;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DistributedLock;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.OrdersRecyclingEngine;
using FlexCharge.Eligibility.Services.ActivityService;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.Orders.OrderStates;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.Eligibility.Services.RecyclingEngineService;
using FlexCharge.Eligibility.Workflows.Tracing;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.OffSessionRetryService;

class OffSessionRetrySchedulerService : IOffSessionRetrySchedulerService
{
    const int RE_SCEDULE_IF_NOT_RETRIED_AFTER_DAYS = 1;

    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IDistributedLockService _distributedLockService;
    private readonly IOrderStateMachine _orderStateMachine;

    public OffSessionRetrySchedulerService(
        PostgreSQLDbContext dbContext,
        IPublishEndpoint publisher,
        IActivityService activityService,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService,
        IOrderStateMachine orderStateMachine)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _activityService = activityService;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        _serviceScopeFactory = serviceScopeFactory;
        _distributedLockService = distributedLockService;
        _orderStateMachine = orderStateMachine;
    }


    public void StartProcessing(Guid? mid)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>();

        _backgroundWorkerCommandQueue.Enqueue(new ExecuteOffSessionRetryStrategiesCommand(mid));
    }

    public async Task ProcessRetryableOrdersAsync(Guid? mid)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .LogEnterAndExit();

        var utcNow = DateTime.UtcNow;

        //Compensation for the case order was scheduled but not retried for some reason
        var reScheduleNotRetriedOrderCutoffTime = utcNow.AddDays(-RE_SCEDULE_IF_NOT_RETRIED_AFTER_DAYS);

        // TODO: Change logic to optimize query - it requires too much indexes and this leads to slower order creation/update
        // Get all orders that can be executed during off-session retry
        var notEligibleRetryableOrdersQuery = _dbContext.Orders
            .Where(x =>
                    x.IsCIT == false &&
                    // !!! do not use x.StopRetries == false, because it will not work with null values !!!
                    x.StopRetries != true &&
                    (x.RetryScheduledTime == null || x.RetryScheduledTime < reScheduleNotRetriedOrderCutoffTime) &&
                    (
                        x.State == nameof(OrderState.ORDER_INITIATED) ||
                        x.State == nameof(OrderState.NOT_ELIGIBLE) ||
                        x.State == nameof(OrderState.CONDITIONAL_CONSUMER_INTERACTION)
                    ) &&
                    utcNow < x.ExpiryDate.Value &&
                    (x.IsGhostMode == false ||
                     x.EvaluationCount == 0) // we don't want to retry ghost mode offers more than once
            );

        // Filter by mid if provided
        if (mid != null)
        {
            notEligibleRetryableOrdersQuery = notEligibleRetryableOrdersQuery
                .Where(x => x.Mid == mid);
        }

        var retryableOrderIdsQuery = notEligibleRetryableOrdersQuery
            .AsNoTracking()
            .Select(x => x.Id);

        // var retryableOrdersWithMerchantsQuery = notEligibleRetryableOrdersQuery
        //     .Join(_dbContext.Merchants,
        //         order => order.Mid,
        //         merchant => merchant.Mid,
        //         (order, merchant) => new {Order = order, Merchant = merchant});

        int ordersProcessed = 0;
        //Using streaming instead of buffering all results in memory
        //See: https://learn.microsoft.com/en-us/ef/core/performance/efficient-querying#buffering-and-streaming
        await foreach (var orderIdToRetry in retryableOrderIdsQuery.AsAsyncEnumerable())
        {
            await EnqueueOrderRetrySchedulingAsync(mid, orderIdToRetry);

            ordersProcessed++;
        }

        workspan.Log.Information("RETRY SCHEDULER: Processed orders: {Count}", ordersProcessed);
    }

    #region Schedule Retry

    public async Task EnqueueOrderRetrySchedulingAsync(Guid? mid, Guid orderId)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId);

        try
        {
            workspan.Log.Information("Enqueueing order retry scheduling");

            await _publisher.Publish(new ScheduleOrderRetryEvent(orderId));
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_Error,
                data: e,
                set: set => set
                    .TenantId(mid)
                    .CorrelationId(orderId));
        }
    }

    public async Task ScheduleOrderRetryAsync(Guid orderId)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", orderId);

        try
        {
            // Ensures that if lock is acquired, it will be released 
            await using var orderLock = new OrderEvaluationDistributedLock();
            await orderLock.AcquireOrderLock(orderId, null, _distributedLockService, _activityService,
                lockDuration: TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(1), TimeSpan.FromSeconds(3), 20);

            var orderAndMerchant = await _dbContext.Orders
                .Where(x => x.Id == orderId)
                .Join(_dbContext.Merchants,
                    order => order.Mid,
                    merchant => merchant.Mid,
                    (order, merchant) => new {Order = order, Merchant = merchant})
                .SingleAsync();

            await ScheduleOrderRetryAsync(orderLock, orderAndMerchant.Merchant, orderAndMerchant.Order);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_Error,
                data: e,
                set: set => set
                    .CorrelationId(orderId));

            throw;
        }
    }

    public async Task ScheduleOrderRetryAsync(OrderEvaluationDistributedLock orderLock, Merchant merchant,
        Order orderToRetry)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", orderToRetry.Id)
            .LogEnterAndExit();

        // Can be acquired here for the same order event if it was acquired in a calling method 
        await orderLock.AcquireOrderLock(orderToRetry.Id, orderToRetry.Mid, _distributedLockService, _activityService,
            lockDuration: TimeSpan.FromMinutes(1),
            TimeSpan.FromMinutes(1), TimeSpan.FromSeconds(3), 20);

        // Not required anymore - not used in a foreach loop
        // using var serviceScope = _serviceScopeFactory.CreateScope();
        // // Scoped DbContext is required to avoid problems with foreach loop in the calling method (can't update entity in foreach loop)
        // using var postgresDbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
        //
        // postgresDbContext.Orders.Update(orderToRetry);

        if (orderToRetry.ExpiryDate is null)
            throw new FlexChargeException("ExpiryDate is null");

        if (orderToRetry.RetryScheduledTime != null)
        {
            var reScheduleNotRetriedOrderCutoffTime = DateTime.UtcNow.AddDays(-RE_SCEDULE_IF_NOT_RETRIED_AFTER_DAYS);

            // Order was scheduled but not retried for some reason? -> Reschedule
            if (orderToRetry.RetryScheduledTime < reScheduleNotRetriedOrderCutoffTime)
            {
                workspan.Log.Information("Order was scheduled but not retried - can be re-scheduled");
            }
            else
            {
                throw new FlexChargeException("Order retry already scheduled");
            }
        }

        if (!orderToRetry.CanBeRetriedUntilExpired())
            throw new FlexChargeException("Order cannot be retried");

        //var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(offerToRetry.Payload);


        // call Rule Engine to decide if we should retry now
        var retryInstructions = await GetRetrySchedulingInstructionsAsync(merchant, orderToRetry);

        if (retryInstructions.StopRetries)
        {
            await StopOrderRetriesAsync(orderToRetry,
                saveChangesToDatabase: false); // we need to save changes in local db context
            await _dbContext.SaveChangesAsync();
        }
        else if (retryInstructions.ExecuteRetry)
        {
            #region Schedule retry

            var utcNow = DateTime.UtcNow;

            DateTime retryScheduledTime;
            if (retryInstructions.ImmediateRetry)
            {
                retryScheduledTime = utcNow;
            }
            else
            {
                var retryTimeForOrder =
                    GetRetryTimeForOrder(orderToRetry, utcNow, retryInstructions.RecycleProcessingWindow);

                if (retryTimeForOrder.OrderExpiresBeforeRetry)
                {
                    #region Observability

                    workspan.Log.Information("Order expires before retry");

                    await _activityService.CreateActivityAsync(
                        OffSessionRetryActivities.OffSessionRetry_RuleEngineResult_DoNothing,
                        set: set => set.TenantId(orderToRetry.Mid).CorrelationId(orderToRetry.Id));

                    #endregion

                    return; //!!!
                }
                else
                {
                    retryScheduledTime = retryTimeForOrder.ScheduleTime.Value;
                }
            }

            var delay = retryScheduledTime - utcNow;

            orderToRetry.RetryScheduledTime = retryScheduledTime;
            await _dbContext.SaveChangesAsync();

            workspan.Log.Information("Scheduling retry at {RetryScheduledTime}", retryScheduledTime);

            // Retry will be initiated by Hangfire delayed job
            await _publisher.Publish(new ScheduleEvent
            {
                EventRecipient = IOffSessionRetrySchedulerService.RetryJobName,
                ScheduledJobId = orderToRetry.Id,
                Type = ScheduleEvent.ScheduleType.Delayed,
                Delay = delay,
            });

            #region Observability

            await _activityService.CreateActivityAsync(OffSessionRetryActivities.OffSessionRetry_RetryScheduled,
                set: set => set.TenantId(orderToRetry.Mid).CorrelationId(orderToRetry.Id)
                    .Meta(meta => meta.SetValue("ScheduledTime", retryScheduledTime.ToString("s"))));

            #endregion

            #endregion
        }
    }

    public static (bool OrderExpiresBeforeRetry, DateTime? ScheduleTime) GetRetryTimeForOrder(Order offerToRetry,
        DateTime utcNow,
        RecycleProcessingWindow recycleProcessingWindow)
    {
        DateTime retryStartTime;
        DateTime retryEndTime;

        // If less then minimumRetryTimeLeft in the current retry window - retry in the next window
        TimeSpan minimumRetryTimeLeft = TimeSpan.FromMinutes(5);

        // RecycleProcessingWindow is within the same day?
        if (recycleProcessingWindow.End > recycleProcessingWindow.Start)
        {
            retryStartTime = utcNow.Date + recycleProcessingWindow.Start;
            retryEndTime = utcNow.Date + recycleProcessingWindow.End;
        }
        else // RecycleProcessingWindow is over midnight?
        {
            retryStartTime = utcNow.Date.AddDays(-1) + recycleProcessingWindow.Start;
            retryEndTime = utcNow.Date + recycleProcessingWindow.End;
        }

        if (retryEndTime <= utcNow + minimumRetryTimeLeft)
        {
            // Not enough time left in the day to retry
            // Schedule for the next day
            retryStartTime = retryStartTime.AddDays(1);
            retryEndTime = retryEndTime.AddDays(1);
        }

        if (retryStartTime < utcNow)
        {
            retryStartTime = utcNow;
        }

        // Schedule retry before expiry and be sure that it will have enough time to be executed
        var safeExpiryTime = offerToRetry.ExpiryDate!.Value.AddHours(-1);
        if (safeExpiryTime <= retryStartTime)
        {
            // Retry time is after expiry time -> cannot schedule retry
            return (true, null); //!!!
        }

        if (safeExpiryTime <= retryEndTime)
        {
            retryEndTime = safeExpiryTime;
        }

        // Calculate random retry offset in seconds from retryStartTime
        int secondsTillRetryWindowEnd = (int) (retryEndTime - retryStartTime).TotalSeconds;

        if (secondsTillRetryWindowEnd > 0)
        {
            var retryTime = retryStartTime.AddSeconds(RandomNumberGenerator.GetInt32(0, secondsTillRetryWindowEnd));

            if (retryTime > retryEndTime)
            {
                throw new Exception($"Retry time is incorrect: {retryTime}. RetryEndTime: {retryEndTime}.");
            }

            return (false, retryTime);
        }
        else return (false, utcNow); //expiry time is approaching, so we should retry immediately
    }

    #endregion


    public async Task<RetryInstructions> GetRetrySchedulingInstructionsAsync(Entities.Merchant merchant, Order order)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>();

        if (order.State == nameof(OrderState.ORDER_APPROVED))
        {
            // We don't want to retry offers that are already approved
            workspan.Log.Warning("Skipping retry because order is already approved");
            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_OrderAlreadyApproved, order);

            return RetryInstructions.CreateDoNothingInstructions();
        }
        else
        {
            #region Call Rule Engine to get retry instructions

            var repaymentInstructions = await GetRepaymentInstructionsFromWorkflowAsync(merchant, order);

            workspan
                .Baggage("RepaymentInstructions", repaymentInstructions);

            if (repaymentInstructions.StopRepayments)
            {
                #region Observability

                workspan.Log.Information("RULE ENGINE: Stop repayments");
                await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_StoppingRepayments_RuleEngine, order,
                    meta: meta =>
                        meta.SetValue("Reason",
                            repaymentInstructions.RecyclingInstructions?.Errors != null
                                ? string.Join(',', repaymentInstructions.RecyclingInstructions.Errors)
                                : ""));

                #endregion

                return RetryInstructions.CreateStopRetriesInstructions();
            }
            else if (repaymentInstructions.RecyclingInstructions != null)
            {
                //for now, we only have one strategy, so we can just execute it

                #region Creating Activity

                if (order.State == nameof(OrderState.ORDER_INITIATED)) // try to evaluate order if it's first time
                {
                    workspan.Log.Information("Scheduled initial evaluation");
                    await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_InitialEvaluationScheduled, order);
                }
                else
                {
                    await AddActivityAsync(
                        OffSessionRetryActivities.OffSessionRetry_RuleEngineResult_ExecuteStrategy, order /*,
                        subcategory: repaymentInstructions.StrategyToExecute,
                        meta: meta => meta
                            .SetValue("Strategy", repaymentInstructions.StrategyToExecute)*/);

                    // workspan.Log.Information("RULE ENGINE: Execute {StrategyToExecute}",
                    //     repaymentInstructions.StrategyToExecute);
                }

                #endregion

                if (repaymentInstructions.ImmediateRetry)
                {
                    workspan.Log.Information("Executing immediate retry");

                    return RetryInstructions.CreateExecuteImmediateRetryInstructions();
                }
                else
                {
                    workspan.Log.Information("Scheduling retry");

                    return RetryInstructions.CreateScheduleRetryInstructions(repaymentInstructions.RecyclingInstructions
                        .RecycleWindow);
                }
            }
            else
            {
                #region Creating Activity

                await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_RuleEngineResult_DoNothing, order);

                #endregion

                workspan.Log.Information("RULE ENGINE: Do nothing");

                return RetryInstructions.CreateDoNothingInstructions();
            }

            #endregion
        }
    }

    public async Task<RetryInstructions> GetRetryExecutionInstructionsAsync(Order order)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>();

        if (order.State == nameof(OrderState.ORDER_APPROVED))
        {
            // We don't want to retry offers that are already approved
            workspan.Log.Warning("Skipping retry because order is already approved");
            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_OrderAlreadyApproved, order);

            return RetryInstructions.CreateDoNothingInstructions();
        }

        return RetryInstructions.CreateExecuteImmediateRetryInstructions();
    }


    public async Task StopOrderRetriesAsync(Order order,
        string? cancelledByUserName = null, Guid? cancelledByUserId = null, string? cancelledByUserGroups = null,
        bool saveChangesToDatabase = true)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", order.Id);

        using var serviceScope = _serviceScopeFactory.CreateScope();

        // Should be scoped to not to share database contexts between calls for different orders in a loop!!!
        var orderStateMachine = serviceScope.ServiceProvider.GetRequiredService<IOrderStateMachine>();

        // Not saving to database to prevent saving changes in scoped db context!!!
        // We need to save changes in _dbContext
        await orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleCancelled, false);

        _dbContext.Orders.Update(order);

        if (saveChangesToDatabase)
        {
            await _dbContext.SaveChangesAsync();
        }

        await FireOrderCancelledEvent(order, cancelledByUserName, cancelledByUserId, cancelledByUserGroups);
    }


    private async Task<(bool StopRepayments, bool ImmediateRetry, RecyclingInstructions? RecyclingInstructions)>
        GetRepaymentInstructionsFromWorkflowAsync(Entities.Merchant merchant, Order order)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>();

        // lastTransactionResponse can be null if Authorization/Sale failed because of some technical problem 
        var lastTransactionResponse = order.GetLastTransactionResultOrDefault();

        int daysSinceOrderFirstPlaced = (int) Math.Truncate((DateTime.UtcNow.Date - order.CreatedOn.Date).TotalDays);

        // int? mitExpiryIntervalInHours = null;
        // if (order.IsMIT())
        // {
        //     mitExpiryIntervalInHours =
        //         (int) Math.Truncate((order.ExpiryDate.Value.Date - order.CreatedOn.Date).TotalHours);
        // }

        Stage4Request stage4Request = new()
        {
            IsMIT = order.IsMIT(),
            FirstDeclineDateDaysOld = (decimal) daysSinceOrderFirstPlaced,
            ResponseCodeSource = lastTransactionResponse?.ResponseCodeGateway ?? "",
            ResponseCode = lastTransactionResponse?.ResponseCode ?? "",
        };

        #region Observability

        await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_RuleEngine_Stage4_Starting, order,
            data: stage4Request);

        #endregion

        RecyclingInstructions recyclingInstructions = null;

        try
        {
            using var serviceScope = _serviceScopeFactory.CreateScope();


            if (!order.IsMIT())
                throw new FlexChargeException("Order is not MIT");

            workspan.Log
                .Information("Order is MIT");

            // Should be scoped to not to share database contexts between calls for different orders in a loop
            var recyclingEngineService = serviceScope.ServiceProvider.GetRequiredService<IRecyclingEngineService>();

            var workflowExecutionResult =
                await recyclingEngineService.CreateAndRunRecyclingWorkflowAsync(merchant, order);

            recyclingInstructions = workflowExecutionResult.WorkflowContext.ExecutionResult;

            if (recyclingInstructions == null)
            {
                throw new FlexChargeException("Stage 4 result is null");
            }

            #region Observability

            workspan
                .Baggage("RecyclingInstructions", recyclingInstructions)
                .Log.Information("Received recycling instructions");

            await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_RuleEngine_Stage4_ResponseReceived, order,
                data: recyclingInstructions,
                meta: meta =>
                    meta
                        .SetValue("WorkflowName", workflowExecutionResult.WorkflowDescription.Name)
                        .SetValue("WorkflowId", workflowExecutionResult.WorkflowDescription.Id));

            #endregion
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Unable to run recycling workflow");
            throw;
        }


        if (recyclingInstructions.StopRepayments)
        {
            return (true, false, null);
        }
        else if (recyclingInstructions.ChargeRetryIntervalInDays == 0)
        {
            // Skipping retry
            return (false, false, null);
        }
        else if (recyclingInstructions.ChargeRetryIntervalInDays < 0)
        {
            throw new Exception("PCSM Stage 4 error: ChargeRetryIntervalInDays can't be less than 0");
        }
        else
        {
            var tryToExecuteToday =
                //daysSinceOrderFirstPlaced > 0 && //do not execute repayment strategies on the day of order placement
                recyclingInstructions.ChargeRetryIntervalInDays > 0 && // if <= 0 -> do nothing
                daysSinceOrderFirstPlaced % recyclingInstructions.ChargeRetryIntervalInDays == 0;

            if (tryToExecuteToday)
            {
                return (false, false, recyclingInstructions);
            }
            else
            {
                // Skipping retry
                return (false, false, null);
            }
        }
    }


    async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order offer,
        string eventName = null,
        //object meta = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await _activityService.AddActivityAsync(activityNameEnum, offer, eventName, data, subcategory, meta);
    }

    public async Task StopMitOrderRetryAsync(Guid orderId,
        bool superAdminCall, Guid callerMid,
        string? cancelledByUserName, Guid? cancelledByUserId, string? cancelledByUserGroups)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", orderId)
            .Baggage("Mid", callerMid);

        try
        {
            // Preventing possible parallel order retry scheduling and evaluation or more than one parallel retry scheduling
            await using var orderLock = new OrderEvaluationDistributedLock();

            await orderLock.AcquireOrderLock(orderId, callerMid, _distributedLockService, _activityService,
                lockDuration: TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(1));


            var order = await _dbContext.Orders.FirstOrDefaultAsync(x => x.Id == orderId);

            if (order is null)
                throw new FlexChargeException("Unable to find order");

            workspan.Baggage("Mid", order.Mid);

            if (!order.IsMIT())
                throw new FlexChargeException("Order is not MIT");

            if (!superAdminCall && order.Mid != callerMid)
                throw new FlexChargeException("Merchant cannot stop retries for other merchant's order");


            if (!order.IsApproved() && (order.CanBeRetriedUntilExpired() || order.IsOnHold()))
            {
                #region Observability

                workspan.Log.Information("Stopping order retries");
                await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_StoppingRepayments_OrderCancelled,
                    order);

                #endregion

                await StopOrderRetriesAsync(order, cancelledByUserName, cancelledByUserId, cancelledByUserGroups);
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Unable to stop order retries");
            throw;
        }
    }

    private async Task FireOrderCancelledEvent(Order order, string? cancelledByUserName, Guid? cancelledByUserId,
        string? cancelledByUserGroups)
    {
        try
        {
            await _publisher.Publish(new OrderCancelledEvent()
            {
                Mid = order.Mid,
                OrderId = order.Id,
                ExternalOrderId = order.ExternalOrderReference,

                CancelledByUserName = cancelledByUserName,
                CancelledByUserId = cancelledByUserId,
                CancelledByUserGroups = cancelledByUserGroups,
            });
        }
        catch (Exception e)
        {
            Workspan.Current?.RecordFatalException(e);
            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_Error, order, data: e);
        }
    }

    public async Task ResumeMitOnHoldOrderAsync(Guid orderId)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        try
        {
            // Preventing possible parallel actions on this order
            await using var orderLock = new OrderEvaluationDistributedLock();
            await orderLock.AcquireOrderLock(orderId, null, _distributedLockService, _activityService,
                lockDuration: TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(1), TimeSpan.FromSeconds(3), 20);

            var order = await _dbContext.Orders.FirstOrDefaultAsync(x => x.Id == orderId);

            if (order is null)
                throw new FlexChargeException("Unable to find order");

            workspan.Baggage("Mid", order.Mid);

            if (!order.IsMIT())
                throw new FlexChargeException("Order is not MIT");

            if (order.IsOnHold())
            {
                #region Observability

                workspan.Log.Information("Resuming on hold order");
                await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_ResumingOnHoldOrder,
                    order);

                #endregion

                await ResumeMITOnHoldOrderAsync(order);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Unable to resume on hold order");
            throw;
        }
    }

    private async Task ResumeMITOnHoldOrderAsync(Order order)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", order.Id);

        using var serviceScope = _serviceScopeFactory.CreateScope();

        if (!order.IsMIT())
        {
            workspan.Log.Fatal("Order is not MIT");

            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_ResumingOnHoldOrder_Error, order,
                data: "Order is not MIT");
            return;
        }

        if (!order.IsOnHold())
        {
            workspan.Log.Fatal("Order is not on hold");

            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_ResumingOnHoldOrder_Error, order,
                data: "Order is not on hold");
            return;
        }

        if (order.IsExpiredMIT())
        {
            workspan.Log.Fatal("Order is expired");

            await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_ResumingOnHoldOrder_Error, order,
                data: "Order is expired");
            return;
        }

        var orderStateMachine = serviceScope.ServiceProvider.GetRequiredService<IOrderStateMachine>();

        await orderStateMachine.ResumeOrderAsync(order, true);

        workspan.Log.Information("Order on hold resumed");

        await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_ResumingOnHoldOrder_Succeeded, order);
    }
}