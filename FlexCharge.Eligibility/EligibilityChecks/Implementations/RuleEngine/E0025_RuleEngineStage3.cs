using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Services.PCSMServices;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;

public class E0025_RuleEngineStage3 : RuleEngineStage3_ActionBased_Base
{
    public override bool IsProductionBlock => true;

    protected override Stage3Response RunPostAuthCitChecks(CheckResults checkResults, Stage3Request request)
    {
        var workspan = Workspan.Start<E0025_RuleEngineStage3>();


        if (checkResults.AchPassed == true)
        {
            workspan.Log.Information("ACH passed");
            return RuleEngineResponse.Approve();
        }

        if (!checkResults.AuthPassed)
        {
            // Shouldn't be there as this method is called only on authorized payments
            workspan.Log.Fatal("Cannot proceed with post auth CIT checks: authorization failed");

            return RuleEngineResponse.Decline();
        }

        if (checkResults.ThreeDsWithLiabilityShiftPassed || checkResults.ThreeDsInformationalOnlyPassed)
        {
            return RuleEngineResponse.Approve();
        }

        if (checkResults.FraudScore <= 59)
        {
            return RuleEngineResponse.Approve();
        }

        if (checkResults.FraudScore > 59 &&
            (checkResults.AvsResponse is AvsResponseType.FullMatch or AvsResponseType.PartialMatch))
        {
            return RuleEngineResponse.CureSet("C0002", "C1001", "NE");
        }

        if (checkResults.FraudScore >= 59 &&
            checkResults.CvvResponse != CvvResponseType.Match)
        {
            return RuleEngineResponse.CureSet("C0007", "C0002", "C1001", "NE");
        }

        // if (authPassed &&
        //     cvvResponse == CvvResponseType.Match &&
        //     avsResponse == AvsResponseType.FullMatch &&
        //     fraudScore <= 59)
        // {
        //     return RuleEngineResponse.EligibilityStage3Passed();
        // }

        // if (authPassed && 
        //     threeDsPassed)
        // {
        //     return RuleEngineResponse.EligibilityStage3Passed();
        // }


        workspan.Log.Information("Post auth CIT checks are not passed");
        return RuleEngineResponse.Decline();
    }

    protected override Stage3Response RunPostAuthMitChecks(CheckResults checkResults, Stage3Request request)
    {
        var workspan = Workspan.Start<E0025_RuleEngineStage3>();

        if (checkResults.AchPassed == true)
        {
            workspan.Log.Information("ACH passed");
            return RuleEngineResponse.Approve();
        }

        if (!checkResults.AuthPassed)
        {
            // Shouldn't be there as this method is called only on authorized payments
            workspan.Log.Fatal("Cannot proceed with post auth MIT checks: authorization failed");

            return RuleEngineResponse.Decline();
        }


        if (request.TransmitApprovedTransactionCount > 0)
        {
            return RuleEngineResponse.Approve();
        }

        // 'InputValues_LDS.ResponseCodeNormalized' = "0" // Approved
        //  AND ('InputValues_LDS.AVSResultCode' = "M" OR 
        // 'InputValues_LDS.AVSResultCode' = "X" OR
        // 'InputValues_LDS.AVSResultCode' = "Y" OR
        // 'InputValues_LDS.AVSResultCode' = "D") //OK "X";"Y";"D";"M"
        if (checkResults.AvsResponse is AvsResponseType.FullMatch or AvsResponseType.PartialMatch)
        {
            return RuleEngineResponse.Approve();
        }

        // 'InputValues_LDS.ResponseCodeNormalized' = "0" // Approved
        // AND
        // 'InputValues_LDS.CVVResultCode' = "M"
        if (checkResults.CvvResponse == CvvResponseType.Match)
        {
            return RuleEngineResponse.Approve();
        }

        // 'InputValues_LDS.ResponseCodeNormalized' = "0" // Approved
        // AND
        //     ('InputValues_LDS.CAVVResultCode' = "Y" OR 
        // 'InputValues_LDS.RebillingObj.External3DSPresent' = 1) //TODO check this
        if (checkResults.ThreeDsWithLiabilityShiftPassed || checkResults.ThreeDsInformationalOnlyPassed)
        {
            return RuleEngineResponse.Approve();
        }

        // 'InputValues_LDS.ResponseCodeNormalized' = "0" // Approved
        // AND
        //     ('InputValues_LDS.Checks.SeonFraudFlag' = 0 AND 
        // 'InputValues_LDS.Checks.KountFraudFlag' = 0)
        if (checkResults.FraudScore <= 59)
        {
            return RuleEngineResponse.Approve();
        }

        workspan.Log.Information("Post auth MIT checks are not passed");
        return RuleEngineResponse.Decline();
    }
}