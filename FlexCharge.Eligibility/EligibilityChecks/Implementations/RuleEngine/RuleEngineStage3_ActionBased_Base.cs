using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures.Implementations;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Action = FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies.Action;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;
using TransactionType = FlexCharge.Eligibility.Entities.Enums.TransactionType;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;

public abstract class RuleEngineStage3_ActionBased_Base : EligibilityCheckBase
{
    static ProviderNormalizedResponseToActionMap _providerNormalizedResponseToActionMap = new();

    protected IntegerValue _CIT_MaxAuthorizationAttempts_IntegerValue;

    protected abstract Stage3Response RunPostAuthCitChecks(CheckResults checkResults, Stage3Request request);
    protected abstract Stage3Response RunPostAuthMitChecks(CheckResults checkResults, Stage3Request request);

    #region CheckResults class

    protected record CheckResults(
        bool AuthPassed,
        bool AchPassed,
        bool ThreeDsWithLiabilityShiftPassed,
        bool ThreeDsInformationalOnlyPassed,
        CvvResponseType CvvResponse,
        AvsResponseType AvsResponse,
        int FraudScore
    );

    #endregion

    public RuleEngineStage3_ActionBased_Base()
    {
        _CIT_MaxAuthorizationAttempts_IntegerValue = AddExternalControl(
            new IntegerValue(new Guid("bc75ee28-40c0-4fe1-8276-53d6ec6e9d8a"),
                "CIT Max Authorization Attempts",
                "Sets the maximum number of authorization attempts for CIT.",
                ExternalControls.CascadeStrategyGroup.Name,
                ExternalControls.CascadeStrategyGroup.CIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E0025_RuleEngineStage3),
                defaultValue: 3, minValue: 1, maxValue: 5));
    }

    protected override async Task ExecuteBlockAsync()
    {
        Context.Stage3Result = await InvokeRuleEngineStage3Async(
            Context.FullAuthorizationOrSaleResult,
            Order, Merchant, OrderPayload,
            Context.Stage2Result, Context.FraudResults,
            Context.ZeroVerificationResult, Context.BureauResults);

        Log($"Stage 3 result {JsonConvert.SerializeObject(Context.Stage3Result)}");

        if (Context.Stage3Result.OrderState.IsNotEligible())
            throw new NotEligibleException();

        if (Context.Stage3Result.CureSet?.Any() == true)
        {
            Log($"Stage 3 cure sequence: {JsonConvert.SerializeObject(Context.Stage3Result.CureSet)}");
        }
    }

    private async Task<Stage3Response> InvokeRuleEngineStage3Async(
        PaymentTransactionResult fullAuthorizationResult, Order order, Merchant merchant,
        EvaluateRequest orderPayload, Stage2Response stage2Result, IDictionary<string, string> fraudResults,
        VerifyPaymentResult zeroVerificationResult, IDictionary<string, int> bureauResults)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>()
            .Tag("FullAuthorizationResult", fullAuthorizationResult)
            .Tag("Order", order)
            .Tag("OrderPayload", orderPayload)
            .Tag("Stage2Result", stage2Result)
            .Tag("FraudResults", fraudResults)
            .Tag("ZeroVerificationResult", zeroVerificationResult)
            .Tag("BureauResults", bureauResults);


        workspan.LogEligibility.Information($"Calling eligibility stage 3");

        bool isFullyAuthorized = fullAuthorizationResult != null ? fullAuthorizationResult.Success : false;

        var lastPaymentOperationResponse = order.GetLastTransactionResultOrDefault();

        // Define decline reason map gateway response to our internal response
        // Define bureau call strategy
        var stage3Request = new Stage3Request()
        {
            FraudProviderResponses = fraudResults,

            OrderExpiryDateTime = Order.ExpiryDate,
            ResponseCodeSource = lastPaymentOperationResponse.ResponseCodeGateway,
            ResponseCode = lastPaymentOperationResponse.ResponseCode,
            IsFullyAuthorized = isFullyAuthorized,

            ZeroVerificationResponseCode = zeroVerificationResult != null
                ? (zeroVerificationResult.IsVerified ? "000" : "300")
                : null,

            BureauProviderResponses =
                bureauResults ?? new Dictionary<string, int>(), // need to map responses from step 2

            IsCustomerInitiatedTransaction = orderPayload.IsMIT != true,

            IsRecurringPayments = orderPayload.IsRecurring == true,

            CvvResultCode = lastPaymentOperationResponse.CVV,
            AvsResultCode = lastPaymentOperationResponse.AVS,

            CavvResultCode = GetCavvResultCode(order),
            //CavvResultCode = order.GetCurrentSCAResponse()?.CavvResultCode ?? "",

            LastTransactionType = lastPaymentOperationResponse.TransactionType,
            NormalizedResponseCode = lastPaymentOperationResponse.NormalizedResponseCode,
            NormalizedResponseMessage = lastPaymentOperationResponse.NormalizedResponseMessage,
            NormalizedResponseCodeGroup = lastPaymentOperationResponse.NormalizedResponseCodeGroup,
        };

        #region Observability

        workspan.LogEligibility.Information("Stage 3 request: {Request}", JsonConvert.SerializeObject(stage3Request));

        await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Starting, data: stage3Request);

        #endregion

        // Run the rule engine
        var stage3Response =
            await RuleEngineStage3Async(order, merchant, orderPayload, stage3Request, lastPaymentOperationResponse,
                base.ServiceProvider);

        #region Observability

        await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_ResponseReceived, data: stage3Response);
        await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_DecisionReasonReceived,
            data: stage3Response.ReasonForDecision);

        #endregion

        #region Order/Payment Provider Blocking

        if (stage3Response.BlockPaymentProvider)
        {
            #region Block Payment Provider

            var providerToBlock = stage3Request.ResponseCodeSource;

            workspan.LogEligibility.Information("Payment provider blocked: {ProviderToBlock}", providerToBlock);

            Order.AddPaymentProviderToBlackList(providerToBlock);

            Log($"Payment provider blocked: {providerToBlock}");
            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_BlockPaymentProvider,
                data: providerToBlock);

            #endregion
        }

        if (stage3Response.StopRetries && Order.IsMIT())
        {
            #region Stop Order Retries

            workspan.LogEligibility.Information("MIT order retries stopped");
            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Decline);

            await StopOrderRetriesAsync();

            throw new NotEligibleException();

            #endregion
        }

        #endregion


        // If the order is not eligible, we need to log the reason for the decision
        if (!stage3Response.Success || // request failed (due to some errors)?
            stage3Response.OrderState.IsNotEligible())
        {
            foreach (var responseError in stage3Response.Errors)
            {
                if (responseError.ErrorCode == "general_error")
                {
                    //eligibilityResponse.AddError(responseError.Error, responseError.Key, responseError.ErrorCode,
                    //    responseError.FriendlyError);
                    workspan.LogEligibility.Error("Stage3 error {Error}", responseError.Error);

                    await AddActivityAsync(EligibilityErrorActivities.RuleEngine_Stage3_Error, responseError.Error);
                }
            }

            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Decline);
            throw new NotEligibleException();
        }
        else
        {
            if (Context.IsGhostMode)
            {
                workspan.LogEligibility.Information($"Ghost mode enabled, setting order state to NOT_ELIGIBLE.");
                await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Decline);

                throw new NotEligibleException();
            }
        }

        await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Success);

        #region [Commented] Test code

        // #if DEBUG
//         // REMOVE THIS AFTER TESTING!!!!
//         if (Order.IsMIT())
//         {
//             stage3Response.OrderState = OrderState.CONDITIONAL_INTERNAL;
//             stage3Response.CureSet = new List<string>() {"C0301"};
//         }
// #endif

        #endregion

        return stage3Response;
    }

    public async Task<Stage3Response> RuleEngineStage3Async(Order order, Merchant merchant,
        EvaluateRequest orderPayload,
        Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse,
        IServiceProvider serviceProvider)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        await _providerNormalizedResponseToActionMap.LoadAsync();

        var normalizedResponseCode = lastPaymentOperationResponse.NormalizedResponseCode;

        workspan
            .Baggage("NormalizedResponseCode", normalizedResponseCode);

        if (string.IsNullOrWhiteSpace(normalizedResponseCode) ||
            !_providerNormalizedResponseToActionMap.Map.TryGetValue(normalizedResponseCode, out var action))
        {
            workspan.Log.Fatal("No action found for normalized response code");

            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_NoActionFound, meta: meta => meta
                .SetValue("NormalizedResponseCode", normalizedResponseCode));

            return RuleEngineResponse.Decline("No action found for normalized response code", "NoActionFound",
                "general_error");
        }

        workspan
            .Baggage("Action", action)
            .Baggage("CitAction", action.CitAction)
            .Baggage("MitAction", action.MitAction)
            .Log.Information("Action found for normalized response code");

        if (stage3Request.IsCustomerInitiatedTransaction)
        {
            return ProcessCitTransaction(order, orderPayload, stage3Request, lastPaymentOperationResponse,
                serviceProvider, action);
        }
        else
        {
            return ProcessMitTransaction(order, stage3Request, lastPaymentOperationResponse, action);
        }
    }

    private Stage3Response ProcessMitTransaction(Order order, Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse, Action action)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        switch (action.MitAction)
        {
            case MitAction.Approve:
                return RunPostAuthMitChecks(order, stage3Request, lastPaymentOperationResponse);
            case MitAction.DoNotTryAgain:
                return RuleEngineResponse.DeclineAndStopRetries();
            case MitAction.Cascade:
                switch (action.When)
                {
                    case When.Now:
                        return RuleEngineResponse.CascadeNow_MIT();
                        break;
                    case When.Later:
                        return RuleEngineResponse.CascadeLater_MIT();
                        break;
                    default:
                        throw new FlexChargeException($"Unknown When value: {action.When}");
                }
            // case MitAction.TryAgainLater:
            //     return RuleEngineResponse.TryAgainLater();
            default:
                workspan.Log.Fatal("Unknown MitAction");
                throw new NotEligibleException();
        }
    }

    protected virtual Stage3Response ProcessCitTransaction(Order order, EvaluateRequest orderPayload,
        Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse, IServiceProvider serviceProvider, Action action)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        #region Open banking testing support

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment && order.IsInTerminalOrKioskMode())
        {
            // For Terminal testing use test cards defined in IsTerminalTestCard_Decline method
            // They will be declined on first evaluation, but approved after any user challenge

            var securityCheckService = serviceProvider.GetRequiredService<ISecurityCheckService>();

            if (securityCheckService.IsTerminalTestCard_Decline(
                    orderPayload?.PaymentMethod?.CardBinNumber,
                    orderPayload?.PaymentMethod?.CardLast4Digits) &&
                order.IsAnyUserChallengeExecuted() == false)
            {
                if (orderPayload?.BillingInformation?.FirstName == "OBANK" &&
                    orderPayload?.BillingInformation?.LastName == "TESTER")
                {
                    return RuleEngineResponse.CureSet("C1001");
                }
                else
                {
                    return RuleEngineResponse.CureSet("C2002a");
                }
            }
        }

        #endregion

        switch (action.CitAction)
        {
            case CitAction.Approve:
                return RunPostAuthCitChecks(order, stage3Request, lastPaymentOperationResponse);
            case CitAction.DoNotTryAgain:
                return RuleEngineResponse.Decline();
            case CitAction.Cascade:
                int maxCascadingAttempts = GetExternalControlValue(_CIT_MaxAuthorizationAttempts_IntegerValue);
                return RuleEngineResponse
                    .CascadeNow_CIT(maxCascadingAttempts);
            default:
                workspan.Log.Fatal("Unknown CitAction");
                throw new NotEligibleException();
        }
    }

    protected virtual Stage3Response RunPostAuthCitChecks(Order order, Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        workspan.Log.Information("Running post auth CIT checks");

        try
        {
            var checkResults = GetCheckResults(stage3Request, order, lastPaymentOperationResponse);

            return RunPostAuthCitChecks(checkResults, stage3Request);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            return RuleEngineResponse.Decline("Error in RunPostAuthCitChecks", "RunPostAuthCitChecks",
                "general_error");
        }
    }

    private Stage3Response RunPostAuthMitChecks(Order order, Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse)
    {
        var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        workspan.Log.Information("Running post auth MIT checks");

        try
        {
            var checkResults = GetCheckResults(stage3Request, order, lastPaymentOperationResponse);

            return RunPostAuthMitChecks(checkResults, stage3Request);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            return RuleEngineResponse.Decline("Error in RunPostAuthMitChecks", "RunPostAuthMitChecks",
                "general_error");
        }
    }


    #region Helper Methods

    private static CheckResults GetCheckResults(Stage3Request request, Order order,
        PaymentTransactionResponse lastTransaction)
    {
        var threeDResult = Get3dsResult(order);

        var checksResults = new CheckResults(
            AuthorizationPassed(lastTransaction),
            AchPassed(lastTransaction),
            threeDResult.Authenticated && threeDResult.InformationalOnly == false,
            threeDResult.Authenticated && threeDResult.InformationalOnly == true,
            lastTransaction.GetCvvResponseType(),
            lastTransaction.GetAvsResponseType(),
            CalculateFraudScore(request.FraudProviderResponses)
        );

        return checksResults;
    }


    //calculate fraud score based on seon and kount
    private static int CalculateFraudScore(IDictionary<string, string> fraudResults)
    {
        var kountfraudflag = fraudResults.TryGetValue("KOUNT.Result", out var kountResponse)
            ? int.Parse(kountResponse)
            : -1;

        var seonfraudflag = fraudResults.TryGetValue("SEON.Result", out var seonResponse)
            ? int.Parse(seonResponse)
            : -1;

        if (seonfraudflag == 0 && kountfraudflag == 0)
            return 0;

        if (seonfraudflag == -1 && kountfraudflag == -1)
            return -1;

        if (seonfraudflag == 1 && kountfraudflag == 0)
            return 59;

        if (seonfraudflag == 0 && kountfraudflag == 1)
            return 59;

        if (seonfraudflag == -1 && kountfraudflag == 0)
            return 0;

        if (seonfraudflag == 0 && kountfraudflag == -1)
            return 0;

        if (seonfraudflag == 1 && kountfraudflag == -1)
            return 59;

        if (seonfraudflag == -1 && kountfraudflag == 1)
            return 59;

        return 99;
    }

    private static bool AuthorizationPassed(PaymentTransactionResponse lastTransaction)
    {
        return (lastTransaction.TransactionType == nameof(TransactionType.FullAuthorization) ||
                lastTransaction.TransactionType == nameof(TransactionType.Sale)) &&
               lastTransaction.NormalizedResponseCode == "0";
    }

    private static bool AchPassed(PaymentTransactionResponse lastTransaction)
    {
        return lastTransaction.TransactionType == nameof(TransactionType.AchVerifiedDebit) &&
               lastTransaction.ResponseCode == "100";
    }

    private static (bool Authenticated, bool? InformationalOnly) Get3dsResult(Order order)
    {
        using var workspan = Workspan.Start<RuleEngineStage3_ActionBased_Base>();

        // Is there a successful 3DS result?
        if (order.SCAAuthenticationToken != null)
        {
            var scaResponses = order.GetSCAAuthenticationResponses();
            var lastScaResponse = scaResponses.LastOrDefault();

            if (lastScaResponse != null)
            {
                if (!string.IsNullOrWhiteSpace(lastScaResponse.CAVV))
                {
                    return (true, lastScaResponse.InformationalOnly);
                }
                else
                {
                    workspan.Log.Fatal("SCAAuthenticationToken is present, but CAVV is empty");
                }
            }
            else
            {
                workspan.Log.Fatal("SCAAuthenticationToken is present, but no SCA responses found");
            }
        }

        return (false, null);
    }

    private static bool BureauChecksPassed(IDictionary<string, int> bureauResults)
    {
        var bureauResponseReturned = bureauResults.TryGetValue("EXPERIAN", out var experianResult);

        return bureauResponseReturned && experianResult > 691;
    }

    private string GetCavvResultCode(Order order)
    {
        string cavvResultCode; // Not attempted
        if (!string.IsNullOrWhiteSpace(order.SCAAuthenticationToken))
        {
            cavvResultCode = "Y";
        }
        else if (!string.IsNullOrWhiteSpace(order.SCAChallengeToken))
        {
            cavvResultCode = "C";
        }
        else cavvResultCode = Order.IsSCAAttempted == true ? "N" : "";

        return cavvResultCode;
    }

    #endregion
}