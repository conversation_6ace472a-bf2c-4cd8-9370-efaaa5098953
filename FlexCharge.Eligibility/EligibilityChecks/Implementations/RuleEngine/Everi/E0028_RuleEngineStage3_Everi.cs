using System;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Cures.Implementations;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.PCSMServices;
using Action = FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies.Action;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine.Everi;

public class E0028_RuleEngineStage3_Everi : RuleEngineStage3_ActionBased_Base
{
    public override bool IsProductionBlock => true;

    protected override Stage3Response ProcessCitTransaction(Order order, EvaluateRequest orderPayload,
        Stage3Request stage3Request,
        PaymentTransactionResponse lastPaymentOperationResponse, IServiceProvider serviceProvider, Action action)
    {
        using var workspan = Workspan.Start<E0028_RuleEngineStage3_Everi>();

        if (action.CitAction == CitAction.Cascade)
        {
            int maxCascadingAttempts = GetExternalControlValue(_CIT_MaxAuthorizationAttempts_IntegerValue);

            return RuleEngineResponse
                    .CureSet()
                    .Then_CureSet(nameof(C0007_AskForCVV)) // retry will be triggered by the cure
                    .Then_CascadeNow_CIT(maxCascadingAttempts - 1) // one retry is already done by AskForCVV cure
                ;
        }
        else
        {
            return base.ProcessCitTransaction(order, orderPayload, stage3Request, lastPaymentOperationResponse,
                serviceProvider, action);
        }
    }

    protected override Stage3Response RunPostAuthCitChecks(CheckResults checkResults, Stage3Request request)
    {
        var workspan = Workspan.Start<E0025_RuleEngineStage3>();


        if (checkResults.AchPassed == true)
        {
            workspan.Log.Information("ACH passed");
            return RuleEngineResponse.Approve();
        }

        if (!checkResults.AuthPassed)
        {
            // Shouldn't be there as this method is called only on authorized payments
            workspan.Log.Fatal("Cannot proceed with post auth CIT checks: authorization failed");

            return RuleEngineResponse.Decline();
        }

        if (checkResults.ThreeDsWithLiabilityShiftPassed || checkResults.ThreeDsInformationalOnlyPassed)
        {
            return RuleEngineResponse.Approve();
        }

        if (checkResults.FraudScore <= 59)
        {
            return RuleEngineResponse.Approve();
        }

        if (checkResults.FraudScore > 59 &&
            (checkResults.AvsResponse is AvsResponseType.FullMatch or AvsResponseType.PartialMatch))
        {
            return RuleEngineResponse.CureSet("C0002", "C1001", "NE");
        }

        if (checkResults.FraudScore >= 59 &&
            checkResults.CvvResponse != CvvResponseType.Match)
        {
            return RuleEngineResponse.CureSet("C0007", "C0002", "C1001", "NE");
        }

        // if (authPassed &&
        //     cvvResponse == CvvResponseType.Match &&
        //     avsResponse == AvsResponseType.FullMatch &&
        //     fraudScore <= 59)
        // {
        //     return RuleEngineResponse.EligibilityStage3Passed();
        // }

        // if (authPassed && 
        //     threeDsPassed)
        // {
        //     return RuleEngineResponse.EligibilityStage3Passed();
        // }


        workspan.Log.Information("Post auth CIT checks are not passed");
        return RuleEngineResponse.Decline();
    }

    protected override Stage3Response RunPostAuthMitChecks(CheckResults checkResults, Stage3Request request)
    {
        var workspan = Workspan.Start<E0025_RuleEngineStage3>();

        workspan
            .Log.Fatal("MIT orders are not supported for Everi");

        throw new NotEligibleException("MIT orders are not supported for Everi integration");
    }
}