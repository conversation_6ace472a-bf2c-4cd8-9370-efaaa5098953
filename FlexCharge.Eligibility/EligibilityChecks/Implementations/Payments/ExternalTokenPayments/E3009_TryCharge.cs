using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.PaymentsService;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class E3009_TryCharge : PaymentEligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task ExecuteBlockAsync()
    {
        await TryChargeAsync();
    }

    private async Task TryChargeAsync()
    {
        using var workspan = Workspan.Start<E3009_TryCharge>();


        PaymentTransactionResult chargeResult = null;

        Context.AuthorizationAttempts++;

        CreateAuthorizationAttemptedFingerprintActivity();

        var paymentsService = ServiceProvider.GetRequiredService<IPaymentsService>();

        await VoidAuthorizationIfAnyAsync(Order, paymentsService);

        var chargeResponse = await paymentsService.ChargeAsync(Order, Merchant, Site);

        if (chargeResponse != null)
        {
            //Order.IsFullAuthorizationChecked = true;

            chargeResult = new PaymentTransactionResult(chargeResponse);

            if (chargeResponse.GatewayFound)
            {
                Context.NextGateway = chargeResponse.NextGateway;
                Context.AlreadyUsedGateways.Add(chargeResponse.GatewayOrder);
            }
            else
            {
                await AddActivityAsync(EligibilityActivities.Charge_NoGatewayFound);

                throw new NotEligibleException();
            }
        }
        else
        {
            workspan.LogEligibility.Error($"Charge Error => Null is returned");
            await AddActivityAsync(EligibilityErrorActivities.Charge_NullResponseReturned);

            //Order.IsFullAuthorizationChecked = false;

            throw new NotEligibleException();
        }

        if (!chargeResponse.Success)
        {
            workspan.LogEligibility.Information(
                $"Charge Failed => {chargeResponse}");

            await AddActivityAsync(EligibilityActivities.Charge_Failed,
                data: chargeResponse,
                meta: meta => meta
                    .SetValue("CanBeRetried", chargeResponse.CanBeRetried));


            if (!chargeResponse.CanBeRetried)
            {
                if (Order.IsMIT())
                    throw new NotEligibleCancelledException();
            }
        }
        else
        {
            Order.IsPaymentCaptured = true;

            workspan.LogEligibility.Information(
                $"Charge Succeeded => {chargeResponse}");
            await AddActivityAsync(EligibilityActivities.Charge_Succeeded,
                data: chargeResponse);
        }
    }
}