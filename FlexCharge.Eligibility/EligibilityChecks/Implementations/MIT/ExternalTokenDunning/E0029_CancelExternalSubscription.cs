using System;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Entities;
using MassTransit;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class E0029_CancelExternalSubscription : BlockImplementationBase<NotEligiblieEverOrderProcessingContext>
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        var order = Context.Order;
        if (order.IsMIT())
        {
            var externalAccountId = order.GetExternalAccountIdOrDefault();
            if (externalAccountId != null)
            {
                return await base.CanBeExecutedAsync();
            }
        }

        return false;
    }

    protected override async Task ExecuteBlockAsync()
    {
        await CancelExternalProviderSubscriptionIfAnyAsync(Context.Order);
    }


    private async Task CancelExternalProviderSubscriptionIfAnyAsync(Order order)
    {
        using var workspan = Workspan.Start<E0029_CancelExternalSubscription>();

        try
        {
            // This order relates to an external provider subscription that should be canceled 
            // when the order is expired
            var externalAccountId = order.GetExternalAccountIdOrDefault();
            if (externalAccountId != null)
            {
                var publisher = GetRequiredService<IPublishEndpoint>();
                await publisher.Publish(new ExternalProviderCancelSubscriptionCommand
                {
                    // we can't cancel any active subscriptions that are processed by an external provider
                    CanCancelActiveSubscription = false,
                    Mid = order.Mid,
                    OrderId = order.Id,
                    ExternalAccountId = externalAccountId!,
                });
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }
}