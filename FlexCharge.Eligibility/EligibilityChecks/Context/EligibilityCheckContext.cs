using System;
using System.Collections.Generic;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.Eligibility.Services.BinNumberValidationServices;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.Eligibility.Services.RiskManagement;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityChecks;

public class EligibilityCheckContext : ExecutionContextBase<NoInputData, NoExecutionResult>,
    IEligibilityCheckContextModifier
{
    public override Guid TenantId => Merchant.Mid;
    public override Guid CorrelationId => Order.Id;

    public UserExperienceFlowType FlowType { get; }

    protected override IExecutionTracer Tracer { get; } = new DummyExecutionTracer();

    private Dictionary<string, string> StoredEligibilityCheckResults { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="paymentInstrumentInformation"></param>
    /// <param name="previousContextInThisEvaluationSession">If it's re-evaluation, this is context of previous evaluation pass</param>
    /// <param name="order"></param>
    /// <param name="orderPayload"></param>
    /// <param name="merchant"></param>
    /// <param name="site"></param>
    /// <param name="changedData"></param>
    /// <param name="isReEvaluation"></param>
    /// <param name="isOffSessionRetry"></param>
    /// <param name="fingerprintService"></param>
    /// <param name="flowType"></param>
    public EligibilityCheckContext(Merchant merchant, Site site,
        Order order, EvaluateRequest orderPayload,
        DataChangeFlags changedData, bool isReEvaluation, bool isOffSessionRetry,
        IFingerprintService fingerprintService, UserExperienceFlowType flowType,
        PaymentInstrumentInformation paymentInstrumentInformation,
        EligibilityCheckContext? previousContextInThisEvaluationSession = null)
    {
        Merchant = merchant;
        Site = site;
        Order = order;
        OrderPayload = orderPayload;
        ChangedData = changedData;
        IsReEvaluation = isReEvaluation;
        IsOffSessionRetry = isOffSessionRetry;
        FingerprintService = fingerprintService;
        FlowType = flowType;

        PaymentInstrumentInformation = paymentInstrumentInformation;

        if (previousContextInThisEvaluationSession != null)
        {
            AuthorizationAttempts = previousContextInThisEvaluationSession.AuthorizationAttempts;
            foreach (var gateway in previousContextInThisEvaluationSession.AlreadyUsedGateways)
            {
                AlreadyUsedGateways.Add(gateway);
            }
        }

        if (order != null)
            LoadStoredEligibilityCheckResults();
    }

    //Inputs
    public Merchant Merchant { get; }
    public Site Site { get; }
    public Order Order { get; }
    public EvaluateRequest OrderPayload { get; }

    public PaymentInstrumentInformation PaymentInstrumentInformation { get; set; }

    public DataChangeFlags ChangedData { get; private set; }
    public bool IsReEvaluation { get; }
    public bool IsOffSessionRetry { get; }
    public IFingerprintService FingerprintService { get; }

    public bool IsGhostMode => Order.IsGhostMode;

    //Results
    public Stage2Response Stage2Result { get; set; }
    public IDictionary<string, string> FraudResults { get; set; }
    public VerifyPaymentResult ZeroVerificationResult { get; set; }
    public bool InSubscriptionTestingMode { get; set; }

    public IDictionary<string, int> BureauResults { get; set; }
    public Stage3Response Stage3Result { get; set; }
    public CureExecutionResult? CureExecutionResult { get; set; }

    #region Fingerprints

    public IList<IFingerprint> Fingerprints { get; set; }
    public IDictionary<string, FingerprintStatistics> FingerprintStatistics { get; set; }
    public FingerprintStatistics CumulativeFingerprintStatistics { get; set; }
    //public IDictionary<string, FingerprintActivityHistory> TransmitActivityHistory { get; set; }

    #endregion


    //public ThreeDS ThreeDSResults { get; set; }

    public PaymentTransactionResult? FullAuthorizationOrSaleResult
    {
        get
        {
            return Order.PaymentTransactionResult != null
                ? JsonConvert.DeserializeObject<PaymentTransactionResult>(Order.PaymentTransactionResult)
                : null;
        }
    }

    public bool IsOrderPaymentFullyAuthorized => Order.IsOrderPaymentFullyAuthorized();

    public bool IsACHDebitAuthorized => Order.IsACHDebitAuthorized();
    public int AuthorizationAttempts { get; set; } = 0;
    public int? NextGateway { get; set; }

    public Dictionary<BinNumberValidationProvidersEnum, BinNumberValidationResponse> BinCheckResults = new();

    public HashSet<int> AlreadyUsedGateways = new();

    /// <summary>
    /// Should be called after every Eligibility Sequence execution
    /// </summary>
    public void ProcessExecutionResults()
    {
        StoreEligibilityCheckResults();
    }


    #region Stored Results

    private void LoadStoredEligibilityCheckResults()
    {
        if (Order.StoredEligibilityCheckResults != null)
        {
            StoredEligibilityCheckResults =
                JsonConvert.DeserializeObject<Dictionary<string, string>>(Order.StoredEligibilityCheckResults);
        }
        else StoredEligibilityCheckResults = new Dictionary<string, string>();
    }

    internal void StoreEligibilityCheckResults()
    {
        Order.StoredEligibilityCheckResults = JsonConvert.SerializeObject(StoredEligibilityCheckResults);
    }

    internal void StoreEligibilityCheckResult(EligibilityCheckBase check, string result)
    {
        if (result != null)
        {
            StoredEligibilityCheckResults[check.BlockId] = result;
        }
        else
        {
            StoredEligibilityCheckResults.Remove(check.BlockId);
        }
    }

    internal string? GetStoredEligibilityCheckResultOrDefault(EligibilityCheckBase check)
    {
        return StoredEligibilityCheckResults.GetValueOrDefault(check.BlockId);
    }

    #endregion

    void IEligibilityCheckContextModifier.SetChangedData(DataChangeFlags changedData)
    {
        ChangedData = changedData;
    }

    void IEligibilityCheckContextModifier.SetPaymentInstrumentInformation(
        PaymentInstrumentInformation paymentInstrumentInformation)
    {
        PaymentInstrumentInformation = paymentInstrumentInformation;
    }
}