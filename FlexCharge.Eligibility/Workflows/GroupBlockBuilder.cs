using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.Eligibility.Workflows.Steps;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

public class GroupBlockBuilder<TContext>
    where TContext : IExecutionContext
{
    private readonly GroupBlockBase<TContext> _groupBlock;

    public GroupBlockBuilder(GroupBlockBase<TContext> groupBlock)
    {
        _groupBlock = groupBlock;
    }

    private TBlock AddChildBlock<TBlock>(TBlock block)
        where TBlock : IBlock<TContext>
    {
        _groupBlock.AddChild(block);

        return block;
    }

    public void Add(string blockName)
    {
        string blockId = blockName;
        int indexOfNamePart = blockId.IndexOf('_');
        if (indexOfNamePart >= 0)
        {
            blockId = blockId.Substring(0, indexOfNamePart);
        }

        var block = new ImplementedActionBlock<TContext>(blockId, blockName);
        AddChildBlock(block);
    }
}