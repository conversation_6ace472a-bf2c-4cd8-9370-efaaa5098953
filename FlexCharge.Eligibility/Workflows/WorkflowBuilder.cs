using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.Eligibility.Workflows.Blocks;
using FlexCharge.Eligibility.Workflows.Exceptions;
using FlexCharge.Eligibility.Workflows.Steps;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

public class WorkflowBuilder<TContext>
    where TContext : IExecutionContext
{
    private IBlock<TContext> _CurrentBlock;

    //private Stack<IGroupBlock<TContext>> CurrentGroupStep = new();

    //public Sequence<TContext> MainSequence { get; } = new();

    public TContext Context => _context;

    public IBlock<TContext> StartBlock { get; private set; }

    public IBlock<TContext> CurrentBlock
    {
        get
        {
            return _CurrentBlock;
            // if (CurrentGroupStep.TryPeek(out var currentSequence)) return currentSequence;
            // else return MainSequence;
        }
    }

    #region FinishAction Helper class

    public class FinishAction : IDisposable
    {
        private readonly Action _action;

        public FinishAction(Action action)
        {
            _action = action;
        }

        public void Dispose()
        {
            _action();
        }
    }

    #endregion

    #region [Commented]Deserialization from string

    // public static WorkflowBuilder<TContext> CreateFromTextSequence(string sequenceDescription)
    // {
    //     WorkflowBuilder<TContext> sequence = new();
    //     var sequenceParts = sequenceDescription.Split(';');
    //
    //     for (int i = 0; i < sequenceDescription.Length;)
    //     {
    //         var ch = sequenceDescription[i];
    //         if (ch == '[')
    //         {
    //             sequence.AddParallelSteps();
    //             i++;
    //         }
    //         else if (ch == ']')
    //         {
    //             sequence.EndParallelSteps();
    //             i++;
    //         }
    //         else if (ch == '#')
    //         {
    //             int startOfCheckName = i + 1;
    //             int endOfCheckName = IndexOfCheckNameEnd(sequenceDescription, startOfCheckName);
    //             if (endOfCheckName < 0)
    //             {
    //                 throw new Exception($"Error in sequence description. Description: {sequenceDescription}");
    //             }
    //
    //             string checkName = sequenceDescription.Substring(startOfCheckName, endOfCheckName - startOfCheckName);
    //
    //             if (checkName.Length == 0)
    //             {
    //                 throw new Exception(
    //                     $"Error in sequence description. Incorrect check name. Description: {sequenceDescription}");
    //             }
    //
    //             sequence.Add(checkName);
    //
    //             i += checkName.Length + 1;
    //         }
    //         else i++;
    //     }
    //
    //     return sequence;
    // }
    //
    //
    // static int IndexOfCheckNameEnd(string sequenceDescription, int nameStartIndex)
    // {
    //     for (int i = nameStartIndex; i < sequenceDescription.Length; i++)
    //     {
    //         char ch = sequenceDescription[i];
    //         if (!char.IsDigit(ch) && !char.IsLetter(ch)) return i;
    //         else if (i == sequenceDescription.Length - 1) return i + 1;
    //     }
    //
    //     return -1;
    // }

    #endregion

    #region Builder Factory Methods

    /// <summary>
    /// 
    /// </summary>
    /// <param name="block"></param>
    /// <param name="resetCurrentBlock"></param>
    /// <typeparam name="TBlock">Set to True for multiple output blocks as next blocks cannot be automatically added to this block</typeparam>
    /// <returns></returns>
    /// <exception cref="WorkflowException"></exception>
    public BlockBuilder<TContext> AddBlock<TBlock>(TBlock block, bool resetCurrentBlock = false)
        where TBlock : IBlock<TContext>
    {
        if (StartBlock == null)
            StartBlock = block;

        if (CurrentBlock != null)
        {
            if (CurrentBlock is IActionBlock<TContext> standardBlock)
            {
                standardBlock.AttachNext(block, 0);
            }
            else throw new WorkflowException($"Cannot attach block {block}");
        }


        _CurrentBlock = !resetCurrentBlock ? block : null;

        return new BlockBuilder<TContext>(block);
    }

    public void Add(string blockName)
    {
        var blockId = GetBlockIdFromBlockName(blockName);

        var block = new ImplementedActionBlock<TContext>(blockId, blockName);
        AddBlock(block);
    }

    public static string GetBlockIdFromBlockName(string blockName)
    {
        string blockId = blockName;
        int indexOfNamePart = blockId.IndexOf('_');
        if (indexOfNamePart >= 0)
        {
            blockId = blockId.Substring(0, indexOfNamePart);
        }

        return blockId;
    }

    public GroupBlockBuilder<TContext> AddParallelBlock()
    {
        var parallelBlock = new ParallelGroupBlock<TContext>();

        AddBlock(parallelBlock);

        return new GroupBlockBuilder<TContext>(parallelBlock);
    }

    public SwitchBlockBuilder<TContext> AddSwitchBlock(string name)
    {
        var switchBlock = new SwitchBlock<TContext>();

        switchBlock.SetInstanceName(name);

        AddBlock(switchBlock, resetCurrentBlock: true);

        return new SwitchBlockBuilder<TContext>(switchBlock);
    }

    public AbTestBlockMultipleExitsBuilder<TContext> AddABTestBlock(string testName,
        Func<TContext, double> aCasePercentage)
    {
        var abTestBlock = new ABTestBlock<TContext>();

        abTestBlock.SetInstanceName(testName);
        abTestBlock.SetAPercentage(aCasePercentage);

        AddBlock(abTestBlock, resetCurrentBlock: true);

        return new AbTestBlockMultipleExitsBuilder<TContext>(abTestBlock);
    }

    public IfBlockMultipleExitsBuilder<TContext> AddIfBlock(string name, Func<TContext, bool> condition)
    {
        var ifBlock = new IfBlock<TContext>();

        ifBlock.SetInstanceName(name);
        ifBlock.SetCondition(condition);

        AddBlock(ifBlock, resetCurrentBlock: true);

        return new IfBlockMultipleExitsBuilder<TContext>(ifBlock);
    }

    #endregion

    #region Dependency Injection

    private IServiceProvider _serviceProvider;
    private TContext? _context;

    protected T GetRequiredService<T>() where T : notnull
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    #endregion


    public async Task<Workflow<TContext>> BuildAsync(WorkflowDescription workflowDescription, TContext context,
        ValueStorage resultsStorage, IServiceScopeFactory serviceScopeFactory, string serializedExternalParameterValues)
    {
        using var workspan = Workspan.Start<WorkflowBuilder<TContext>>()
            .Baggage("WorkflowId", workflowDescription.Id)
            .Baggage("WorkflowVersion", workflowDescription.Version)
            .Baggage("WorkflowDomain", workflowDescription.Domain)
            .Baggage("WorkflowName", workflowDescription.Name)
            .Baggage("WorkflowDescription", workflowDescription.Description)
            .LogEnterAndExit();


        try
        {
            using var serviceScope = serviceScopeFactory.CreateScope();
            _serviceProvider = serviceScope.ServiceProvider;

            _context = context;

            if (StartBlock is null)
                throw new WorkflowException("No blocks have been added to the workflow");

            await StartBlock.CreateAsync(context, serviceScopeFactory, resultsStorage);

            var workflow = new Workflow<TContext>(StartBlock);

            foreach (var externalControl in _externalControls)
            {
                workflow.AddExternalControl(externalControl);
            }

            LoadExternalParameterValues(context, serializedExternalParameterValues);
            workflow.SetExternalControlValues(context.ExternallyControlledParameters);

            return workflow;
        }
        finally
        {
            _serviceProvider = null;
        }
    }


    public async Task Run(WorkflowDescription workflowDescription, IServiceScopeFactory serviceScopeFactory)
    {
        using var workspan = Workspan.Start<WorkflowBuilder<TContext>>()
            .Baggage("CorrelationId", _context?.CorrelationId)
            .Baggage("TenantId", _context?.TenantId)
            .Baggage("WorkflowId", workflowDescription.Id)
            .Baggage("WorkflowVersion", workflowDescription.Version)
            .Baggage("WorkflowName", workflowDescription.Name)
            .Tag("WorkflowDescription", workflowDescription.Description)
            .Tag("WorkflowDomain", workflowDescription.Domain)
            .LogEnterAndExit();

        try
        {
            using var serviceScope = serviceScopeFactory.CreateScope();
            _serviceProvider = serviceScope.ServiceProvider;

            #region Observability

            await AddActivityAsync(WorkflowActivities.Workflow_ExecutionStarted, _context);

            if (_context?.ExternallyControlledParameters?.Count > 0)
            {
                var externalParameterValues = _context.ExternallyControlledParameters.SerializeValues();

                workspan.Log
                    .Information("External parameters specified: {ExternalParameters}",
                        externalParameterValues);

                await AddActivityAsync(WorkflowActivities.Workflow_ExternalParameters_Specified, _context,
                    data: externalParameterValues);
            }

            #endregion

            await StartBlock.Run();

            #region Observability

            await AddActivityAsync(WorkflowActivities.Workflow_ExecutionEnded, _context);

            #endregion
        }
        finally
        {
            _serviceProvider = null;
        }
    }

    #region Activity

    protected async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        TContext? context,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        var activityService = GetRequiredService<IActivityService>();

        await activityService.CreateActivityAsync(activityNameEnum,
            set => set
                .TenantId(context?.TenantId)
                .CorrelationId(context?.CorrelationId)
                .Subcategory(subcategory)
                .Data(data)
                .Meta(meta));
    }

    protected async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        TContext context,
        Func<object> dataSetter,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        object data = null;
        if (dataSetter != null)
            data = dataSetter();

        await AddActivityAsync(activityNameEnum, context, data, subcategory, meta);
    }

    #endregion

    #region SubFlows

    // private void CreateAndRegisterSubFlow(Guid subFlowId, WorkflowBuilder<TContext> subFlow)
    // {
    //     GetRequiredService<ISubFlowRegistry>().RegisterSubFlow(subFlowId, subFlow);
    // }

    #endregion

    public WorkflowBuilder<TContext> BLOCK(string name, IBlock<TContext> block)
    {
        block.SetInstanceName(name);
        AddBlock(block);
        return this;
    }


    public WorkflowBuilder<TContext> BLOCK(IBlock<TContext> block)
    {
        AddBlock(block);
        return this;
    }

    public WorkflowBuilder<TContext> DUMMY(string name)
    {
        return BLOCK(name, new DummyBlock<TContext>());
    }

    public WorkflowBuilder<TContext> DUMMY()
    {
        return BLOCK(new DummyBlock<TContext>());
    }


    public IfBlockMultipleExitsBuilder<TContext> IF(string name, Func<TContext, bool> condition) =>
        AddIfBlock(name, condition);

    public SwitchBlockBuilder<TContext> SWITCH(string name) =>
        AddSwitchBlock(name);

    public void END() =>
        BLOCK(new GotoEndBlock<TContext>());

    public void FINAL_END() =>
        CONTINUE() // this will connect all terminal links to the final end block
            .BLOCK(new EndBlock<TContext>());

    public void EXCEPTION<TException>(string? message = null) where TException : Exception =>
        BLOCK(new ThrowExceptionBlock<TContext>() {ExceptionType = typeof(TException), Message = message});

    // public void SUB_FLOW(Guid subFlowId) =>
    //     BLOCK(new SubFlowExecutionBlock<TContext>() {SubFlowId = subFlowId});
    //
    // public void SUB_FLOW(Guid subFlowId) =>
    //     BLOCK(new SubFlowExecutionBlock<TContext>() {SubFlowId = subFlowId});
    //
    //// public void SUB_FLOW(Guid subFlowId, WorkflowBuilder<TContext> subFlow)
    //// {
    ////     CreateAndRegisterSubFlow(subFlowId, subFlow);
    ////     BLOCK(new SubFlowExecutionBlock<TContext>() {SubFlowId = subFlowId});
    //// }

    public WorkflowBuilder<TContext> INLINE_SUBFLOW(WorkflowBuilder<TContext> subFlow)
    {
        subFlow
            .CONTINUE(); // this will connect the subflow to the current block event if there are multiple terminal links

        BLOCK(subFlow.StartBlock);

        return subFlow;
    }

    public WorkflowBuilder<TContext> INLINE_SUBFLOW(Action<WorkflowBuilder<TContext>> createSubFlow)
    {
        var subFlow = new WorkflowBuilder<TContext>();
        createSubFlow(subFlow);

        return INLINE_SUBFLOW(subFlow);
    }

    public WorkflowBuilder<TContext> Log(string message, LogLevel level = LogLevel.Information) =>
        BLOCK(new LogBlock<TContext>() {Message = message, Level = level});

    public WorkflowBuilder<TContext> LogError(string message, LogLevel level = LogLevel.Information) =>
        Log(message, level);

    public WorkflowBuilder<TContext> Activity<TActivityNameEnum>(TActivityNameEnum message,
        Func<TContext, object>? data = null, Action<TContext, IPayloadMetadataSetter> meta = null)
        where TActivityNameEnum : Enum
    {
        return BLOCK(message.ToString(), new ActivityBlock<TContext>()
        {
            ActivityCategory = message.GetType(),
            ActivityName = message.ToString(),
            DataSetter = data,
            MetaSetter = meta
        });
    }

    public WorkflowBuilder<TContext> Transform(string name, Action<TContext> contextUpdater)
    {
        return BLOCK(name, new TransformBlock<TContext>() {ContextUpdater = contextUpdater});
    }

    public WorkflowBuilder<TContext> CONTINUE( /*Action<WorkflowBuilder<TContext>> builder*/)
    {
        if (StartBlock == null)
            return this;

        if (CurrentBlock != null)
            return this;

        var continuationDummyBlock = new ContinuationDummyBlock<TContext>();

        BLOCK(continuationDummyBlock);

        var theVeryFirstBlock = StartBlock;
        while (theVeryFirstBlock.PreviousLinks.Any())
        {
            theVeryFirstBlock = theVeryFirstBlock.PreviousLinks.First();
        }

        BlockBuilder<TContext>.ContinueWith(StartBlock, continuationDummyBlock);

        return this;
    }

    private void LoadExternalParameterValues(
        IExecutionContext eligibilityContext, string serializedExternalParameters)
    {
        using var workspan = Workspan.Start<WorkflowBuilder<TContext>>();

        if (!string.IsNullOrWhiteSpace(serializedExternalParameters))
        {
            try
            {
                eligibilityContext.ExternallyControlledParameters.DeserializeValues(serializedExternalParameters);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Failed to deserialize external parameters");
                throw new FlexChargeException("Failed to deserialize external parameters", e);
            }
        }
    }


    #region External Control

    List<IExternalControl> _externalControls = new();

    // public IEnumerable<IExternalControl> GetExternalControls()
    // {
    //     return _externalControls;
    // }

    public TExternalControl AddExternalControl<TExternalControl>(TExternalControl externalControl)
        where TExternalControl : IExternalControl
    {
        _externalControls.Add(externalControl);

        return externalControl;
    }

    #endregion
}