using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public interface IBlockImplementation : IExternalControllable
{
    string BlockId { get; }

    Task<BlockEditor> GetBlockEditorAsync(Dictionary<string, string> blockParameters,
        IServiceScopeFactory serviceScopeFactory);

    Task<List<AnswerValidationError>> TryUpdateBlockEditableParametersAsync(IDictionary<Guid, string> answers,
        IServiceScopeFactory serviceScopeFactory);

    Dictionary<string, string> GetBlockParameters();
}

public interface IBlockImplementation<TContext> : IBlockImplementation
{
    TContext Context { get; set; }

    Task ExecuteAsync();

    /// <summary>
    /// To support adding planned eligibility steps that are not implemented yet
    /// </summary>
    bool IsImplemented { get; }

    bool IsProductionBlock { get; }
    public bool IsTestBlock { get; }

    void Initialize(TContext context,
        IServiceScopeFactory serviceScopeFactory,
        ValueStorage valueStorage);
}