using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Workflows.Steps;

public class ImplementedActionBlock<TContext> : ActionBlockBase<TContext>, IBlock<TContext>
    where TContext : IExecutionContext
{
    public override string Name { get; }
    public override string FullName => Check.GetType().Name;

    public ImplementedActionBlock(string blockId, string fullName)
    {
        Name = blockId;
        SetInstanceName(GetHumanReadableName(fullName));
    }


    private string GetHumanReadableName(string name)
    {
        string humanReadableName = null;
        if (name.StartsWith("E") && name.Contains("_"))
        {
            humanReadableName = name
                .Substring(name.IndexOf('_') + 1)
                .Replace("_", " ");
        }

        return !string.IsNullOrWhiteSpace(humanReadableName) ? humanReadableName : name;
    }

    public override string ToString()
    {
        return $"#{Name} ({FullName})";
    }

    public override async Task CreateAsync(TContext context,
        IServiceScopeFactory serviceScopeFactory,
        ValueStorage resultsStorage)
    {
        using var serviceScope = serviceScopeFactory.CreateScope();

        var eligibilityCheckResolver = serviceScope.ServiceProvider
            .GetRequiredService<ServiceCollectionExtensions.WorkflowBlockResolver>();

        Check = eligibilityCheckResolver(Name) as IBlockImplementation<TContext>;

        if (Check == null)
        {
            Workspan.Current?.Log.Error($"Unknown eligibility check: {Name}");
            await AddActivityAsync(EligibilityErrorActivities.EligibilityChecks_UnknownEligibilityCheck,
                subcategory: Name,
                metaSetter: meta => meta.SetValue("CheckId", Name));

            throw new NotEligibleException();
        }
        else
        {
            Check.Initialize(context, serviceScopeFactory, resultsStorage);
        }

        // Call the base implementation to create next linked blocks
        await base.CreateAsync(context, serviceScopeFactory, resultsStorage);
    }

    public IBlockImplementation<TContext> Check { get; private set; }

    public override async Task Execute()
    {
        using var workspan = Workspan.Start<ImplementedActionBlock<TContext>>($"EligibilityCheck: {Name}");

        try
        {
            await Check.ExecuteAsync();
        }
        catch (Exception e) when (e is not NotEligibleException)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    #region External Control

    public override IEnumerable<IExternalControl> GetExternalControls() => Check.GetExternalControls();

    #endregion
}