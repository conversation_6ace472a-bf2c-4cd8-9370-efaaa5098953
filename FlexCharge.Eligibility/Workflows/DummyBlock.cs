using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

/// <summary>
/// Represents an action block with no specific implementation.
/// This block performs no operations during execution.
/// </summary>
/// <typeparam name="TContext">
/// The type of the execution context that this block operates within.
/// It must implement the <see cref="IExecutionContext"/> interface.
/// </typeparam>
internal class DummyBlock<TContext> : ActionBlockBase<TContext>
    where TContext : IExecutionContext
{
    public override string Name => "Dummy Block";

    public override string FullName => "Dummy Block";

    public override async Task Execute()
    {
    }
}