using System;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;

namespace FlexCharge.Eligibility.Workflows;

public interface IExecutionContext : ITraceable
{
    Guid TenantId { get; }
    Guid CorrelationId { get; }

    ExternallyControlledParameterValues ExternallyControlledParameters => null;

    abstract IExecutionTracer ITraceable.Tracer { get; }
}

public interface IExecutionContext<TInputData, TExecutionResult> : IExecutionContext
    where TInputData : IWorkflowInputData
    where TExecutionResult : IWorkflowExecutionResult
{
    public TInputData InputData { get; }

    public void SetInputData(TInputData inputData);

    public TExecutionResult ExecutionResult { get; }

    public void SetResult(TExecutionResult result);
}