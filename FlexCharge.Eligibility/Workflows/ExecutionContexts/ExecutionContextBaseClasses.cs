using System;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;

namespace FlexCharge.Eligibility.Workflows;

public abstract class ExecutionContextBase<TInputData, TExecutionResult> :
    IExecutionContext<TInputData, TExecutionResult>
    where TInputData : IWorkflowInputData
    where TExecutionResult : IWorkflowExecutionResult
{
    public abstract Guid TenantId { get; }

    public abstract Guid CorrelationId { get; }

    IExecutionTracer ITraceable.Tracer => Tracer;

    protected abstract IExecutionTracer Tracer { get; }

    #region External Control

    public ExternallyControlledParameterValues ExternallyControlledParameters { get; } = new();

    public T GetExternalParameterValue<T>(ExternalControlBase<T> externalControl)
        where T : struct
    {
        return externalControl.Value ?? externalControl.DefaultValue;
    }

    #endregion

    private TInputData _inputData;

    public TInputData InputData => _inputData;

    #region Input Data

    public void SetInputData(TInputData inputData)
    {
        _inputData = inputData;
    }

    #endregion

    private TExecutionResult _executionResult;

    public TExecutionResult ExecutionResult => _executionResult;

    public void SetResult(TExecutionResult result)
    {
        _executionResult = result;
    }
}

public abstract class ExecutionContextWithOutputOnlyBase<TExecutionResult> :
    ExecutionContextBase<NoInputData, TExecutionResult>
    where TExecutionResult : IWorkflowExecutionResult
{
}

public abstract class ExecutionContextBase : ExecutionContextBase<NoInputData, NoExecutionResult>
{
}