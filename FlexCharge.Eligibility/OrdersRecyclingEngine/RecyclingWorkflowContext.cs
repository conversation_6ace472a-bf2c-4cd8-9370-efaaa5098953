using System;
using FlexCharge.Common.Response;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.Eligibility.Workflows.Tracing;

namespace FlexCharge.Eligibility.OrdersRecyclingEngine;

public class RecyclingWorkflowContext : ExecutionContextWithOutputOnlyBase<RecyclingInstructions>
{
    public RecyclingWorkflowContext(Merchant merchant, Order order, DateTime utcNow)
    {
        TenantId = order.Mid;
        CorrelationId = order.Id;

        Merchant = merchant;

        Order = order;

        var lastTransactionResponse = order.GetLastTransactionResultOrDefault();

        LastTransactionResponse = lastTransactionResponse;

        ResponseCodeSource = lastTransactionResponse?.ResponseCodeGateway ?? "";
        ResponseCode = lastTransactionResponse?.ResponseCode ?? "";


        // FirstDeclineDateDaysOld = (decimal) daysSinceOrderFirstPlaced,
        DaysSinceOrderFirstPlaced = (int) Math.Truncate((utcNow.Date - order.CreatedOn.Date).TotalDays);

        MitExpiryIntervalInHours =
            (int) Math.Truncate((order.ExpiryDate.Value.Date - order.CreatedOn.Date).TotalHours);

        OrderProcessingWindowInHours = (int) Math.Truncate((order.ExpiryDate.Value - order.CreatedOn).TotalHours);
    }

    public int OrderProcessingWindowInHours { get; }

    public PaymentTransactionResponse? LastTransactionResponse { get; }

    public override Guid TenantId { get; }
    public override Guid CorrelationId { get; }

    protected override IExecutionTracer Tracer { get; } = new SimpleExecutionTracer();

    public Merchant Merchant { get; }
    public Order Order { get; }

    public string ResponseCode { get; }
    public string ResponseCodeSource { get; }
    public int DaysSinceOrderFirstPlaced { get; }
    public int MitExpiryIntervalInHours { get; }

    /// <summary>
    /// For testing purposes it can be manually set to any value.
    /// </summary>
    public DateTime CurrentTimeUtc { get; set; } = DateTime.UtcNow;
}

public class RecycleProcessingWindow
{
    /// <summary>
    /// Distance from the start of the current day in UTC.
    /// </summary>
    public TimeSpan Start { get; init; }

    /// <summary>
    /// Distance from the start of the current day in UTC.
    /// </summary>
    public TimeSpan End { get; init; }

    public RecycleProcessingWindow(TimeSpan start, TimeSpan end)
    {
        Start = start;
        End = end;
    }
};

public class RecyclingInstructions : BaseResponse, IWorkflowExecutionResult
{
    public bool StopRepayments { get; private set; }
    public decimal? ChargeRetryIntervalInDays { get; private set; }

    public RecycleProcessingWindow RecycleWindow { get; private set; }


    private RecyclingInstructions()
    {
    }

    #region Factory Methods

    public static RecyclingInstructions StopRecycling(string stopReason)
    {
        var instructions = new RecyclingInstructions
        {
            StopRepayments = true
        };

        instructions.AddError(stopReason);

        return instructions;
    }

    public static RecyclingInstructions Schedule(RecycleProcessingWindow recycleProcessingWindow)
    {
        return new RecyclingInstructions
        {
            ChargeRetryIntervalInDays = 1,
            RecycleWindow = recycleProcessingWindow
        };
    }


    public static RecyclingInstructions Schedule(int retryEveryNDays, RecycleProcessingWindow recycleProcessingWindow)
    {
        return new RecyclingInstructions
        {
            ChargeRetryIntervalInDays = retryEveryNDays,
            RecycleWindow = recycleProcessingWindow
        };
    }

    public static RecyclingInstructions Skip()
    {
        return new RecyclingInstructions
        {
            ChargeRetryIntervalInDays = 0
        };
    }

    #endregion
}