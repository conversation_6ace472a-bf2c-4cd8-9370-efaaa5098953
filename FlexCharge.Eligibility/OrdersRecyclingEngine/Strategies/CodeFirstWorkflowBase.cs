using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.OrdersRecyclingEngine.Strategies;

public abstract class CodeFirstWorkflowBase<TContext>
    where TContext : IExecutionContext
{
    public abstract Guid WorkflowId { get; }
    public abstract string Domain { get; }
    public abstract string Name { get; }
    public abstract string Description { get; }
    public abstract int Version { get; }
    public abstract bool IsReadonly { get; }

    public abstract Task<WorkflowBuilder<TContext>> CreateWorkflowAsync();

    public WorkflowDescription ToWorkflowDescription()
    {
        return new WorkflowDescription
        {
            Id = WorkflowId,
            Name = Name,
            IsReadonly = IsReadonly,
            Description = Description,
            Version = Version,
            Domain = Domain
        };
    }
}