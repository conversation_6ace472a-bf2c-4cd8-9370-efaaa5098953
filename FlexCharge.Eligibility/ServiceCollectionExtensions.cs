using System.Collections.Generic;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.BinNumberValidationServices;
using FlexCharge.Eligibility.Services.CreditBureauServices;
using FlexCharge.Eligibility.Services.FraudServices;
using FlexCharge.Eligibility.Services.SessionMatcherService;
using FlexCharge.Eligibility.Services.SessionMatcherService.SessionMatchers;

namespace FlexCharge.Eligibility;

public class ServiceCollectionExtensions
{
    public delegate ISessionMatcher SessionMatcherResolver(SessionMatchersEnum key);

    public delegate IFraudService FraudServiceResolver(FraudProvidersEnum key);

    public delegate ICreditBureauService CreditBureauServiceResolver(BureauProvidersEnum key);

    public delegate IBinNumberValidationService
        BinNumberValidationServiceResolver(BinNumberValidationProvidersEnum key);

    public delegate ICure CureResolver(string cureName, bool throwExceptionIfNotFound);

    public delegate IBlockImplementation WorkflowBlockResolver(string cureName);
}