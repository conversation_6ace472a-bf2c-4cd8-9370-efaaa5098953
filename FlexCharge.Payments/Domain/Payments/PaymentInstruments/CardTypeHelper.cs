namespace FlexCharge.Payments.Domain.Payments.PaymentInstruments;

public static class CardTypeHelper
{
    public static CardType? ConvertFromString(string? cardType)
    {
        switch (cardType?.ToUpper())
        {
            case "CREDIT":
                return CardType.Credit;
            case "DEBIT":
                return CardType.Debit;
            case "PREPAID":
                return CardType.Prepaid;
            default:
                return null;
        }
    }
}