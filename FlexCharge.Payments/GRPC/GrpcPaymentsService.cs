using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Grpc.Payments;
using FlexCharge.Payments.Domain.Payments.PaymentInstruments;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Grpc.Core;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using ShippingAddress = FlexCharge.Contracts.Common.ShippingAddress;

namespace FlexCharge.Payments.GRPC;

public class GrpcPaymentsService : FlexCharge.Grpc.Payments.GrpcPaymentsService.GrpcPaymentsServiceBase
{
    private readonly IPaymentOrchestrator _paymentOrchestrator;
    private readonly PostgreSQLDbContext _dbContext;

    public GrpcPaymentsService(
        IPaymentOrchestrator paymentOrchestrator,
        PostgreSQLDbContext dbContext)
    {
        _paymentOrchestrator = paymentOrchestrator;
        _dbContext = dbContext;
    }

    #region AUTHORIZE

    public override async Task<SerializedResponse> FullyAuthorizePayment(
        SerializedRequest request, ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcPaymentsService>();

        try
        {
            var serializedRequest = request.Request;

            var command = JsonConvert.DeserializeObject<FullyAuthorizePaymentCommand>(serializedRequest);

            workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentId", command.PaymentInstrument?.PaymentInstrumentId)
                .LogEnterAndExit();

            var descriptor = new DescriptorDTO();

            if (command.UseDynamicDescriptor)
            {
                descriptor.Name = command.Descriptor.Name;
                descriptor.Phone = command.Descriptor.Phone;
                descriptor.Address = command.Descriptor.Address;
                descriptor.City = command.Descriptor.City;
                descriptor.State = command.Descriptor.State;
                descriptor.Postal = command.Descriptor.Postal;
                descriptor.Country = command.Descriptor.Country;
                descriptor.Mcc = command.Descriptor.Mcc;
                descriptor.Url = command.Descriptor.Url;
                descriptor.MerchantId = command.Descriptor.MerchantId;
            }

            var billingAddress = command.BillingAddress;
            var shippingAddress = command.ShippingAddress;

            if (command.UseBillingAsShipping)
            {
                shippingAddress = new ShippingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Phone = billingAddress.Phone,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country
                };
            }

            var authorizationRequest = new AuthorizationRequest()
            {
                Token = command.PaymentInstrument.PaymentInstrumentToken,
                CardType = CardTypeHelper.ConvertFromString(command.PaymentInstrument.Type),
                ProcessorCode = command.ProcessorId,
                Mid = command.Mid,
                CurrencyCode = command.Currency,
                Amount = command.Amount,
                Discount = command.Discount,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
                Modifiers = command.Modifiers == null
                    ? null
                    : PaymentModifiers.FromPaymentModifiersModel(command.Modifiers),
                BillingAddress = new BillingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country,
                    PhoneNumber = billingAddress.Phone,
                    Email = command.PayerEmail
                },
                ShippingAddress = command.UseBillingAsShipping
                    ? new Services.PaymentServices.PaymentServiceModels.ShippingAddress
                    {
                        Address1 = billingAddress.Address1,
                        Address2 = billingAddress.Address2,
                        City = billingAddress.City,
                        State = billingAddress.State,
                        Zip = billingAddress.Zip,
                        Country = billingAddress.Country,
                        FirstName = billingAddress.FirstName,
                        LastName = billingAddress.LastName,
                        PhoneNumber = billingAddress.Phone
                    }
                    : new Services.PaymentServices.PaymentServiceModels.ShippingAddress
                    {
                        FirstName = shippingAddress?.FirstName,
                        LastName = shippingAddress?.LastName,
                        Address1 = shippingAddress?.Address1,
                        Address2 = shippingAddress?.Address2,
                        City = shippingAddress?.City,
                        State = shippingAddress?.State,
                        Zip = shippingAddress?.Zip,
                        Country = shippingAddress?.Country,
                        PhoneNumber = shippingAddress?.Phone,
                    },
                UseDynamicDescriptor = command.UseDynamicDescriptor,
                Descriptor = descriptor,
                IsRebilling = command.IsRebilling,
                Device = new DeviceDetailsDTO
                {
                    IpAddress = command.DeviceInformation?.IpAddress,
                    UserAgent = command.DeviceInformation?.UserAgent
                },
                IsCit = command.IsCit,
                TryUseAccountUpdater = command.TryUseAccountUpdater,
                UserDefinedFields = command.UserDefinedFields
            };

            if (!string.IsNullOrEmpty(command.SchemeTransactionId))
            {
                authorizationRequest.NetworkReferenceData = new NetworkReferenceData
                {
                    TransactionId = command.SchemeTransactionId
                };
            }

            if (command.OverrideDuplicateOrderCheckTimespan.HasValue)
            {
                authorizationRequest.DuplicateCheckInSeconds =
                    command.OverrideDuplicateOrderCheckTimespan;
            }

            authorizationRequest.BillingAddress.Email = command.PayerEmail;

            if (command.ThreeDs != null)
            {
                authorizationRequest.ThreeDS = new ThreeDsecureDTO
                {
                    EcommerceIndicator = command.ThreeDs.EcommerceIndicator,
                    AuthenticationValue = command.ThreeDs.AuthenticationValue,
                    DirectoryServerTransactionId = command.ThreeDs.DirectoryServerTransactionId,
                    ThreeDsVersion = command.ThreeDs.ThreeDsVersion,
                    Xid = command.ThreeDs.Xid,
                    AuthenticationValueAlgorithm = command.ThreeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = command.ThreeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = command.ThreeDs.AuthenticationResponseStatus,
                    Enrolled = command.ThreeDs.Enrolled == true ? "Y" : "N",
                };
            }


            var authResponse = await _paymentOrchestrator.AuthorizeAsync(authorizationRequest,
                new OrchestrationOptions
                {
                    IsCascadingPayment = command.PerformCascadingPayment,
                    Order = command.GatewayOrder,
                    PreviousOrder = command.GatewayOrder,
                    IsRebilling = command.IsRebilling,
                    IsCIT = command.IsCit,
                    Currency = command.Currency,
                    Country = command.PaymentInstrument?.Country,
                    PaymentRoutingStrategy = command.PaymentRoutingStrategy,
                    BlocklistedProviders = command.BlocklistedProviders,
                    AlreadyTriedProviders = command.AlreadyTriedProviders?.Select(x =>
                        (SupportedGatewayId: x.SupportedGatewayId, Timestamp: x.Timestamp)).ToList(),
                    RiskTier = command.RiskTier,
                }, token: CancellationToken.None);

            var gatewayNotFound = authResponse.ErrorsWithCodes.Any(x => x.Key == "GatewayNotFound");

            var commandResponse = new FullyAuthorizePaymentCommandResponse
            {
                Success = authResponse.Success,
                ResponseCode = authResponse.ProviderResponseCode,
                ResponseMessage = authResponse.ProviderResponseMessage,
                BinNumber = authResponse.BinNumber,
                TransactionId = authResponse.TransactionId,
                OrderId = authResponse.OrderId,

                GatewayFound = !gatewayNotFound,
                GatewayId = authResponse.ProviderId,
                SupportedGatewayId = authResponse.SupportedGatewayId,
                GatewayOrder = authResponse.GatewayOrder,

                Provider = authResponse.Provider,
                ProviderResponseCode = authResponse.ProviderResponseCode,
                ProviderTransactionToken = authResponse.ProviderTransactionToken,

                InternalResponseCode = authResponse.InternalResponseCode,
                InternalResponseMessage = authResponse.InternalResponseMessage,
                InternalResponseGroup = authResponse.InternalResponseGroup,

                CvvCode = authResponse.CvvCode,
                AvsCode = authResponse.AvsCode,
                Errors = authResponse.Errors,
                ErrorsWithCodes = authResponse.ErrorsWithCodes,
                NextGateway = authResponse?.NextGatewayOrder,

                AccountUpdaterMessage = authResponse.AccountUpdaterMessage
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion

    #region CAPTURE

    public override async Task<SerializedResponse> CapturePayment(SerializedRequest request, ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcPaymentsService>();

        try
        {
            var serializedRequest = request.Request;

            var command = JsonConvert.DeserializeObject<CapturePaymentCommand>(serializedRequest);

            workspan
                .Baggage("Mid", command.Mid)
                .Baggage("TransactionId", command.TransactionId)
                .Baggage("OrderId", command.OrderId)
                .LogEnterAndExit();


            var response = await _paymentOrchestrator.CaptureAsync(new CapturePaymentRequest()
            {
                Mid = command.Mid,
                TransactionId = command.TransactionId
            }, CancellationToken.None);

            var commandResponse = new CapturePaymentCommandResponse
            {
                Success = response.Success,
                ResponseCode = response.ProviderResponseCode,
                ResponseMessage = response.ProviderResponseMessage,
                //BinNumber = captureResponse.BinNumber,
                TransactionId = response.TransactionId,
                Provider = response.Provider,
                ProviderResponseCode = response.ProviderResponseCode
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion

    #region VOID

    public override async Task<SerializedResponse> VoidPayment(SerializedRequest request, ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcPaymentsService>();

        try
        {
            var serializedRequest = request.Request;

            var command = JsonConvert.DeserializeObject<CapturePaymentCommand>(serializedRequest);

            workspan
                .Baggage("Mid", command.Mid)
                .Baggage("TransactionId", command.TransactionId)
                .Baggage("OrderId", command.OrderId)
                .LogEnterAndExit();

            var response = await _paymentOrchestrator.VoidAsync(new VoidPaymentRequest()
            {
                Mid = command.Mid,
                OrderId = command.OrderId.HasValue ? command.OrderId.Value : Guid.Empty,
                TransactionId = command.TransactionId
            });

            var commandResponse = new VoidPaymentCommandResponse()
            {
                Success = response.Success,
                ResponseCode = response.ProviderResponseCode,
                ResponseMessage = response.ProviderResponseMessage,
                TransactionId = command.TransactionId,
                Provider = response.Provider,
                ProviderResponseCode = response.ProviderResponseCode,
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion

    #region CHARGE

    public override async Task<SerializedResponse> ChargePayment(SerializedRequest request, ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcPaymentsService>();

        try
        {
            var serializedRequest = request.Request;

            var command = JsonConvert.DeserializeObject<ChargePaymentCommand>(serializedRequest);

            workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentExternalToken", command.ExternalPaymentInstrumentToken)
                .Baggage("ExternalAccountId", command.ExternalAccountId)
                .LogEnterAndExit();

            var stripeNmorSupportedGatewayId = SystemWideSupportedGateways.Stripe_NMOR;
            var supportedGateway =
                await _dbContext.SupportedGateways.SingleAsync(x => x.Id == stripeNmorSupportedGatewayId);

            var chargeRequest = new ChargePaymentRequest()
            {
                SupportedGateway = supportedGateway,
                ExternalAccountId = command.ExternalAccountId,
                Token = command.ExternalPaymentInstrumentToken,
                Mid = command.Mid,
                CurrencyCode = command.Currency,
                Amount = command.Amount,
                Discount = command.Discount,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
                IsCit = command.IsCit,
                UserDefinedFields = command.UserDefinedFields
            };

            var chargeResponse =
                await _paymentOrchestrator.ChargeAsync(chargeRequest, token: CancellationToken.None);

            var gatewayNotFound = chargeResponse.Errors.Any(x => x.Code == "GatewayNotFound");

            var chargeResult = chargeResponse.Result!;

            var commandResponse = new ChargePaymentCommandResponse
            {
                Success = chargeResponse.Succeeded,
                ResponseCode = chargeResult.ProviderResponseCode,
                ResponseMessage = chargeResult.ProviderResponseMessage,
                BinNumber = chargeResult.BinNumber,
                TransactionId = chargeResult.TransactionId,
                OrderId = chargeResult.OrderId,
                GatewayFound = !gatewayNotFound,
                GatewayOrder = chargeResult.GatewayOrder,
                Provider = chargeResult.Provider,
                ProviderResponseCode = chargeResult.ProviderResponseCode,
                ProviderTransactionToken = chargeResult.ProviderTransactionToken,

                InternalResponseCode = chargeResult.InternalResponseCode,
                InternalResponseMessage = chargeResult.InternalResponseMessage,
                InternalResponseGroup = chargeResult.InternalResponseGroup,

                CvvCode = chargeResult.CvvCode,
                AvsCode = chargeResult.AvsCode,
                Errors = chargeResponse.Errors.Select(x => x.ErrorMessage).ToList(),
                ErrorsWithCodes = chargeResult.ErrorsWithCodes,
                NextGateway = chargeResult?.NextGatewayOrder,

                AccountUpdaterMessage = chargeResult.AccountUpdaterMessage,

                CanBeRetried = chargeResult.CanBeRetried,
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public override async Task<SerializedResponse> CanChargeExternalToken(SerializedRequest request,
        ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcPaymentsService>();

        try
        {
            var serializedRequest = request.Request;

            var command = JsonConvert.DeserializeObject<ChargePaymentCommand>(serializedRequest);


            workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentExternalToken", command.ExternalPaymentInstrumentToken)
                .LogEnterAndExit();

            var stripeNmorSupportedGatewayId = SystemWideSupportedGateways.Stripe_NMOR;
            var supportedGateway =
                await _dbContext.SupportedGateways.SingleAsync(x => x.Id == stripeNmorSupportedGatewayId);

            var chargeRequest = new CanChargeExternalTokenRequest()
            {
                SupportedGateway = supportedGateway,
                ExternalAccountId = command.ExternalAccountId,
                Token = command.ExternalPaymentInstrumentToken,
                Mid = command.Mid,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
            };

            var chargeResponse =
                await _paymentOrchestrator.CanChargeExternalTokenAsync(chargeRequest, token: CancellationToken.None);

            // var gatewayNotFound = chargeResponse.ErrorsWithCodes.Any(x => x.Code == "GatewayNotFound");

            var commandResponse = new CanChargeExternalTokenCommandResponse
            {
                Success = chargeResponse.Succeeded,

                CanCharge = chargeResponse.Result?.CanCharge == true,

                Errors = chargeResponse.Errors.Select(x => x.ErrorMessage).ToList(),
                ErrorsWithCodes = chargeResponse.Errors
                    .Select(x => new KeyValuePair<string, string>(x.Code, x.ErrorMessage))
                    .ToList(),
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(commandResponse)
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    #endregion
}