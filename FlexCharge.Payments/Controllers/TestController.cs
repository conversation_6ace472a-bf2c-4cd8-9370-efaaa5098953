#if DEBUG
using FlexCharge.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using CsvHelper;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.NoSQL.DynamoDB;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Stripe.Models;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Common;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Payments.Consumers;
using FlexCharge.Payments.DebugHelpers;
using FlexCharge.Payments.Domain.Payments.IntegrityManager;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.AccountUpdater.VGS;
using FlexCharge.Payments.Services.OpenBanking;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.ProcessingStrategies;
using FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;
using FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Common;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx.Models.Sales;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Cards;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;
using FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3;
using FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;
using FlexCharge.Payments.Services.PaymentServices.Providers.Stripe;
using FlexCharge.Payments.Services.PaymentServices.ResponseCodeMapping;
using FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.Authentication;
using FlexCharge.Payments.Services.Stripe;
// using FlexCharge.Payments.Services.SpreedlyService;
using FlexCharge.Payments.Services.SVBAchServices;
using FlexCharge.Payments.Services.SVBAchServices.Models;
using FlexCharge.Payments.Services.TokenExService;
using FlexCharge.Payments.Services.TokenExService.Models.ThreeDSecure;
using FlexCharge.Utils.JsonConverters;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Stripe;
using Stripe.TestHelpers;
using BillingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.BillingAddress;
using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;
using ShippingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.ShippingAddress;
using TransactionType = FlexCharge.Payments.Services.TokenExService.Models.ThreeDSecure.TransactionType;
using Transfer = FlexCharge.Payments.Services.SVBAchServices.Models.Transfer;
using VoidPaymentCommand = FlexCharge.Contracts.Commands.VoidPaymentCommand;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ILogger _logger;

        private readonly VgsSdk _vgsSdk;

        //private readonly IPayoutService _payoutService;
        public IPaymentOrchestrator _paymentOrchestrator { get; set; }
        private readonly IOpenBankingService _obServices;

        private readonly ISVBService _svbServices;

        //private readonly ISpreedlyService _spreedlyService;
        private readonly ITokenExService _tokenExService;
        private readonly MerchantCreatedEventConsumer _merchantCreatedEventConsumer;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IPublishEndpoint _publishEndpoint;
        private readonly IRequestClient<TokenizeInstrumentCommand> _tokenizeInstrumentRequestClient;
        private readonly IRequestClient<FullyAuthorizePaymentCommand> _fullyAuthorizePaymentRequestClient;
        private readonly IRequestClient<CapturePaymentCommand> _capturePaymentRequestClient;
        private readonly IRequestClient<DebitPaymentCommand> _debitPaymentRequestClient;
        private readonly IRequestClient<VoidPaymentCommand> _voidPaymentRequestClient;
        private readonly IRequestClient<CreditPaymentCommand> _creditPaymentRequestClient;
        private readonly AppOptions _globalData;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly CheckoutPaymentProvider _checkoutProvider;
        private readonly RapydPaymentProvider _rapydPaymentProvider;
        private readonly PaysafePaymentProvider _paysafeProvider;
        private readonly PaysafeSDK _paysafesdk;
        private readonly RapydSdk _rapydSdk;
        private readonly Revolv3Sdk _revolvSdk;
        private readonly AdyenPaymentProvider _adyenPaymentProvider;

        private readonly StripeSubscriptionSdk _stripeSubscriptionSdk;

        private readonly IEnumerable<IPaymentProvider> _paymentProviders;
        private readonly EpxSdk _epxsdk;
        private readonly ISVBService _svbAchService;
        private readonly IPartnerPaymentServices _partnerPaymentServices;
        private readonly IGeoServices _geoServices;
        private readonly IReportsService _reportsService;
        private IStripeBillingPortalService _stripeBillingPortalService;

        public TestController(ILogger<TestController> logger, IOptions<AppOptions> globalData,
            //IPayoutService payoutService, 
            IPublishEndpoint publishEndpoint, IOpenBankingService obServices,
            MerchantCreatedEventConsumer merchantCreatedEventConsumer,
            PostgreSQLDbContext dbContext,
            ISVBService svbServices,
            IPaymentOrchestrator paymentOrchestrator,
            IRequestClient<TokenizeInstrumentCommand> tokenizeInstrumentRequestClient,
            ITokenExService tokenExService,
            IRequestClient<FullyAuthorizePaymentCommand> fullyAuthorizePaymentRequestClient,
            IServiceScopeFactory scopeFactory,
            IRequestClient<CapturePaymentCommand> capturePaymentRequestClient,
            IRequestClient<VoidPaymentCommand> voidPaymentRequestClient,
            IRequestClient<CreditPaymentCommand> creditPaymentRequestClient,
            IRequestClient<DebitPaymentCommand> debitPaymentRequestClient,
            IEnumerable<IPaymentProvider> providers,
            StripeSubscriptionSdk stripeSubscriptionSdk,
            PaysafeSDK paysafesdk,
            EpxSdk epxsdk,
            ISVBService svbAchService,
            IPartnerPaymentServices partnerPaymentServices,
            RapydSdk rapydSdk,
            Revolv3Sdk revolvSdk,
            IReportsService reportsService,
            VgsSdk vgsSdk, IStripeBillingPortalService stripeBillingPortalService)
        {
            _logger = logger;
            //_payoutService = payoutService;
            _publishEndpoint = publishEndpoint;
            _obServices = obServices;
            _merchantCreatedEventConsumer = merchantCreatedEventConsumer;
            _dbContext = dbContext;
            _svbServices = svbServices;
            _paymentOrchestrator = paymentOrchestrator;
            _tokenizeInstrumentRequestClient = tokenizeInstrumentRequestClient;
            _tokenExService = tokenExService;
            _fullyAuthorizePaymentRequestClient = fullyAuthorizePaymentRequestClient;
            _capturePaymentRequestClient = capturePaymentRequestClient;
            _voidPaymentRequestClient = voidPaymentRequestClient;
            _creditPaymentRequestClient = creditPaymentRequestClient;
            _debitPaymentRequestClient = debitPaymentRequestClient;
            _globalData = globalData.Value;
            _scopeFactory = scopeFactory;
            _checkoutProvider = providers.FirstOrDefault(p => p.GetType() == typeof(CheckoutPaymentProvider))
                as CheckoutPaymentProvider;
            _paysafesdk = paysafesdk;
            var ls = providers.ToList();
            _paysafeProvider = providers.FirstOrDefault(p => p.GetType() == typeof(PaysafePaymentProvider))
                as PaysafePaymentProvider;
            _paymentProviders = providers;
            _stripeSubscriptionSdk = stripeSubscriptionSdk;
            _epxsdk = epxsdk;
            _rapydSdk = rapydSdk;
            _svbAchService = svbAchService;
            _revolvSdk = revolvSdk;
            _partnerPaymentServices = partnerPaymentServices;
            _reportsService = reportsService;
            _adyenPaymentProvider = providers.FirstOrDefault(p =>
                    p.GetType() == typeof(AdyenPaymentProvider))
                as AdyenPaymentProvider;

            _vgsSdk = vgsSdk;
            _stripeBillingPortalService = stripeBillingPortalService;
        }

        #region Stripe Test Helpers

        #region Stripe Subscription Test Clocks

        [HttpPost("StartStripeSubscriptionClock")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> StartStripeSubscriptionClock()
        {
            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.IsActive && x.SupportedGateway.Sandbox &&
                    x.NameIdentifier == GatewayTypesConstants.Stripe);

            var sgateway = await _dbContext.SupportedGateways
                .Where(x => x.Id == gateway.SupportedGatewayId && x.Sandbox)
                .FirstOrDefaultAsync();

            StripeConfiguration.ApiKey = sgateway.SecretKey;

            var service = new TestClockService();

            var clocks = await service.ListAsync();
            foreach (var c in clocks)
            {
                await service.DeleteAsync(c.Id);
            }

            var options = new TestClockCreateOptions
            {
                FrozenTime = DateTime.UtcNow, //  DateTimeOffset. FromUnixTimeSeconds(1635750000).UtcDateTime,
                Name = "Clock1",
            };
            var clock = await service.CreateAsync(options);

            Environment.SetEnvironmentVariable("STRIPE_CLOCK", clock.Id);
            return Ok();
        }

        // [HttpGet("TestAdyen")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestAdyen()
        // {
        //       var authrequest= new AuthorizationRequest()
        //       {
        //           
        //       }
        // }

        [HttpPost("SetStripeSubscriptionClock")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> SetStripeSubscriptionClock(int days)
        {
            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.IsActive && x.SupportedGateway.Sandbox &&
                    x.NameIdentifier == GatewayTypesConstants.Stripe);

            var sgateway = await _dbContext.SupportedGateways
                .Where(x => x.Id == gateway.SupportedGatewayId && x.Sandbox)
                .FirstOrDefaultAsync();

            StripeConfiguration.ApiKey = sgateway.SecretKey;

            var clockId = Environment.GetEnvironmentVariable("STRIPE_CLOCK");
            if (clockId is not null)
            {
                var service = new TestClockService();
                var coptions = new TestClockAdvanceOptions
                {
                    FrozenTime = DateTime.UtcNow.AddDays(days),
                };

                await service.AdvanceAsync(clockId, coptions);
            }


            return Ok();
        }

        [HttpGet("TestEpx")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestEpx()
        {
            // EpxConfig config = new()
            // {
            //     SecretKey = "8EEDC66DF02D7803E05321281FAC8C31",
            //     Customer = "9001",
            //     Merchant = "711001",
            //     Dba = "1",
            //     Terminal = "66"
            // };
            EpxConfig config = new()
            {
                SecretKey = "0E77AF57021C04FCE063140F180AC4E0",
                Customer = "3007",
                Merchant = "*************",
                Dba = "1",
                Terminal = "3"
            };

            try
            {
                //     var brick = await _epxsdk.CreateBricAsync(new()
                //     {
                //        
                //         Account = "******************", // "******************",
                //         ExpirationDate = "2512",
                //         CardEntryMethod = "E"
                //     }, config, CancellationToken.None);
                //
                //     var xdx = 1;
                // }
                // catch (Exception e)
                // {
                //     var x = 1;
                // }


                // var verifyResp = await _epxsdk.VerifyAsync(new()
                // {
                //     Account =
                //         "****************", //   "******************",//"****************16", // "******************",
                //     Address = new()
                //     {
                //         Address = "123 Main St",
                //         FirstName = "Joe",
                //         City = "NYC",
                //         State = "NY",
                //         LastName = "Doe",
                //         ZipCode = "12345"
                //     },
                //     // CountryCode = "US"
                //     ExpirationDate = "2512",
                //     Cvv2 = "123",
                //     CardEntryMethod = "E",
                //     IndustryType = "E",
                //     VerboseResponse = "Y",
                //     EmailAddress = "<EMAIL>",
                //
                //     WorkPhone = "**********",
                //     HomePhone = "**********",
                // }, config, CancellationToken.None);


                // var xss = await _epxsdk.CreateBricAsync(new()
                // {
                //     Account = "****************",
                //     ExpirationDate = "2802"
                // }, config, CancellationToken.None);
                ///****************
                ///

                EpxSalesRequest areq = new()
                {
                    Amount = 1.00M,
                    Transaction = 1115,
                    BatchID = 221,


                    Account = "******************",
                    Cvv2 = "243",
                    ExpirationDate = "2810",
                    // "****************", //  "****************", //****************", //""****************", // "****************",//"****************", // "******************",
                    Address = new()
                    {
                        Address = "333 A",
                        FirstName = "Fred",
                        City = "Milpitas",
                        State = "CA",
                        LastName = "Fre",
                        ZipCode = "95035"
                    },


                    CardEntryMethod = "E",
                    Capture = false,
                    IndustryType = "E",
                    VerboseResponse = "Y",
                    SoftDescriptor = "Nautica N",
                    SoftDescriptor2 = "************",

                    // TavvECI = "07",
                    EmailAddress = "<EMAIL>",
                    //   CavvResponse = "fffs",
                    //   CavvUCAF = "cavv",
                    UserData = new() {{"1", "pera"}, {"2", "gebe"}},
                    CurrencyCode = "840",

                    OrderNumber = Guid.NewGuid().ToString().Substring(0, 25),
                    //WorkPhone = "1234567890",
                    //HomePhone = "1234567890",
                    // ShippedToCountryCode = "US",
                    // ShippedToFirstName = "Nick",
                    // ShippedToLastName = "Lender",
                    // ShippedToPhoneNumber = "1234567890",
                    // ShippedToStreetAddress = "Main Street 1",
                    // ShippedToZipCode = "12345",

                    // CavvResponse = "2",
                    // CavvUCAF = "kAMDMQj2JxGyJwY76bX5B4QBoXzp", //"xgQYYgZVAAAAAAAAAAAAAAAAAAAA",
                    // TDSVersion = "2",
                    //  DirectoryServerTranId =
                    //        "e1a579eb-dc2a-4ca3-b2af-74c8d75c884e", // "dbcb49fb-fed6-4059-b466-2845fa6317d9",
                    //  TransType = TransTypeValiues.Sale,

                    //  DirectoryServerTranId = "f38e6948-5388-41a6-bca4-b49723c19437",

                    CountryCode = "US",
                    // ThreedsVersion = "1",
                    //TDSVersion = "2",
                };
                var ajson = System.Text.Json.JsonSerializer.Serialize(areq);
                var response = await _epxsdk.SaleOrAuthorizeAsync(areq, config, CancellationToken.None);

                var captureRsp = await _epxsdk.CaptureByBricAsync(new()
                {
                    Amount = 12.22M,
                    IndustryType = "E",
                    CardEntryMethod = CardEntryMethodValues.BricOrGuidToken
                }, response.Reference.Bric, config, CancellationToken.None);

                var refrsp = await _epxsdk.RefundByBricAsync(new()
                {
                    Amount = 12.22M,
                    IndustryType = "E",
                    CardEntryMethod = CardEntryMethodValues.BricOrGuidToken
                }, response.Reference.Bric, config, CancellationToken.None);

                var revrsp1 = await _epxsdk.ReverseCaptureOrRefundOrAuthorizationByBricAsync(new()
                {
                    Transaction = 112,
                    BatchID = 22,
                    IndustryType = "E",
                    CardEntryMethod = CardEntryMethodValues.BricOrGuidToken
                }, refrsp.Reference.Bric, config, CancellationToken.None);

                var ddd = 1;

                // var voidResp = await _epxsdk.VoidByBricAsync(new()
                // {
                //     Amount = 12.22M,
                //     IndustryType = "E",
                //     CardEntryMethod = CardEntryMethodValues.BricOrGuidToken
                // }, response.Reference.Bric, config, CancellationToken.None);


                // var state = await _epxsdk.GetTransactionDetailByBric(response.Reference.Bric,
                //     config, CancellationToken.None);
                // var statejson = System.Text.Json.JsonSerializer.Serialize(state);
                //var capstate = await _epxsdk.GetTransactionDetailByBric(captureRsp.Reference.Bric,
                //config, CancellationToken.None);
                //var capturestatejson = System.Text.Json.JsonSerializer.Serialize(state);


                var dddd = 1;


                // var nnn = 1;


                //


                //
                // var voidjson = System.Text.Json.JsonSerializer.Serialize(voidResp);


                var revrsp = await _epxsdk.ReverseCaptureOrRefundOrAuthorizationByBricAsync(new()
                {
                    IndustryType = "E",
                    CardEntryMethod = CardEntryMethodValues.BricOrGuidToken
                }, captureRsp.Reference.Bric, config, CancellationToken.None);


                var xx = 1;
            }
            catch (Exception e)
            {
                var xx = 1;
            }

            return Ok();
            // CVV2 = "123",
            // AMOUNT = "12.22",
            // ACCOUNT_NBR = ******************,
            // DBA_NBR = 1, 
            // ADDRESS = "123 Main St",
            // FIRST_NAME = "John",
            // LAST_NAME = "Doe",
            // EXP_DATE = "2705",
            // TRAN_NBR = 114205,
            // TRAN_TYPE = TranTypeVals.Authorization,
            // CARD_ENT_METH = CardEntryMethodEnum.MerchantOnFile,  
        }

        #endregion

        [HttpGet("TestStripeSubscription")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeSubscription()
        {
            // full-woodcock-formerly.ngrok-free.app is static ngrok domain
            // So url is full-woodcock-formerly.ngrok-free.app/webhooks
            // Stripe should has this url in webhook settings
            // 5022 is port for Payments MS http
            string webhooksDomain =
                await NgrokHelper.GetNgrokWebhooksDomainAsync(false, "full-woodcock-formerly.ngrok-free.app", 5022);


            var mid = new Guid(
                "76e105a7-4d60-457b-82f2-648d46aba287"); //new Guid("5c321e38-f39c-4ae6-a23b-b031f76efaf7"); //"f0cb6a94-2a3a-4161-a23e-a6d4d74c90e7");
            var token =
                await _tokenizeInstrumentRequestClient
                    .GetResponse<TokenizeInstrumentCommandResponse>(
                        new()
                        {
                            Mid = mid, //"31e66a07-8882-4da8-a901-9fd460c55b24",//mid
                            FirstName = "John",
                            LastName = "Fed",
                            Number = "****************",
                            VerificationValue = "999", //cvv,
                            Month = 1,
                            Year = 2025
                        }, CancellationToken.None);

            var z = await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                //  x.Merchant.Mid == mid &&
                x.IsActive && x.SupportedGateway.Sandbox &&
                x.NameIdentifier == GatewayTypesConstants.Stripe);
            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.Merchant.Mid == mid &&
                    x.IsActive && x.SupportedGateway.Sandbox &&
                    x.NameIdentifier == GatewayTypesConstants.Stripe);
            var sgateway = await _dbContext.SupportedGateways
                .Where(x => x.Id == gateway.SupportedGatewayId && x.Sandbox)
                .FirstOrDefaultAsync();

            StripeConfiguration.ApiKey = sgateway.SecretKey;


            var options = new TestClockCreateOptions
            {
                FrozenTime = DateTime.UtcNow, //  DateTimeOffset. FromUnixTimeSeconds(1635750000).UtcDateTime,
                Name = "Ten days renewal",
            };
            var service = new TestClockService();
            var clocks = await service.ListAsync();
            foreach (var c in clocks)
            {
                await service.DeleteAsync(c.Id);
            }

            var clock = await service.CreateAsync(options);
            var orderId = Guid.NewGuid();


            var utcNow = DateTime.UtcNow;

            var orderExpiryDate = utcNow + TimeSpan.FromDays(4);

            //var daysTillSubscriptionExpiration = (int)Math.Floor((order.ExpiryDate.Value.Date - utcNow.Date).TotalDays);

            // We want to start retrying the charge on the next day
            // on 7pm UTC - it's morning in USA - good time to try a charge
            var firstChargeTime = utcNow.Date + TimeSpan.FromDays(1) + TimeSpan.FromHours(17);

            // We want to stop retries (essentially end not started subscription) on the day before the subscription expiration
            // to protect from charges after order expiration date
            var daysFromSubscriptionStartTillSubscriptionExpiration = (int) Math.Floor(
                (orderExpiryDate.Date - firstChargeTime.Date).TotalDays) - 1;

            var cancelAt = firstChargeTime + TimeSpan.FromDays(daysFromSubscriptionStartTillSubscriptionExpiration);


            var request = new CreateSubscriptionRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                IntervalUnit = IntervalUnit.Day,
                IntervalCount = daysFromSubscriptionStartTillSubscriptionExpiration,
                //PayerId = payerId,
                Amount = 10000,
                FeeAmount = 0,

                CurrencyCode = "USD",
                StartDate = firstChargeTime,
                CancelAt = cancelAt,

                CreditCard = new SaleRequestCreditCard
                {
                    // Mid = mid, //"31e66a07-8882-4da8-a901-9fd460c55b24",//mid
                    FirstName = "John",
                    LastName = "Fed",
                    Number = "****************", //cardNumber,
                    VerificationValue = "999", //cvv,
                    Month = 1,
                    Year = 2025
                },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = 12,
                //     Year = 2029
                // },
                PaymentInstrumentId = Guid.NewGuid(),
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                // Device = new DeviceDetailsDTO()
                // {
                //     IpAddress = "************"
                // },
                // ShippingAddress = null,
                // ThreeDS = new ThreeDsecureDTO
                // {
                //     EcommerceIndicator = "02",
                //     AuthenticationValue = "ejJRWG9SWWRpU2I1M21DelozSXU",
                //     DirectoryServerTransactionId = Guid.NewGuid().ToString()
                // }
            };

            var card = request.CreditCard;


            var req = new CreateStripeSubscriptionRequest()
            {
                Amount = request.Amount,
                Currency = request.CurrencyCode,
                ProviderName = request.Gateway.Name,
                Card = new()
                {
                    Cvc = card.VerificationValue,
                    Number = card.Number,
                    ExpMonth = card.Month,
                    ExpYear = card.Year
                },
                Email = request.BillingAddress.Email,
                OrderId = request.OrderId,
                Mid = request.Mid,
                BillingDetails = request.BillingAddress is var billing
                                 && billing is null
                    ? null
                    : new()
                    {
                        Address = new StripeAddress()
                        {
                            City = billing.City,
                            Country = billing.Country,
                            Line1 = billing.Address1,
                            Line2 = billing.Address1,
                            State = billing.State,
                            PostalCode = billing.Zip
                        },
                    },
                IntervalCount = request.IntervalCount,
                IntervalUnit = request.IntervalUnit,
                IsCit = false,
                RequestOptions = new() {ApiKey = sgateway.SecretKey},
                LiveMode = !request.SupportedGateway.Sandbox,
                //startdate=default
                PaymentInstrumentId = request.PaymentInstrumentId,
                StartDate = request.StartDate,
                CancelAt = request.CancelAt,
            };

            var resss = await _stripeSubscriptionSdk.CreateSubscriptionAsync(req, CancellationToken.None, clock.Id);

            // var coptions = new TestClockAdvanceOptions
            // {
            //     FrozenTime = DateTime.UtcNow.AddDays(1),
            // };
            //
            // service.Advance(clock.Id, coptions);
            //
            // /// var sawass = await _stripeSubscriptionSdk.GetAndUpdateExternalSubscriptionState(new O,  new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            //
            // var subs = await _stripeSubscriptionSdk.GetSubscriptionsByIdAsync(orderId.ToString(),
            //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            //
            // coptions = new TestClockAdvanceOptions
            // {
            //     FrozenTime = DateTime.UtcNow.AddDays(1),
            // };
            // service.Advance(clock.Id, coptions);
            // // subs = await _stripeSubscriptionSdk.GetSubscriptionsByIdAsync(orderId.ToString(),
            // //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            // // var invoices = await _stripeSubscriptionSdk.GetInvoicesBySubsIdAsync(subs[0].Id,
            // //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            //
            // coptions = new TestClockAdvanceOptions
            // {
            //     FrozenTime = DateTime.UtcNow.AddDays(2),
            // };
            // service.Advance(clock.Id, coptions);
            // invoices = await _stripeSubscriptionSdk.GetInvoicesBySubsIdAsync(subs[0].Id,
            //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            //
            // coptions = new TestClockAdvanceOptions
            // {
            //     FrozenTime = DateTime.UtcNow.AddDays(8),
            // };
            // service.Advance(clock.Id, coptions);
            // invoices = await _stripeSubscriptionSdk.GetInvoicesBySubsIdAsync(subs[0].Id,
            //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            // subs = await _stripeSubscriptionSdk.GetSubscriptionsByIdAsync(orderId.ToString(),
            //     new() { ApiKey = sgateway.SecretKey }, CancellationToken.None);
            // //  new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.UtcNow.Day, 0, 0, 0, DateTimeKind.Utc).AddDays(1)
            //

            // var res = await _paymentOrchestrator.CreateSubscriptionAsync(request, gateway,
            //     token: CancellationToken.None);
            //var sub = await _dbContext.Subscriptions.FirstOrDefaultAsync(s => s.OrderId == res.OrderId);
            //var cras = await _paymentOrchestrator.CancelSubscriptionAsync(orderId, CancellationToken.None);
            //var xx = 1;


            return Ok();
        }

        #endregion

        [HttpGet("testVGS")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestVGS(string clientId,
            string clientSecret, string username, string password,
            string cardNumber,
            int expirt_month,
            int expiry_year,
            string cardholderName, string vgsMerchantId)
        {
            //var result = await _vgsSdk.GetAccessTokenAsync(
            //  clientId, clientSecret);

            // var etoken = await _vgsSdk.EnrollNetworkTokenAsync(username, password, result.access_token, null);

            var updateCard = await _vgsSdk.CardCheckAsync(
                new()
                {
                    name = cardholderName,
                    number = cardNumber,
                    exp_month = expirt_month,
                    exp_year = expiry_year
                }, clientId, clientSecret
            );
            return Ok(updateCard);
        }


        [HttpGet("TestRevolv")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestRevolv(string cardNumber, string cvv, string expDate,
            string firstName, string lastName, int amount)
        {
            var config = new RevolvConfiguration();


            var gateway = await _dbContext
                .SupportedGateways
                .SingleOrDefaultAsync(x => x.Name == "revolv-prod");

            config = JsonSerializer.Deserialize<RevolvConfiguration>(gateway.Configuration);
            ///YYYY-MM-dd
            var invoices = await _revolvSdk.GetInvoices(config,
                null,
                null,
                "2024-08-12",
                "2024-08-14",
                null,
                null,
                CancellationToken.None);


            var refundRequest = new RevolvRefundRequest()
            {
                amount = 100
            };
            // var res= await _revolvSdk.Refund(refundRequest, config, 1184149,
            //      CancellationToken.None);


            var saleRequest = new RevolvSalesRequest()
            {
                invoice = new()
                {
                    amount = new()
                    {
                        value = amount,
                    },
                },

                paymentMethod = new()
                {
                    creditCard = new RevolvCreditCard()
                    {
                        expirationDate = expDate,
                        securityCode = cvv,
                        paymentAccountNumber =
                            cardNumber, //  "****************" //  "****************",//  "****************"
                    },
                    billingFirstName = firstName,
                    billingLastName = lastName,

                    //691 S Milpitas Blvd, Ste 212, Milpitas, CA, 95035, US
                },
                dynamicDescriptor = new()
                {
                    subMerchantName = "Flex Test",
                    subMerchantPhone = "************",
                    city = "NYC"
                }
            };

            var saleResultO = await _revolvSdk.Sale(saleRequest, config, CancellationToken.None);
            var saleResult = saleResultO.SuccessfulResponse;
            //
            // var inv = await _revolvSdk.GetInvoice(config, saleResult.invoiceId, CancellationToken.None);
            //
            // var refundequest = new RevolvRefundRequest()
            // {
            //     amount = saleRequest.invoice.amount.value
            // };
            //
            //
            // var refundResult =
            //     await _revolvSdk.Refund(refundequest, config, saleResult.invoiceId, CancellationToken.None);


            var authRequest = new RevolvAuthRequest()
            {
                amount = new()
                {
                    value = 1,
                },
                paymentMethod = new()
                {
                    creditCard = new RevolvCreditCard()
                    {
                        expirationDate = expDate,
                        securityCode = cvv,
                        paymentAccountNumber =
                            cardNumber, //  "****************" //  "****************",//  "****************"
                    },
                    billingFirstName = firstName,
                    billingLastName = lastName,
                },

                // dynamicDescriptor = new()
                // {
                //     city = "Milpitas"
                // }
            };

            // result.dynamicDescriptor = descriptor is null
            //     ? null
            //     : new()
            //     {
            //         countryCode = descriptor.Country,
            //         city = descriptor.City,
            //         subMerchantName = descriptor.Name,
            //         subMerchantPhone = descriptor.Phone,
            //         subMerchantId = descriptor.MerchantId
            //     };
            var result1 = await _revolvSdk.Authorize(authRequest, config, CancellationToken.None);


            var voidRequest = new RevolvVoidRequest()
            {
                amount = authRequest.amount.value + 1000,
                paymentMethodAuthorizationId = result1.SuccessfulResponse.paymentMethodAuthorizationId.Value
            };


            var resultVoid = await _revolvSdk.Void(voidRequest, config, CancellationToken.None);


            var captureRequest = new RevolvCaptureRequest()
            {
                invoice = new()
                {
                    amount = authRequest.amount,
                },
                dynamicDescriptor = new()
                {
                    city = "bb"
                }
            };
            var capresult1 = await _revolvSdk.Capture(captureRequest, config,
                result1.SuccessfulResponse.paymentMethodAuthorizationId.Value,
                CancellationToken.None);

            var request = new AccessTokenRequest
            {
                ClientId = config.clientId,
                ClientSecret = config.secretKey
            };

            var result = await _revolvSdk.GetAccessToken(request, config, CancellationToken.None);


            return Ok();
        }

        [HttpGet("TestPaysafe")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestPaySafe()
        {
            var amounts = new[] {100}; //5, 6, 11, 77, 100}

            foreach (var amount in amounts)
            {
                var mid = new Guid("02d7934a-3969-4d19-b3aa-da9d2e1ae77e");
                // "76e105a7-4d60-457b-82f2-648d46aba287"); // new Guid("31e66a07-8882-4da8-a901-9fd460c55b24");//new Guid("9a1a5026-2539-48f2-8847-ef8415292300");
                var token =
                    default(MassTransit.Response<TokenizeInstrumentCommandResponse>);
                await _tokenizeInstrumentRequestClient
                    .GetResponse<TokenizeInstrumentCommandResponse>(
                        new TokenizeInstrumentCommand
                        {
                            Mid = mid, //"31e66a07-8882-4da8-a901-9fd460c55b24",//mid
                            FirstName = "John",
                            LastName = "Fed",
                            Number = "****************", // "****************", //cardNumber,
                            VerificationValue = "999", //cvv,
                            Month = 1,
                            Year = 2025
                        }, CancellationToken.None);

                //var mid = new Guid("4ab80cdb-fcc4-4e8e-a354-22858a189734");//new Guid("9a1a5026-2539-48f2-8847-ef8415292300");


                _logger.LogInformation(
                    $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");


                // var gateway =
                //     await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                //         x.Merchant.Mid == mid && x.IsActive && x.IsSandbox &&
                //         x.NameIdentifier == GatewayTypesConstants.Checkout);

                // mid = await _dbContext.Gateways.Where(x =>
                //     x.IsActive && 
                //      x.IsSandbox &&
                //     x.NameIdentifier == GatewayTypesConstants.FlexChargeDummy)
                //     .Select(x=>x.Merchant.Mid).FirstOrDefaultAsync();

                var gateway =
                    await _dbContext.Gateways.Include(x => x.Merchant).FirstOrDefaultAsync(x =>
                        // x.Merchant.Mid == mid &&
                        x.IsActive && x.SupportedGateway.Sandbox &&
                        x.NameIdentifier == GatewayTypesConstants.Paysafe);
                mid = gateway.Merchant.Mid;

                //Auth Capture
                try
                {
                    var vres = await Verify(mid, gateway, "MyShoesStore", token.Message);

                    var authorizeCommandRes =
                        await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoeStore", token);
                    //var voidPaymentCommandRes = await Void_Via_Command(mid, authorizeCommandRes.TransactionId);

                    var captureCommandRes = await Capture_Via_Command(mid, authorizeCommandRes.TransactionId);
                    var creditP = await Refund_Via_Command(mid, amount, "USD", authorizeCommandRes.OrderId,
                        Guid.NewGuid(), authorizeCommandRes.TransactionId);
                    var debitCommandRes =
                        await Debit_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoesStore", token);


                    var authorizeCommandRes2 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD",
                        "MyShoesStore", token);
                    var captureCommandRes2 = await Capture_Via_Command(mid, authorizeCommandRes2.TransactionId);
                    var creditPaymentCommandRes2 = await Refund_Via_Command(mid, amount, "USD",
                        authorizeCommandRes2.OrderId, Guid.NewGuid(), captureCommandRes2.TransactionId);
                    // //auth void
                    var authorizeCommandRes3 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD",
                        "MyShoesStore", token);
                    var voidPaymentCommandRes3 = await Void_Via_Command(mid, authorizeCommandRes3.TransactionId);

                    // // //Auth Capture + void (Failed)
                    var authorizeCommandRes4 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD",
                        "MyShoesStore", token);
                    var captureCommandRes4 = await Capture_Via_Command(mid, authorizeCommandRes4.TransactionId);
                    var voidPaymentCommandRes4 = await Void_Via_Command(mid, captureCommandRes4.TransactionId);
                    // //
                    // // //Auth + void (Failed)
                    var authorizeCommandRes5 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD",
                        "MyShoesStore", token);
                    var voidPaymentCommandRes5 = await Void_Via_Command(mid, authorizeCommandRes5.TransactionId);
                    //
                    // var voidPaymentCommandRes2 = await Void_Via_Command(mid, authorizeCommandRes5.TransactionId);


                    await Authorize_Capture_Test(mid, gateway, amount, "USD", "MyShoesStore", "+14084594427",
                        token.Message);
                    // await Authorize_Capture_Credit_Test(mid, gateway, 100, "USD", "FlexCharge1", "*********", token.Message);
                    // await Credit_Test(mid,Guid.NewGuid(),Guid.NewGuid(),Guid.NewGuid(),new Guid("2a6f5fd8-0c37-4524-8cd5-1686f4e689da"),gateway, 110, "USD", "FlexCharge1", "*********", token.Message);
                    // await Authorize_Capture_Credit_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                    // await Authorize_Void_Test(mid, gateway, amount, "USD", "MyShoesStore", null, token.Message);
                    // await Authorize_Test(mid, gateway, amount, "USD", "MyShoesStore", null, token.Message);
                    // await Authorize_Void_Test(mid, gateway,amount,"USD","MyCustomShoes","*********", token.Message);
                    // await Authorize3d_Test(mid, gateway,20,"USD", "FlexCharge5","*********",token.Message);
                    // await Sale_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                    // await Sale_Test(mid, gateway,210,"USD", "FlexCharge6","*********",token.Message);
                    await Sale_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                    await CascadingAuthorize(mid, 0, amount, "USD", "MyShoesStore", "*********", true, token.Message);
                    //
                    // await Verify(mid, gateway, "MyShoesStore", token.Message);
                    // await Authoriza_Scheme_Transaction_Id(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                    // await Authorize_SchemTransactionID(mid, gateway, 111, "USD", "FlexCharge7", "*********", true, token.Message);
                    // await Authorize_W_Shipping_Void_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);

                    //await ACH_DEBIT_Test(mid);

                    //test credit
                    // await Credit_Test(mid, Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid(),
                    //     new Guid("b54a7744-29aa-49aa-b979-983e3728d5f0")
                    //     , gateway, amount, "USD", "FlexCharge1", "*********",
                    //     token.Message);

                    // var nmiSDKTEst = new NmiPaymentSdk("kyHpBft784kEmn57ka4qG59G3sGv54wW");
                    // // Create a test transaction request
                    // var transaction = new TransactionRequest
                    // {
                    //     Type = "auth",
                    //     CcNumber = "****************",
                    //     CcExp = "1223",
                    //     Amount = 100.00m,
                    //     FirstName = "John",
                    //     LastName = "Doe",
                    //     Address1 = "123 Main St",
                    //     City = "Los Angeles",
                    //     State = "CA",
                    //     Zip = "90001",
                    //     Country = "US"
                    // };
                    //
                    // // Process the transaction
                    // var response = await nmiSDKTEst.ProcessTransaction(transaction);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
            }

            return Ok();
        }

        [HttpGet("TestRapyd")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestRapyd()
        {
            var _serializeOptionsUTF8 = new JsonSerializerOptions
            {
                // PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                NumberHandling = JsonNumberHandling.AllowReadingFromString
                //  Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            _serializeOptionsUTF8.Converters.Add(new JsonStringEnumConverter());
            _serializeOptionsUTF8.Converters.Add(new IgnoreEmptyStringNullableEnumConverter());
            _serializeOptionsUTF8.Converters.Add(new NullToEmptyStringConverter());
            var gateway =
                await _dbContext.SupportedGateways.FirstOrDefaultAsync(x =>
                    x.Sandbox &&
                    x.Name == "Rapyd"); //GatewayTypesConstants.Rapyd);
            var config = JsonSerializer.Deserialize<RapydConfiguration>(gateway.Configuration, _serializeOptionsUTF8);
            var request = new RapydCreatePaymentRequest()
            {
                Amount = 92,
                Currency = "EUR",
                Capture = false,
                PaymentMethod = new()
                {
                    fields = new GbVisaCard()
                    {
                        Name = "John Black",
                        Number = "****************", // "****************",
                        ExpirationMonth = "12",
                        ExpirationYear = "2025",
                        CVV = "123",
                        NumberType = CardNumberType.tpan // regulat card number,
                    },
                    type = RapydPaymentMethods.gb_visa_card,
                },
                CompletePaymentUrl = "fc.com", //required
                ErrorPaymentUrl = "fc.error.com", //required
                Ewallet = "ewallet_b2d10d543aad0c4a0883fd4b9a034c96",


                PaymentMethodOptions = new()
                {
                    _3d_required = false,
                    _3d_version = "2.1.0",
                    cavv = "eqrwwerewrwe",
                    xid = "erere",
                    ds_trans_id = "sfgdsfgdfsg",
                    eci = "ttt",
                    // tavv = null// token cryptogram
                },
            };

            try
            {
                var customerReq = new RapydCreateCustomerRequest()
                {
                    name = "Joe Black",
                    email = "<EMAIL>",
                };

                var customer = await _rapydSdk.CreateCustomerAsync(customerReq, config, Guid.NewGuid().ToString(),
                    CancellationToken.None);

                var x = await _rapydSdk.AuthorizeOrSaleAsync(request, config, Guid.NewGuid().ToString(),
                    CancellationToken.None);


                // var vv = await _rapydSdk.VoidAsync(new(), x.Data.id, config, Guid.NewGuid().ToString(),
                //     CancellationToken.None);

                var capReq = new RapydCaptureRequest()
                {
                    amount = 92,
                    receipt_email = "<EMAIL>",
                    statement_descriptor = "captured"
                };

                var y = await _rapydSdk.CaptureAsync(capReq,
                    x.Data.id, config, Guid.NewGuid().ToString(),
                    CancellationToken.None);

                var rp = await _rapydSdk.RetrievePayment(y.Data.id, config, Guid.NewGuid().ToString(),
                    CancellationToken.None);

                var refundReq = new RapydRefundRequest()
                {
                    amount = 92,
                    currency = "EUR",
                    reason = "refund",
                    ewallets = null,
                    merchant_reference_id = Guid.NewGuid().ToString(),
                    payment = y.Data.id
                };
                var rrf = await _rapydSdk.RefundAsync(refundReq,
                    config, Guid.NewGuid().ToString(),
                    CancellationToken.None);
            }
            catch (Exception e)
            {
                var x = 1;
            }


            // var methods = await _rapydSdk.ListPaymentMethods(gateway, "GB", null, default);

            // var methods=await  _rapydSdk.ListPaymentMethods(gateway, "GB", null, default);
            // var fielkds = await _rapydSdk.GetRequiredFields(gateway, "gb_mastercard_card", default);

            //gb_mastercard_card

            return Ok();
        }

        [HttpGet("TestCheckout")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestCheckout()
        {
            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.IsActive && x.SupportedGateway.Sandbox &&
                    x.NameIdentifier == GatewayTypesConstants.Checkout);

            var authorizationRequest = new AuthorizationRequest()
            {
                CurrencyCode = "USD",
                Amount = 300,
                PayerId = Guid.NewGuid(),
                OrderId = Guid.NewGuid(),
                CreditCard = new SaleRequestCreditCard()
                {
                    FirstName = "DDD",
                    LastName = "AAA",
                    Number = "****************", // "****************", // "****************",//"4".PadRight(16, '1'),
                    //VerificationValue = "999",
                    Month = 10,
                    Year = 33
                },
                Gateway = gateway,
                IsCit = false,
                BillingAddress = new BillingAddress
                {
                    FirstName = "Sam",
                    LastName = "Grey",
                    Address1 = "oxford street, 1",
                    // Address2 = billingAddress.Address2,
                    City = "New York City",
                    State = "New York",
                    Zip = "12345",
                    Country = "US",
                    //PhoneNumber = billingAddress.Phone,
                    Email = "<EMAIL>"
                },
                // ShippingAddress = new ShippingAddressDTO
                // {
                //     Address1 = shippingAddress?.Address1,
                //     Address2 = shippingAddress?.Address2,
                //     City = shippingAddress?.City,
                //     State = shippingAddress?.State,
                //     Zip = shippingAddress?.Zip,
                //     Country = shippingAddress?.Country,
                //     PhoneNumber = shippingAddress?.Phone,
                // },
                // UseDynamicDescriptor = command.UseDynamicDescriptor,
                // Descriptor = descriptor,
                // IsRebilling = command.IsRebilling,
                // Device = new DeviceDetailsDTO
                // {
                //     IpAddress = command.Device?.IpAddress,
                //     UserAgent = command.Device?.UserAgent
                // },
            };

            var d = await _checkoutProvider.AuthorizeAsync(authorizationRequest, CancellationToken.None);
            // await _checkoutProvider.CaptureAsync(new()
            // {
            //     Amount = 8000,
            //     CurrencyCode = "USD",
            //     TransactionToken = d.ProviderTransactionToken,
            //     OrderId = d.OrderId,
            //     Mid = authorizationRequest.Mid,
            //     Gateway = authorizationRequest.Gateway,
            //     TransactionId = d.TransactionId
            // });
            return Ok(d);
        }

        [HttpGet("TestCards")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestCard(Guid mid, string cardNumber, string cvv, int month, int year,
            int amount)
        {
            var token = //default(MassTransit.Response<TokenizeInstrumentCommandResponse>);
                await _tokenizeInstrumentRequestClient
                    .GetResponse<TokenizeInstrumentCommandResponse>(
                        new TokenizeInstrumentCommand
                        {
                            Mid = mid, //"31e66a07-8882-4da8-a901-9fd460c55b24",//mid
                            FirstName = "John",
                            LastName = "Fed",
                            Number = cardNumber, //cardNumber,
                            VerificationValue = cvv, //cvv,
                            Month = month,
                            Year = year,
                            Address1 = "123 N CENTRAL", //"123 Main St",
                            City = "NYC",
                            State = "NY",
                            Zip = "12345",
                        }, CancellationToken.None);

            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.Merchant.Mid == mid && x.IsActive && x.SupportedGateway.Sandbox);

            try
            {
                // // //Auth capture refund
                // var authorizeCommandRes2 =
                //     await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoesStore", token);
                // var captureCommandRes2 = await Capture_Via_Command(mid, authorizeCommandRes2.TransactionId);
                // var creditPaymentCommandRes2 = await Refund_Via_Command(mid, amount, "USD",
                //     authorizeCommandRes2.OrderId, Guid.NewGuid(), captureCommandRes2.TransactionId);

                // // //auth void
                //  var authorizeCommandRes3 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoesStore", token);
                //  var voidPaymentCommandRes3 = await Void_Via_Command(mid, authorizeCommandRes3.TransactionId);
                // //
                // // //Auth Capture + void (Failed)
                //  var authorizeCommandRes4 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoesStore", token);
                // var captureCommandRes4 = await Capture_Via_Command(mid, authorizeCommandRes4.TransactionId);
                // var voidPaymentCommandRes4 = await Void_Via_Command(mid, captureCommandRes4.TransactionId);
                // //
                // // //Auth + void (Failed)
                //  var authorizeCommandRes5 = await Authorize_Via_Command(mid, gateway.Order.Value, amount, "USD", "MyShoesStore", token);
                //  var voidPaymentCommandRes5 = await Void_Via_Command(mid, authorizeCommandRes5.TransactionId);
                //
                // var voidPaymentCommandRes2 = await Void_Via_Command(mid, authorizeCommandRes5.TransactionId);


                await Authorize_Capture_Test(mid, gateway, amount, "USD", "MyXStore", "+14084594427", token.Message);
                // await Credit_Test(mid,Guid.NewGuid(),Guid.NewGuid(),Guid.NewGuid(),new Guid("2a6f5fd8-0c37-4524-8cd5-1686f4e689da"),gateway, 110, "USD", "FlexCharge1", "*********", token.Message);
                // await Authorize_Capture_Credit_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                // await Authorize_Void_Test(mid, gateway, amount, "USD", "MyShoesStore", null, token.Message);
                //await Authorize_Test(mid, gateway, amount, "USD", "MyShoesStore", null, token.Message);
                //await Authorize_Void_Test(mid, gateway, amount, "USD", "MyCustomShoes", "*********", token.Message);

                // await Sale_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                // await Sale_Test(mid, gateway,210,"USD", "FlexCharge6","*********",token.Message);
                // await Sale_Test(mid, gateway, 210, "USD", "FlexCharge6", "*********", token.Message);
                // await CascadingAuthorize(mid, 0, 111, "USD", "FlexCharge7", "*********", true, token.Message);
                //
                // await Verify(mid, gateway, "MyShoesStore", token.Message);
                // await Authoriza_Scheme_Transaction_Id(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);
                // await Authorize_SchemTransactionID(mid, gateway, 111, "USD", "FlexCharge7", "*********", true, token.Message);
                // await Authorize_W_Shipping_Void_Test(mid, gateway, amount, "USD", "MyShoesStore", "*********", token.Message);

                //await ACH_DEBIT_Test(mid);

                //test credit
                // await Credit_Test(mid, Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid(),
                //     new Guid("b54a7744-29aa-49aa-b979-983e3728d5f0")
                //     , gateway, amount, "USD", "FlexCharge1", "*********",
                //     token.Message);

                // var nmiSDKTEst = new NmiPaymentSdk("kyHpBft784kEmn57ka4qG59G3sGv54wW");
                // // Create a test transaction request
                // var transaction = new TransactionRequest
                // {
                //     Type = "auth",
                //     CcNumber = "****************",
                //     CcExp = "1223",
                //     Amount = 100.00m,
                //     FirstName = "John",
                //     LastName = "Doe",
                //     Address1 = "123 Main St",
                //     City = "Los Angeles",
                //     State = "CA",
                //     Zip = "90001",
                //     Country = "US"
                // };
                //
                // // Process the transaction
                // var response = await nmiSDKTEst.ProcessTransaction(transaction);
            }
            catch (Exception e)
            {
            }


            return Ok();
        }

        private async Task GetNextGatewayAsync_Visa_CIT(Guid mid)
        {
            int? previousGatewayOrder = null;
            string cardBrand = CardBrand.Visa.ToString();
            bool isCIT = true;
            Func<Gateway, bool> matchCondition = (gateway) => true;
            List<string> blocklistedGateways = null;
            CancellationToken token = default;

            // Act
            var result = await _paymentOrchestrator.GetNextGatewayAsync(mid, new OrchestrationOptions
            {
                IsCascadingPayment = null,
                IsRebilling = null,
                Order = null,
                Brand = CardBrand.Visa,
                IsCIT = true,
                BlocklistedProviders = null,
            }, null, token);
            //Console.WriteLine(JsonConvert.SerializeObject(result));
        }

        private async Task<FullyAuthorizePaymentCommandResponse> Authorize_Via_Command(Guid mid, int? gatewayOrder,
            int amount, string currency, string descriptor,
            MassTransit.Response<TokenizeInstrumentCommandResponse> token)
        {
            var res = await _fullyAuthorizePaymentRequestClient.GetResponse<FullyAuthorizePaymentCommandResponse>(
                new FullyAuthorizePaymentCommand
                {
                    IsCit = true,
                    Mid = mid,
                    OrderId = Guid.NewGuid(),
                    PayerId = Guid.NewGuid(),
                    PayerEmail = "gff.com",
                    PaymentInstrument = new PaymentInstrumentModel
                    {
                        ScaAuthenticationToken = null,
                        PaymentInstrumentToken = token.Message.Id.ToString(),
                        Country = "US",
                    },
                    //ScaAuthenticationToken = null,
                    Amount = amount,
                    Currency = currency,
                    UseDynamicDescriptor = true,
                    //DescriptorOverride = descriptor,
                    //DescriptorCityOverride = null,
                    GatewayOrder = gatewayOrder,
                    PerformCascadingPayment = true,
                    OverrideDuplicateOrderCheckTimespan = 0,
                    BillingAddress = SetBilling(),

                    Descriptor = new Descriptor
                    {
                        // Name = descriptor,
                        // City = "New York",
                        // Country = "USA",
                        // Postal = "10001",
                        // State = "NY",
                        // Address = "East 74th street",
                        // Phone = "**********",
                        // Url = "TestUrl"
                    },
                    //IsRebilling = true,
                    UseBillingAsShipping = true,
                    DeviceInformation = new DeviceInfo()
                    {
                        IpAddress = "0.0.0.0"
                    },

                    TryUseAccountUpdater = true
                });

            return res.Message;
        }

        private async Task<CapturePaymentCommandResponse> Capture_Via_Command(Guid mid, Guid transactionId)
        {
            var response = await _capturePaymentRequestClient.GetResponse<CapturePaymentCommandResponse>(
                new()
                {
                    Mid = mid,
                    TransactionId = transactionId
                });

            return response.Message;
        }

        private async Task<VoidPaymentCommandResponse> Void_Via_Command(Guid mid, Guid transactionId)
        {
            var response = await _voidPaymentRequestClient.GetResponse<VoidPaymentCommandResponse>(
                new VoidPaymentCommand
                {
                    Mid = mid,
                    TransactionId = transactionId,
                    OrderId = Guid.Empty
                });

            return response.Message;
        }

        private async Task<CreditPaymentCommandResponse> Refund_Via_Command(Guid mid, int amount, string currency,
            Guid orderId, Guid payerId, Guid transactionId)
        {
            var response = await _creditPaymentRequestClient.GetResponse<CreditPaymentCommandResponse>(
                new CreditPaymentCommand
                {
                    Mid = mid,
                    TransactionId = transactionId,
                    Reason = "Test refund",
                    OrderId = orderId,
                    PayerId = payerId,
                    CurrencyCode = currency,
                    AmountToRefund = Utils.Formatters.IntToDecimal(amount),
                });

            return response.Message;
        }

        private async Task<DebitPaymentCommandResponse> Debit_Via_Command(Guid mid, int? gatewayOrder,
            int amount, string currency, string descriptor,
            MassTransit.Response<TokenizeInstrumentCommandResponse> token)
        {
            var res = await _debitPaymentRequestClient.GetResponse<DebitPaymentCommandResponse>(
                new DebitPaymentCommand()
                {
                    Mid = mid,
                    OrderId = Guid.NewGuid(),
                    PayerId = Guid.NewGuid(),
                    //ProcessorCode = "firstdata5199",

                    PayerEmail = "<EMAIL>",

                    PaymentInstrument = new PaymentInstrumentModel
                    {
                        ScaAuthenticationToken = null,
                        PaymentInstrumentToken = token.Message.Id.ToString(),
                        Country = "US"
                    },

                    Amount = amount,
                    Currency = currency,
                    UseDynamicDescriptor = true,
                    GatewayOrder = gatewayOrder,
                    // PerformCascadingPayment = false,
                    OverrideDuplicateOrderCheckTimespan = 0,
                    BillingAddress = SetBilling(),
                    Descriptor = new Descriptor
                    {
                        Name = descriptor,
                        City = "MILPITAS",
                        Phone = "TestPhone",
                        Url = "TestUrl"
                    },
                    //IsRebilling = true,
                    UseBillingAsShipping = true,
                    DeviceInformation = new DeviceInfo()
                    {
                        IpAddress = "0.0.0.0"
                    },

                    TryUseAccountUpdater = true
                });

            return res.Message;
        }

        #region Seters

        //set billing address
        private Contracts.Common.BillingAddress SetBilling()
        {
            return new Contracts.Common.BillingAddress
            {
                FirstName = "Joe",
                LastName = "Fed",
                Country = "US",
                Address1 = "123 N CENTRAL",
                Address2 = "",
                City = "MILPITAS",
                Zip = "12345",
                State = "CA",
                Phone = "1800950"
            };
        }

        //set billingAddressDto
        private BillingAddress SetBillingAddressDTO()
        {
            return new BillingAddress
            {
                FirstName = "Joe",
                LastName = "Fed",
                Country = "US",
                Address1 = "123 Main St", //   "691 MILPITAS BLVD #212",
                Address2 = "",
                City = "NYC",
                Zip = "12345", //"95035",
                State = "NY",
                Email = "<EMAIL>"
            };
        }

        //set shipping address

        #endregion

        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Get(Guid mid, string publicToken, string accessToken, string accountId)
        {
            _logger.LogInformation(
                $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");

            //simulate schemeTransactionID
            //{
            //     "sessionToken": "{{sessionToken}}",
            //     "merchantId": "{{merchantId}}",
            //     "merchantSiteId": "{{merchantSiteId}}",
            //     "clientRequestId": "{{clientRequestId}}",
            //     "timeStamp": "{{timestamp}}",
            //     "checksum": "{{checksum}}",
            //     "Currency": "USD",
            //     "amount": "99",
            //     "isRebilling": "0",
            //     "paymentOption": {
            //         "card": {
            //             "cardNumber": "****************",
            //             "cardHolderName": "john smith",
            //             "expirationMonth": "12",
            //             "expirationYear": "25"
            //         }
            //     },
            //     "billingAddress": {
            //         "firstName": "John",
            //         "lastName": "Smith",
            //         "address": "340689 main St.",
            //         "city": "London",
            //         "country": "GB",
            //         "email": "<EMAIL>"
            //     },
            //     "deviceDetails": {
            //         "ipAddress": "**************"
            //     }
            // }


            var token =
                await _tokenizeInstrumentRequestClient
                    .GetResponse<TokenizeInstrumentCommandResponse>(
                        new TokenizeInstrumentCommand
                        {
                            Mid = mid,
                            FirstName = "Guy",
                            LastName = "Vatman",
                            Number = "4444333322221111",
                            VerificationValue = "111",
                            Month = 1,
                            Year = 25
                        }, CancellationToken.None);

            var gateway =
                await _dbContext.Gateways.FirstOrDefaultAsync(x =>
                    x.Merchant.Mid == mid && x.SupportedGateway.Sandbox &&
                    x.NameIdentifier == GatewayTypesConstants.Nuvei);

            //await ACH_DEBIT_Test(mid);
            //await Authorize_Capture_Credit_Test(mid, gateway, 100, "USD", "FlexCharge1", "*********", token.Message);
            //await Credit_Test(mid,Guid.NewGuid(),Guid.NewGuid(),Guid.NewGuid(),new Guid("2a6f5fd8-0c37-4524-8cd5-1686f4e689da"),gateway, 110, "USD", "FlexCharge1", "*********", token.Message);
            //await Authorize_Capture_Credit_Test(mid, gateway, 110, "USD", "FlexCharge_AC", "*********", token.Message);
            //await Authorize_Void_Test(mid, gateway, 120, "USD", "FlexCharge_AV", "*********", token.Message);
            //await Authorize_Void_Test(mid, gateway,130,"USD","FlexCharge4","*********", token.Message);
            //await Authorize3d_Test(mid, gateway,20,"USD", "FlexCharge5","*********",token.Message);
            //await Sale_Test(mid, gateway, 200, "USD", "Test Desc", "*********", token.Message);
            //await Sale_Test(mid, gateway,210,"USD", "FlexCharge6","*********",token.Message);
            //await Sale_Test(mid, gateway, 210, "USD", "FlexCharge6", "*********", token.Message);
            // await CascadingAuthorize(mid, 0, 122, "USD", "FlexCharge7", "*********", true,
            //     token.Message);


            //await _payoutService.ExecutePayouts();
            //
            // await _payoutService.CheckPayoutStatus();

            #region other

            //Plaid tests
            // if (!string.IsNullOrEmpty(accessToken))
            //     await _obServices.SetAccessToken(accessToken);
            // else
            //     await _obServices.ExchangePublicToken(publicToken);
            //
            // var accountToken = await _obServices.CreateProcessorToken(accountId);
            // await _obServices.Identity();
            // await _obServices.Balance();
            // await _obServices.Auth();
            // await _obServices.Accounts();
            // await _obServices.GetTransactions(DateOnly.MinValue, DateOnly.MaxValue);

            // await _publishEndpoint.Publish<Contracts.Payouts.PayoutRequestedEvent>(new
            // {
            //     PayoutOrders = new List<PayoutOrderItem>
            //     {
            //         new PayoutOrderItem()
            //         {
            //             
            //         }
            //     }
            // });

            // await _publishEndpoint.Publish<Contracts.ApplicationConvertedEvent>(new
            // {
            //     MerchantId = new Guid("76e105a7-4d60-457b-82f2-648d46aba287")
            // });
            // var merchant = await _dbContext.Merchants.SingleAsync(x=>x.Mid == new Guid("76e105a7-4d60-457b-82f2-648d46aba287"));
            //     
            // if (string.IsNullOrWhiteSpace(merchant.Spreedly3dsMerchantProfileKey) ||
            //     string.IsNullOrWhiteSpace(merchant.Spreedly3dsScaProviderKey))
            // {
            //     await _applicationConvertedEventConsumer.Add3DSSupportAsync(merchant,
            //         merchant.SpreedlyEnvironmentKey, merchant.SpreedlySecretKey,
            //         _publishEndpoint, CancellationToken.None);
            // }    

            //await _publishEndpoint.Publish<InitiateMonthlyPayoutCommand>(new {});

            #endregion


            return Ok();
        }

        #region Card Payments

        private async Task Sale_Test(Guid mid, Gateway gateway, int amount, string currency, string descriptorName,
            string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var res = await _paymentOrchestrator.SaleAsync(new SaleRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                Token = token.Id.ToString(),
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = token.Message.CardHolderFirstName,
                //     LastName = token.Message.CardHolderLastName,
                //     Number = null,
                //     VerificationValue = null,
                //     Month = token.Message.ExpirationMonth,
                //     Year = token.Message.ExpirationYear
                // },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = "12",
                //     Year = "2029"
                // },
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "************"
                },
                ShippingAddress = null,
                ThreeDS = new ThreeDsecureDTO
                {
                    EcommerceIndicator = "02",
                    AuthenticationValue = "ejJRWG9SWWRpU2I1M21DelozSXU",
                    DirectoryServerTransactionId = Guid.NewGuid().ToString()
                },
            }, gateway: gateway, token: CancellationToken.None);
        }

        private async Task CascadingAuthorize(Guid mid,
            int gatewayOrder,
            int amount,
            string currency,
            string descriptorName,
            string descriptorPhone,
            bool performCascadingPayment,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                IsRebilling = false,
                // NetworkReferenceData = new NetworkReferenceData
                // {
                //     TransactionId = "303152204895680",
                //     Brand = "VISA"
                // },
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
                ShippingAddress = null,
            }, new OrchestrationOptions
            {
                IsCascadingPayment = performCascadingPayment,
                Order = gatewayOrder,
            }, gateway: null, token: CancellationToken.None);
        }

        private async Task Authorize_SchemTransactionID(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName,
            string descriptorPhone,
            bool performCascadingPayment,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            // var gateway = performCascadingPayment
            //     ? await _paymentOrchestrator.GetNextGateway(mid, gatewayOrder, x => x.IsSandbox,
            //         CancellationToken.None)
            //     : await _paymentOrchestrator.GetGatewayAsync(mid, gatewayOrder, CancellationToken.None);

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                //IsRebilling = false,
                // NetworkReferenceData = new NetworkReferenceData
                // {
                //     TransactionId = "303152204895680",
                //     Brand = "VISA"
                // },
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
                ShippingAddress = null,
            }, new OrchestrationOptions
            {
                IsCascadingPayment = performCascadingPayment
            }, gateway: null, token: CancellationToken.None);
        }

        private async Task Authorize3d_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var res = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone,
                    City = "NYC"
                },
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "**************"
                },
                ShippingAddress = null,
                IsCit = true
                // ThreeDS = new ThreeDsecureDTO
                // {
                //     EcommerceIndicator = "2",
                //     ThreeDsVersion = "2",
                //     AuthenticationValue = "ejJRWG9SWWRpU2I1M21DelozSXU=",
                //     DirectoryServerTransactionId = "9e6c6e9b-b390-4b11-ada9-0a8f595e8600"
                // }
            }, gateway: gateway, token: CancellationToken.None);
        }

        private async Task Authorize_Void_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                //IsRebilling = true,
                // NetworkReferenceData = new NetworkReferenceData
                // {
                //     TransactionId = "887001863998888",
                //     Brand = "VISA"
                // },
                Token = token.Id.ToString(),
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = token.Message.CardHolderFirstName,
                //     LastName = token.Message.CardHolderLastName,
                //     Number = null,
                //     VerificationValue = null,
                //     Month = token.Message.ExpirationMonth,
                //     Year = token.Message.ExpirationYear
                // },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = "12",
                //     Year = "2029"
                // },
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                ShippingAddress = new ShippingAddress()
                {
                    Country = "US",
                    Address1 = "691 MILPITAS BLVD #212",
                    Address2 = "",
                    City = "MILPITAS",
                    Zip = "95035",
                    State = "CA",
                },
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
            }, new OrchestrationOptions
            {
                IsCascadingPayment = true,
                IsRebilling = false,
                Brand = CardBrand.Visa,
                IsCIT = false,
                BlocklistedProviders = null
            }, token: CancellationToken.None);

            var voidResult = await _paymentOrchestrator.VoidAsync(new VoidPaymentRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                TransactionId = authResult.TransactionId,
                PaymentInstrumentId = authResult.PaymentInstrumentId
            }, token: CancellationToken.None);
        }

        private async Task Authorize_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var merchant = await _dbContext.Merchants.FirstOrDefaultAsync(x => x.Mid == mid);

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone,
                    Country = merchant.Descriptor_Country
                },
                IsCit = true,
                //IsRebilling = true,
                // NetworkReferenceData = new NetworkReferenceData
                // {
                //     TransactionId = "887001863998888",
                //     Brand = "VISA"
                // },
                Token = token.Id.ToString(),
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = token.Message.CardHolderFirstName,
                //     LastName = token.Message.CardHolderLastName,
                //     Number = null,
                //     VerificationValue = null,
                //     Month = token.Message.ExpirationMonth,
                //     Year = token.Message.ExpirationYear
                // },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = "12",
                //     Year = "2029"
                // },
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                ShippingAddress = new ShippingAddress()
                {
                    Country = "US",
                    Address1 = "691 MILPITAS BLVD #212",
                    Address2 = "",
                    City = "MILPITAS",
                    Zip = "95035",
                    State = "CA",
                },
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
            }, new OrchestrationOptions
            {
                PaymentRoutingStrategy = PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString(),
                IsCascadingPayment = false,
                IsRebilling = false,
                PreviousOrder = null,
                Order = 0,
                Currency = "USD",
                Country = "USD",
                IsCIT = true,
                BlocklistedProviders = null
            }, gateway: gateway, token: CancellationToken.None);
        }

        private async Task Authorize_W_Shipping_Void_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = true,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                ShippingAddress = null,
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = null
                }
            }, gateway: gateway, token: CancellationToken.None);

            // var voidResult = await _paymentOrchestrator.VoidAsync(new VoidPaymentRequest
            // {
            //     Mid = mid,
            //     OrderId = orderId,
            //     PayerId = payerId,
            //     Gateway = gateway,
            //     TransactionId = authResult.TransactionId,
            //     PaymentInstrumentId = authResult.PaymentInstrumentId
            // }, gateway, token: CancellationToken.None);
        }

        private async Task Authoriza_Scheme_Transaction_Id(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = true,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                IsRebilling = false,
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                ShippingAddress = null,
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                }
            }, gateway: gateway, token: CancellationToken.None);

            var voidResult = await _paymentOrchestrator.VoidAsync(new VoidPaymentRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                TransactionId = authResult.TransactionId,
                PaymentInstrumentId = authResult.PaymentInstrumentId
            }, token: CancellationToken.None);
        }

        private async Task Authorize_Capture_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                IsCit = true,
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                Token = token.Id.ToString(),
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = token.Message.CardHolderFirstName,
                //     LastName = token.Message.CardHolderLastName,
                //     Number = null,
                //     VerificationValue = null,
                //     Month = token.Message.ExpirationMonth,
                //     Year = token.Message.ExpirationYear
                // },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = "12",
                //     Year = "2029"
                // },
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
                ShippingAddress = null,
            }, new OrchestrationOptions
            {
                PaymentRoutingStrategy = PaymentRoutingStrategyTypes.WeightedLoadBalancing.ToString(),
                IsCascadingPayment = false,
                IsRebilling = false,
                PreviousOrder = null,
                Order = 0,
                Currency = "USD",
                Country = "USD",
                IsCIT = true,
                BlocklistedProviders = null,
                AlreadyTriedProviders = new List<(Guid SupportedGatewayId, DateTime? Time)>()
                {
                    (new("1ee5b3ee-86d7-45f9-9405-23c96991d01e"), DateTime.UtcNow)
                }
            }, gateway: gateway, token: CancellationToken.None);

            var captureResult = await _paymentOrchestrator.CaptureAsync(new CapturePaymentRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                CurrencyCode = currency,
                TransactionId = authResult.TransactionId,
                PaymentInstrumentId = authResult.PaymentInstrumentId
            }, token: CancellationToken.None);
        }

        private async Task Authorize_Capture_Credit_Test(Guid mid, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.AuthorizeAsync(new AuthorizationRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                FeeAmount = 0,
                CurrencyCode = currency,
                UseDynamicDescriptor = false,
                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    Phone = descriptorPhone
                },
                Token = token.Id.ToString(),
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = token.Message.CardHolderFirstName,
                //     LastName = token.Message.CardHolderLastName,
                //     Number = null,
                //     VerificationValue = null,
                //     Month = token.Message.ExpirationMonth,
                //     Year = token.Message.ExpirationYear
                // },
                // CreditCard = new SaleRequestCreditCard
                // {
                //     FirstName = "Guy",
                //     LastName = "Vat",
                //     Number = "****************",
                //     VerificationValue = "123",
                //     Month = "12",
                //     Year = "2029"
                // },
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
                ShippingAddress = null,
            }, gateway: gateway, token: CancellationToken.None);


            var captureResult = await _paymentOrchestrator.CaptureAsync(new CapturePaymentRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Amount = amount,
                CurrencyCode = currency,
                TransactionId = authResult.TransactionId,
                PaymentInstrumentId = authResult.PaymentInstrumentId
            }, token: CancellationToken.None);

            var creditResult = await _paymentOrchestrator.CreditAsync(new CreditPaymentRequest()
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Gateway = gateway,
                Amount = amount,
                CurrencyCode = currency,
                PaymentInstrumentId = authResult.PaymentInstrumentId,
                TransactionId = captureResult.TransactionId
            }, gateway, token: CancellationToken.None);
        }

        #region TokenEx Tests

        [HttpPost("TestWebHooks")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Test_Webhooks(CancellationToken token
        )
        {
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
            foreach (var provider in _paymentProviders)
            {
                var res = await provider.CanHandleWebhookEventAsync(json, HttpContext.Request.Headers, token);
                if (res.CanHandle) break;
            }

            return Ok();
        }


        [HttpPost($"/{nameof(Tokenex_Test)}")]
        public async Task<ActionResult> Tokenex_Test(CancellationToken token)
        {
            Guid merchantId = Guid.Parse("76e105a7-4d60-457b-82f2-648d46aba287");

            var cardNumber = "****************";
            var supportedVersions = await _tokenExService.PreAuthenticateAsync(merchantId, cardNumber, token);

            var threeDSecureResponse = supportedVersions.threeDSecureResponse;
            if (threeDSecureResponse != null)
            {
                var transactionId = threeDSecureResponse.threeDSServerTransID;
                var url = threeDSecureResponse.threeDSMethodURL;
                var threeDsVersionToUse = supportedVersions.recommended3dsVersion;
                var dsServerId = supportedVersions.recommendedDirectoryServiceIdentifier;

                MethodCompletionIndicator methodCompletionIndicator;

                //3DS Client-side fingerprinting requested?
                if (!string.IsNullOrWhiteSpace(threeDSecureResponse.threeDSMethodURL))
                {
                    methodCompletionIndicator = MethodCompletionIndicator.ThreeDSMethodSuccessfullyCompleted;
                    //methodCompletionIndicator = MethodCompletionIndicator.ThreeDSMethodUnSuccessful;
                }
                else
                {
                    methodCompletionIndicator = MethodCompletionIndicator.ResultUnavailable;
                }

                var authenticateResponse = await _tokenExService.Authenticate3dsAsync(merchantId,
                    methodCompletionIndicator,
                    dsServerId,
                    threeDsVersionToUse, DeviceChannel.Browser,
                    MessageCategory.PaymentAuthentication, RequestorAuthenticationIndicator.PaymentTransaction,
                    TransactionType.GoodsServicePurchase,
                    new CardDetails(
                        Number: "****************",
                        CardExpiryDate: "2112",
                        AccountType: 2
                    ),
                    new CardholderDetails(
                        Name: "Bobby Tables",
                        EmailAddress: "<EMAIL>"
                        //TODO: BILLING SHIPPING ETC
                    ),
                    // PurchaseDetails.CreateSinglePurchase(
                    //     amount: 1000,
                    //     Currency: "USD",
                    //     purchaseTime: new DateTime(2021, 12, 01, 17, 14, 25)
                    //),
                    PurchaseDetails.CreateRecurringTransaction(
                        amount: 1000,
                        currency: "USD",
                        purchaseTime: new DateTime(2021, 12, 01, 17, 14, 25),
                        new DateTime(2025, 12, 01),
                        30
                    ),
                    new BrowserInfo(
                        AcceptHeaders: "*/*",
                        IpAddress: "**************",
                        JavascriptEnabled: false,
                        Language: "en-us",
                        ColorDepth: "3",
                        ScreenHeight: "1080",
                        ScreenWidth: "1920",
                        TimeZone: "300",
                        UserAgent: "Cardholder Browser UserAgent"
                    ),
                    await NgrokHelper.GetNgrokWebhooksDomainAsync() + "/handle_challenge_result_notification",
                    ChallengeWindowSize.FullScreen,
                    token);

                return Ok(authenticateResponse);
            }

            return Ok();
        }

        #endregion

        private async Task<IVerifyInstrumentResult> Verify(Guid mid, Gateway gateway,
            string descriptorName,
            TokenizeInstrumentCommandResponse token)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var authResult = await _paymentOrchestrator.VerifyAsync(new VerifyInstrumentRequest()
            {
                Mid = mid,
                Gateway = gateway,
                OrderId = orderId,
                PayerId = payerId,
                Amount = 0,
                FeeAmount = 0,
                CurrencyCode = "USD",
                UseDynamicDescriptor = false,
                IsCit = true,

                Descriptor = new DescriptorDTO
                {
                    Name = descriptorName,
                    //Phone = descriptorPhone
                },
                IsRebilling = true,
                // NetworkReferenceData = new NetworkReferenceData
                // {
                //     TransactionId = "887001863998888",
                //     Brand = "VISA"
                // },
                Token = token.Id.ToString(),
                PaymentInstrumentId = default,
                ScaAuthenticationToken = null,
                DuplicateCheckInSeconds = null,
                BillingAddress = SetBillingAddressDTO(),
                ShippingAddress = new ShippingAddress()
                {
                    Country = "US",
                    Address1 = "691 MILPITAS BLVD #212",
                    Address2 = "",
                    City = "MILPITAS",
                    Zip = "95035",
                    State = "CA",
                },
                Device = new DeviceDetailsDTO()
                {
                    IpAddress = "0.0.0.0"
                },
            }, gateway: gateway, token: CancellationToken.None);
            return authResult;
        }

        private async Task Credit_Test(Guid mid, Guid orderId, Guid payerId, Guid paymentInstrumentId,
            Guid transactionId, Gateway gateway, int amount, string currency,
            string descriptorName, string descriptorPhone,
            TokenizeInstrumentCommandResponse token)
        {
            var creditResult = await _paymentOrchestrator.CreditAsync(new CreditPaymentRequest()
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Gateway = gateway,
                Amount = amount,
                CurrencyCode = currency,
                PaymentInstrumentId = paymentInstrumentId,
                TransactionId = transactionId
            }, gateway, token: CancellationToken.None);

            Console.WriteLine(creditResult);
        }

        #endregion

        #region ACH

        [HttpGet("test-ach")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestAch(Guid? mid, Guid? orderId, string? publicToken)
        {
            // ACH tests

            #region ACH Tests

            //var result = await ACH_DEBIT_Test(mid.Value, orderId ?? Guid.NewGuid());
            //var result = await ACH_CREDIT_Test(mid.Value, null);
            //var cancelResult = await ACH_Cancel_Test(result.TransactionId, mid.Value);

            #endregion

            //var result = await ACH_VerifiedCredit_Test(mid.Value, null);

            #region Partner ACH Tests

            var result = await ACH_Partner_Debit();
            //var result = await ACH_Partner_Credit();
            //var cancelResult = await ACH_Partner_Cancel(result.TransactionId);

            #endregion


            //await ACH_Debit();
            //await ACH_Verified_Debit_Test(publicToken);
            // await ACH_query_transfer_test();

            //var result = await ACH_Verified_Credit_Test(mid.Value);


            return Ok(result);
        }

        [HttpGet("test-ach-refund")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestAchRefund(Guid mid, Guid transactionId, int amount)
        {
            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            var transaction = await _dbContext.Transactions.FirstOrDefaultAsync(x => x.Id == transactionId);

            if (transaction == null)
                return NotFound();

            if (transaction.PaymentType != nameof(PaymentMethodType.Ach))
                return BadRequest("Transaction is not ACH");

            var refundResult = await Refund_Via_Command(mid, amount, "USD", orderId,
                payerId,
                transactionId);

            return Ok(refundResult);
        }

        private async Task<AchCancelResult> ACH_Cancel_Test(Guid transactionId, Guid mid)
        {
            // var cancelRes =
            //     await _svbServices.VerifiedCancel(Guid.Parse(resss.results[0].id), Guid.Empty, CancellationToken.None);

            var res = await _paymentOrchestrator.AchCancelAsync(new AchCancelPaymentRequest
            {
                IsVerifiedAch = false,
                Mid = mid,
                OrderId = Guid.NewGuid(),
                TransactionId = transactionId
            });

            return res;
        }

        private async Task ACH_Verified_Cancel_Test(Guid transactionId)
        {
            // var cancelRes =
            //     await _svbServices.VerifiedCancel(Guid.Parse(resss.results[0].id), Guid.Empty, CancellationToken.None);
        }

        private async Task ACH_query_transfer_test()
        {
            await _svbServices.QueryTransfersAsync(new TransfersInfoRequest
            {
                Offset = 10,
                Limit = 1000,
            }, CancellationToken.None);
        }

        private async Task ACH_Verified_Debit_Test(string publicToken)
        {
            await _obServices.SetRelatedMidAsync(Guid.Parse("036e7009-bc04-41bf-8410-6f9195e7c891"));

            await _obServices.ExchangePublicTokenAsync(publicToken);
            var processorToken = await _obServices.GenerateProcessorTokenAsync("vPRbr5eAZyUq554AKRKrhnJ6EQDG3DtnyvqE4");

            var resss = await _svbServices.VerifiedDebitAsync(new VerifiedTransferRequest()
            {
                BatchDetails = new BatchDetails
                {
                    SvbAccountNumber = "**********",
                    Direction = DirectionEnum.DEBIT.ToString(),
                    SecCode = SecCodeEnum.PPD.ToString(),
                    SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                    CompanyEntryDescription = "A123456789",
                    EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                    Currency = "USD"
                },
                Transfers = new List<VerifiedTransfer>
                {
                    new()
                    {
                        Amount = 555,
                        IdentificationNumber = "000015234AB",
                        CounterpartyName = "Michael Clarke",
                        ProviderService = "PLAID",
                        ProviderId = processorToken //"processor-sandbox-cf30b4ca-7b1b-41f8-872e-faeb6a79211f",
                    }
                }
            }, CancellationToken.None);
        }

        #region Partner Ach

        private async Task<AchTransferResult> ACH_Partner_Debit()
        {
            var pid = Guid.NewGuid();

            string Success_SimulationAccount = "**********";
            string R01_ErrorSimulationAccount = "****************";
            string R02_ErrorSimulationAccount = "****************";

            var res = await _partnerPaymentServices.AchDebitAsync(new AchPartnerTransferRequest
            {
                Pid = pid,
                ProviderNameIdentifier = "svb",
                Amount = 150,
                CurrencyCode = "USD",
                Currency = "USD",
                IsVerifiedAch = false,
                IdentificationNumber = "123123",
                Processor = GatewayTypesConstants.Svb,
                //Provider = OpenBankingProviders.Plaid,
                CompanyEntryDescription = "FLX ACH",
                EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                SecType = SecCodeEnum.PPD,
                Sender = new AchAccountDTO
                {
                    Id = default,
                    Name = "Michael Clarke",
                    //InstitutionName = "SVB",
                    InstitutionName = "JP Morgan Chase Bank",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = Success_SimulationAccount,
                    //AccountNumber = R01_ErrorSimulationAccount,
                    //AccountNumber = R02_ErrorSimulationAccount,
                    RoutingNumber = "*********",
                },
                Receiver = new AchAccountDTO
                {
                    Id = default,
                    Name = "FlexCharge",
                    InstitutionName = "SVB",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = "**********",
                    RoutingNumber = "*********",
                }
            });

            // var queryResult = await _svbAchService.QueryTransferAsync(res.ProviderTransactionToken, null,
            //     mid, CancellationToken.None);

            return res;
        }

        private async Task<AchTransferResult> ACH_Partner_Credit()
        {
            var pid = Guid.NewGuid();

            string Success_SimulationAccount = "**********";
            string R01_ErrorSimulationAccount = "****************";
            string R02_ErrorSimulationAccount = "****************";

            var res = await _partnerPaymentServices.AchCreditAsync(new AchPartnerTransferRequest
            {
                Pid = pid,
                ProviderNameIdentifier = "svb",
                Amount = 150,
                CurrencyCode = "USD",
                Currency = "USD",
                IsVerifiedAch = false,
                IdentificationNumber = "123123",
                Processor = GatewayTypesConstants.Svb,
                //Provider = OpenBankingProviders.Plaid,
                CompanyEntryDescription = "FLX ACH",
                EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                SecType = SecCodeEnum.PPD,
                Sender = new AchAccountDTO
                {
                    Id = default,
                    Name = "Michael Clarke",
                    //InstitutionName = "SVB",
                    InstitutionName = "JP Morgan Chase Bank",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = Success_SimulationAccount,
                    //AccountNumber = R01_ErrorSimulationAccount,
                    //AccountNumber = R02_ErrorSimulationAccount,
                    RoutingNumber = "*********",
                },
                Receiver = new AchAccountDTO
                {
                    Id = default,
                    Name = "FlexCharge",
                    InstitutionName = "SVB",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = "**********",
                    RoutingNumber = "*********",
                }
            });

            // var queryResult = await _svbAchService.QueryTransferAsync(res.ProviderTransactionToken, null,
            //     mid, CancellationToken.None);

            return res;
        }

        private async Task<AchCancelResult> ACH_Partner_Cancel(Guid transactionId)
        {
            // var cancelRes =
            //     await _svbServices.VerifiedCancel(Guid.Parse(resss.results[0].id), Guid.Empty, CancellationToken.None);

            var res = await _partnerPaymentServices.AchCancelAsync(new AchPartnerCancelPaymentRequest()
            {
                IsVerifiedAch = false,
                TransactionId = transactionId
            });

            return res;
        }

        #endregion


        private async Task ACH_Debit()
        {
            await _svbServices.DebitAsync(new TransferRequest
            {
                BatchDetails = new BatchDetails
                {
                    SvbAccountNumber = "**********",
                    Direction = DirectionEnum.CREDIT.ToString(),
                    SecCode = SecCodeEnum.CCD.ToString(),
                    SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                    CompanyEntryDescription = "A123456789",
                    EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                    Currency = "USD"
                },
                Transfers = new List<Transfer>
                {
                    new()
                    {
                        Amount = 100,
                        IdentificationNumber = "000015234AB",
                        ReceiverAccountNumber = "2222220",
                        ReceiverAccountType = AccountTypeEnum.CHECKING.ToString(),
                        ReceiverName = "George Washington",
                        ReceiverRoutingNumber = "*********"
                    },
                    new()
                    {
                        Amount = 150,
                        IdentificationNumber = "000065234OP",
                        ReceiverAccountNumber = "7676766",
                        ReceiverAccountType = AccountTypeEnum.SAVINGS.ToString(),
                        ReceiverName = "Michael Clarke",
                        ReceiverRoutingNumber = "*********"
                    }
                }
            }, CancellationToken.None);
        }

        private async Task<AchTransferResult> ACH_DEBIT_Test(Guid mid, Guid orderId)
        {
            var gateway = await _dbContext.Gateways
                .Include(x => x.Merchant)
                .Where(x =>
                    x.Merchant.Mid == mid &&
                    x.NameIdentifier.ToLower() == GatewayTypesConstants.Svb)
                .SingleOrDefaultAsync();

            var payerId = Guid.NewGuid();

            string Success_SimulationAccount = "**********";
            string R01_ErrorSimulationAccount = "****************";
            string R02_ErrorSimulationAccount = "****************";

            var res = await _paymentOrchestrator.AchDebitAsync(new AchTransferRequest
                {
                    Mid = mid,
                    OrderId = orderId,
                    PayerId = payerId,
                    Amount = 150,
                    FeeAmount = 0,
                    CurrencyCode = "USD",
                    Currency = "USD",
                    UseDynamicDescriptor = true,
                    // DeviceDetails = new DeviceDetails()
                    // {
                    //     IpAddress = "0.0.0.0"
                    // },
                    RetainInstrument = true,
                    IsVerifiedAch = false,
                    IdentificationNumber = "123123",
                    Processor = GatewayTypesConstants.Svb,
                    //Provider = OpenBankingProviders.Plaid,
                    CompanyEntryDescription = "FLX ACH",
                    EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                    SecType = SecCodeEnum.PPD,
                    Sender = new AchAccountDTO
                    {
                        Id = default,
                        Name = "Michael Clarke",
                        //InstitutionName = "SVB",
                        InstitutionName = "JP Morgan Chase Bank",
                        AccountType = AccountTypeEnum.CHECKING,
                        AccountNumber = Success_SimulationAccount,
                        //AccountNumber = R01_ErrorSimulationAccount,
                        //AccountNumber = R02_ErrorSimulationAccount,
                        RoutingNumber = "*********",
                    },
                    Receiver = new AchAccountDTO
                    {
                        Id = default,
                        Name = "FlexCharge",
                        InstitutionName = "SVB",
                        AccountType = AccountTypeEnum.CHECKING,
                        AccountNumber = "**********",
                        RoutingNumber = "*********",
                    }
                }, new OrchestrationOptions(),
                gateway: gateway);

            // var queryResult = await _svbAchService.QueryTransferAsync(res.ProviderTransactionToken, null,
            //     mid, CancellationToken.None);

            return res;
        }

        private async Task<AchTransferResult> ACH_CREDIT_Test(Guid mid, Guid? transactionToRefundId)
        {
            var gateway = await _dbContext.Gateways
                .Include(x => x.Merchant)
                .Where(x =>
                    x.Merchant.Mid == mid &&
                    x.NameIdentifier.ToLower() == GatewayTypesConstants.Svb)
                .SingleOrDefaultAsync();

            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            string Success_SimulationAccount = "**********";
            string R01_ErrorSimulationAccount = "****************";
            string R02_ErrorSimulationAccount = "****************";

            var creditRequest = new AchTransferRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Amount = 150,
                FeeAmount = 0,
                CurrencyCode = "USD",
                Currency = "USD",
                UseDynamicDescriptor = true,
                // DeviceDetails = new DeviceDetails()
                // {
                //     IpAddress = "0.0.0.0"
                // },
                RetainInstrument = true,
                IsVerifiedAch = false,
                IdentificationNumber = "123123",
                Processor = GatewayTypesConstants.Svb,
                //Provider = OpenBankingProviders.Plaid,
                CompanyEntryDescription = "FLX ACH",
                EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                SecType = SecCodeEnum.PPD,
                Receiver = new AchAccountDTO
                {
                    Id = default,
                    Name = "FlexCharge",
                    InstitutionName = "SVB",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = "**********",
                    RoutingNumber = "*********",
                },
                Sender = new AchAccountDTO
                {
                    Id = default,
                    Name = "Michael Clarke",
                    //InstitutionName = "SVB",
                    InstitutionName = "JP Morgan Chase Bank",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = Success_SimulationAccount,
                    //AccountNumber = R01_ErrorSimulationAccount,
                    //AccountNumber = R02_ErrorSimulationAccount,
                    RoutingNumber = "*********",
                }
            };

            AchTransferResult res;
            if (transactionToRefundId == null)
            {
                res = await _paymentOrchestrator.AchCreditAsync(creditRequest, gateway: gateway);
            }
            else
            {
                res = await _paymentOrchestrator.AchRefundAsync(creditRequest, transactionToRefundId.Value);
            }

            // var queryResult = await _svbAchService.QueryTransferAsync(res.ProviderTransactionToken, null,
            //     mid, CancellationToken.None);

            return res;
        }

        // private async Task<TransferResponse> ACH_Verified_Credit_Test(Guid mid)
        // {
        //     await _obServices.SetRelatedMidAsync(mid);
        //
        //     //await _obServices.ExchangePublicTokenAsync(publicToken);
        //     //var processorToken = await _obServices.GenerateProcessorTokenAsync("vPRbr5eAZyUq554AKRKrhnJ6EQDG3DtnyvqE4");
        //
        //     string processorToken = "access-sandbox-1141d2d7-9e70-437a-a7fb-e42adf11cba8";
        //
        //     var res = await _svbServices.VerifiedCreditAsync(new VerifiedTransferRequest()
        //     {
        //         BatchDetails = new BatchDetails
        //         {
        //             SvbAccountNumber = "**********",
        //             Direction = DirectionEnum.CREDIT.ToString(),
        //             SecCode = SecCodeEnum.PPD.ToString(),
        //             SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
        //             CompanyEntryDescription = "A123456789",
        //             EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
        //             Currency = "USD"
        //         },
        //         Transfers = new List<VerifiedTransfer>
        //         {
        //             new()
        //             {
        //                 Amount = 555,
        //                 IdentificationNumber = "000015234AB",
        //                 CounterpartyName = "Michael Clarke",
        //                 ProviderService = "PLAID",
        //                 ProviderId = processorToken //"processor-sandbox-cf30b4ca-7b1b-41f8-872e-faeb6a79211f",
        //             }
        //         }
        //     }, CancellationToken.None);
        //
        //
        //     return res;
        // }

        private async Task<AchTransferResult> ACH_VerifiedCredit_Test(Guid mid, Guid? transactionToRefundId)
        {
            await _obServices.SetRelatedMidAsync(mid);

            string processorToken =
                    "access-sandbox-8958efa0-6b5e-4ff2-b1c0-04c35faaff60"
                //"access-sandbox-2e147025-4439-42de-9225-035f0f422e42"
                //"access-sandbox-1141d2d7-9e70-437a-a7fb-e42adf11cba8"
                ;

            var gateway = await _dbContext.Gateways
                .Include(x => x.Merchant)
                .Where(x =>
                    x.Merchant.Mid == mid &&
                    x.NameIdentifier.ToLower() == GatewayTypesConstants.Svb)
                .SingleOrDefaultAsync();

            var orderId = Guid.NewGuid();
            var payerId = Guid.NewGuid();

            string Success_SimulationAccount = "**********";
            string R01_ErrorSimulationAccount = "****************";
            string R02_ErrorSimulationAccount = "****************";


            var creditRequest = new AchTransferRequest
            {
                Mid = mid,
                OrderId = orderId,
                PayerId = payerId,
                Amount = 150,
                FeeAmount = 0,
                CurrencyCode = "USD",
                Currency = "USD",
                UseDynamicDescriptor = true,
                // DeviceDetails = new DeviceDetails()
                // {
                //     IpAddress = "0.0.0.0"
                // },
                RetainInstrument = true,
                IsVerifiedAch = true,
                IdentificationNumber = "123123AAA",
                Processor = GatewayTypesConstants.Svb,
                Provider = OpenBankingProviders.Plaid,
                CompanyEntryDescription = "FLX ACH",
                EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                SecType = SecCodeEnum.PPD,
                Receiver = new AchAccountDTO
                {
                    Id = default,
                    Name = "Michael Clarke",
                    //InstitutionName = "SVB",
                    //InstitutionName = "JP Morgan Chase Bank",
                    //AccountType = AccountTypeEnum.CHECKING,
                    //AccountNumber = Success_SimulationAccount,
                    //AccountNumber = R01_ErrorSimulationAccount,
                    //AccountNumber = R02_ErrorSimulationAccount,
                    //RoutingNumber = "*********",
                    ProcessorToken = processorToken
                },
                Sender = new AchAccountDTO
                {
                    Id = default,
                    Name = "FlexCharge",
                    InstitutionName = "SVB",
                    AccountType = AccountTypeEnum.CHECKING,
                    AccountNumber = "**********",
                    //RoutingNumber = "*********",
                },
            };

            AchTransferResult res;
            if (transactionToRefundId == null)
            {
                res = await _paymentOrchestrator.AchCreditAsync(creditRequest, gateway: gateway);
            }
            else
            {
                res = await _paymentOrchestrator.AchRefundAsync(creditRequest, transactionToRefundId.Value);
            }

            // var queryResult = await _svbAchService.QueryTransferAsync(res.ProviderTransactionToken, null,
            //     mid, CancellationToken.None);

            return res;
        }

        #endregion

        #region PAYMENT CALCULATIONS

        [HttpGet("test-payment-calculations")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestPaymentCalculations(Guid orderId)
        {
            var orderTransactions = await _dbContext
                .Transactions.Where(x => x.OrderId == orderId)
                .ToListAsync();

            var orderPayments = new OrderPayments(orderTransactions);
            var refundable = orderPayments.TotalRefundableAmount();

            return Ok();
        }

        #endregion

        #region TEST DYNAMO DB

        // [HttpGet("test-dynamo")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestDynamo()
        // {
        //     using var workspan = Workspan.Start<TestController>();
        //
        //     // Set the maximum number of threads
        //     ThreadPool.SetMaxThreads(2, 2);
        //
        //     List<List<Task>> tasks = new();
        //
        //     // int timerCount = 0;
        //     // Timer timer = new Timer((state) =>
        //     // {
        //     //     workspan.Log.Information("Timer elapsed: {TimerCount}", timerCount++);
        //     // }, null, 0, 10);
        //
        //
        //     var sw = Stopwatch.StartNew();
        //
        //     int taskRows = 10;
        //     int tasksInRow = 10;
        //     int merchantsToUpdateInATask = 1000;
        //     for (int i = 0; i < taskRows; i++)
        //     {
        //         var parallelTasks = new List<Task>();
        //
        //         for (int j = 0; j < tasksInRow; j++)
        //         {
        //             parallelTasks.Add(null);
        //         }
        //
        //         ParallelOptions parallelOptions = new()
        //         {
        //             MaxDegreeOfParallelism = tasksInRow
        //         };
        //
        //         await Parallel.ForEachAsync(parallelTasks, parallelOptions,
        //             async (id, cancellationToken) =>
        //             {
        //                 var sw2 = Stopwatch.StartNew();
        //
        //                 List<Guid> supportedGatewayIds = new();
        //                 for (int k = 0; k < merchantsToUpdateInATask; k++)
        //                 {
        //                     supportedGatewayIds.Add(Guid.NewGuid());
        //                 }
        //
        //                 await UpdateMetricsTask(supportedGatewayIds, 10000);
        //
        //                 sw2.Stop();
        //                 workspan.Log.Information("One update took {Elapsed}", sw2.Elapsed.ToString("g"));
        //             });
        //     }
        //
        //
        //     sw.Stop();
        //     workspan.Log.Information("TestDynamo took {Elapsed}", sw.Elapsed.ToString("g"));
        //
        //     return Ok();
        //
        //
        //     #region Local functions
        //
        //     async Task UpdateMetricsTask(List<Guid> supportedGatewayIds, int amount)
        //     {
        //         try
        //         {
        //             AtomicUpdatesBuilder metrics = new AtomicUpdatesBuilder();
        //
        //             foreach (var supportedGatewayId in supportedGatewayIds)
        //             {
        //                 using (metrics.StartItemUpdate<ProvidersMetricsTable>(supportedGatewayId,
        //                            TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.Monthly,
        //                                DateTime.UtcNow)))
        //                 {
        //                     UpdateMetrics(metrics);
        //                 }
        //
        //                 // using (metrics.StartItemUpdate<ProvidersMetricsTable>(supportedGatewayId,
        //                 //            TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.All,
        //                 //                DateTime.UtcNow)))
        //                 // {
        //                 //     UpdateMetrics(metrics);
        //                 // }
        //             }
        //
        //             await metrics.UpdateAsync(_dynamoDb, false);
        //         }
        //         catch (Exception e)
        //         {
        //             workspan.RecordFatalException(e);
        //         }
        //
        //         void UpdateMetrics(AtomicUpdatesBuilder metrics)
        //         {
        //             metrics.Add(
        //                 nameof(ProvidersMetricsTable.TotalTransactionCount),
        //                 +1);
        //
        //             metrics.Add(
        //                 nameof(ProvidersMetricsTable.TotalTransactionAmount),
        //                 +amount);
        //
        //             switch ("5")
        //             {
        //                 case var bin when bin.StartsWith("5") || bin.StartsWith("2"): // MASTER CARD
        //                 {
        //                     metrics.Add(
        //                         nameof(ProvidersMetricsTable.MasterCardTransactionsCount),
        //                         +1);
        //
        //                     metrics.Add(
        //                         nameof(ProvidersMetricsTable.MasterCardTransactionsAmount),
        //                         +amount);
        //                     break;
        //                 }
        //                 case var bin when bin.StartsWith("4"): // VISA
        //                 {
        //                     metrics.Add(
        //                         nameof(ProvidersMetricsTable.VisaTransactionsCount),
        //                         +1);
        //
        //                     metrics.Add(
        //                         nameof(ProvidersMetricsTable.VisaTransactionsAmount),
        //                         +amount);
        //                     break;
        //                 }
        //                 default:
        //                     // Other card network -> nothing to do 
        //                     break;
        //             }
        //         }
        //     }
        //
        //     #endregion
        // }
        //
        // [HttpGet("test-dynamo-get-provider-statistics")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestGetPaymentProviderStatisticsFromDynamo(Guid supportedGatewayId)
        // {
        //     var dynamoDb = new DynamoDbClient(_dynamoDb);
        //     var statistics = await dynamoDb.GetItemAsync<ProvidersMetricsTable>(
        //         supportedGatewayId.ToString(),
        //         TimeRangeKeyFactory.CreateTimeRangeKey(TimeRangeKeyFactory.TimeRange.Monthly, DateTime.UtcNow),
        //         false);
        //
        //     return Ok(statistics);
        // }

        #endregion

        [HttpGet("test-sftp")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestSftp()
        {
            var sftpHost = "sftp.flex-charge.com";
            var username = "nautica";
            var password = "162019";
            // var password = "";
            var port = 22;
            // var pollingPath = "/inbound";
            var pollingPath = "/sftp-gateway-staging/nautica_76e105a7-4d60-457b-82f2-648d46aba287/inbound";

            var privateKeyPath =
                @"-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABBFKTmPnn
DCu/vnqhACP4g/AAAAGAAAAAEAAAAzAAAAC3NzaC1lZDI1NTE5AAAAIAXMJPfCiJL3BPNA
y6duzDQl1a7Kev0HR9QVVvm2w5pUAAAAsCdFtu1CdlYzCy8sBctZAC23w9r+IA99wFvjRj
Xrnh8eQSWn/XE+EMBirjs3p9GecX/w0uUBgaaL0E4mFFnLlWowETlE5uMb2yR7+lbSJ5FX
sf5SGrGo8GngBRaLFlQP0mlogNfFRyIGKnIq5EsDZleABCvT8xj8c9kWpDPHbHlNSuSHu+
RyU6Jpc/voo4ti5RQsAfcOShAPaCd+WauQpARhRrnd/g2czXeGgpmT7sP9
-----END OPENSSH PRIVATE KEY-----";

            var previouslyDownloadedFiles = new HashSet<string>();

            await _reportsService.PollReportsAsync();
            return Ok();
        }

        [HttpGet("test-sftp-download")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestSftpDownload()
        {
            await _reportsService.PollAndProcessS3DisputesFilesAsync();
            return Ok();
        }

        [HttpGet("fill-paysafe-config")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> FillPaysafeConfig()
        {
            var provider = await _dbContext.SupportedGateways
                .Include(x => x.ReportingConfigurations)
                .FirstOrDefaultAsync(x => x.Id == Guid.Parse("bb2e9d53-d109-4898-8dc1-883897108b49"));

            // var provider = await _dbContext.SupportedGateways
            //     .Include(x => x.ReportingConfigurations)
            //     .FirstOrDefaultAsync(x => x.Id == Guid.Parse("72f23f8b-85ca-4762-a57b-2f48c69cb7d2"));
            if (provider != null)
            {
                var reportingConfiguration = new ReportingConfiguration
                {
                    ProviderName = "Paysafe",
                    Source = "PaysafeAMRFraud",
                    AutoMatch = false,
                    SftpHost = "mercbatch.paysafe.com",
                    SftpUsername = "aFLEXCHARGE",
                    SftpPassword = "",
                    SftpPort = 22,
                    SftpPollingPath = "/outbound",
                    SftpFileNamePart = "AMR_Fraud_Report_by_Account_Group_218424",
                };

                // var reportingConfiguration = new ReportingConfiguration
                // {
                //     ProviderName = "Paysafe",
                //     Source = "PaysafeAMRFraud",
                //     AutoMatch = false,
                //     SftpHost = "sftp.flex-charge.com",
                //     SftpUsername ="nautica",
                //     SftpPassword = "",
                //     SftpPort = 22,
                //     SftpPollingPath = "/sftp-gateway-staging/nautica_76e105a7-4d60-457b-82f2-648d46aba287/inbound",
                // };

                provider.ReportingConfigurations = reportingConfiguration;
                await _dbContext.SaveChangesAsync();
            }

            return Ok();
        }

        [HttpGet("test-myrcvr-alerts")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestMyrcvrAlerts()
        {
            using var workspan = Workspan.Start<TestController>();

            try
            {
                // var url = "https://developer.myrcvr.com/api/sandbox/alerts";
                // var sandboxPrivateKey =
                //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6InNhbmRib3giLCJ1c2VyX25hbWUiOiJzYW5kYm94IiwiY2xpZW50SUQiOiJzYW5kYm94In0.Bft5V9UP_CGFpCqaowpQODEpaDIaNV2gztJmPIWVhXs";
                // await _myRcvrDisputeService.GetMyRcvrAlertsAsync(url, sandboxPrivateKey);

                await _reportsService.GetAlertReportsAsync();

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("send-production-kount-alert-webhooks")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> SendProductionKountAlertWebhooks(IFormFile formFile)
        {
            using var workspan = Workspan.Start<TestController>();

            try
            {
                using var httpClient = new HttpClient();

                var endpoint =
                    "https://4m8uwcypoc.execute-api.us-east-1.amazonaws.com/v1/payments/webhooks?provider=kount";

                //read file line by-line and send http request to an endpoint using HttpClient with each line as body
                using var reader = new StreamReader(formFile.OpenReadStream());
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    var putResponse = await httpClient.PostAsync(requestUri: endpoint,
                        new StringContent(line, Encoding.UTF8, "application/json"));

                    if (putResponse.IsSuccessStatusCode)
                    {
                        workspan.Log.Information("OK: {Alert}", line);
                    }
                    else
                    {
                        workspan.Log.Fatal("ERROR: {Alert}", line);
                    }

                    System.Threading.Thread.Sleep(300);
                }
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }

            return Ok();
        }

        [HttpGet("test-stripe-billing-portal-link")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeBillingPortalLink(string invoiceId)
        {
            using var workspan = Workspan.Start<TestController>();

            try
            {
                var subscriptionUpdateLink = await _stripeBillingPortalService.CreateUpdateSubscriptionLinkAsync(
                    "acct_1QeDMUCIySJgy9RD", new Guid("733b043b-3119-40cc-bc7d-bf346f48b762"),
                    Guid.NewGuid(), invoiceId);


                return Ok(subscriptionUpdateLink);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }


        [HttpGet("export-internal-response-code-mapping-to-csv")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> ExportInternalResponseCodeMappingToCsv()
        {
            using var workspan = Workspan.Start<TestController>();

            using var streamWriter = new StreamWriter("InternalResponseCodeMapping.csv");
            using var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture, false);

            var records = new List<InternalResponseRecord>();
            foreach (var key in InternalResponseMapper.IndexTable.Keys)
            {
                var mappedResponse = InternalResponseMapper.IndexTable[key];

                records.Add(new InternalResponseRecord(
                    mappedResponse.MappedResponseCode,
                    mappedResponse.MappedResponseMessage,
                    mappedResponse.MappedResponseGroup,
                    mappedResponse.MappedDeclineTypeResponse));
            }

            await csvWriter.WriteRecordsAsync(records);

            return Ok();
        }

        record InternalResponseRecord(
            string MappedResponseCode,
            string MappedResponseMessage,
            MappedResponseGroup? MappedResponseGroup,
            MappedDeclineTypeResponse? MappedDeclineTypeResponse);


        // [HttpGet("export-provider-response-code-mapping-to-csv")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> ExportProviderResponseCodeMappingToCsv()
        // {
        //     using var workspan = Workspan.Start<TestController>();
        //
        //     using var streamWriter = new StreamWriter("Stripe.csv");
        //     using var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture, false);
        //
        //     var records = new List<ProviderResponseToInternalResponseRecord>();
        //     
        //     foreach (var providerResponse in StripeResponseMapper.IndexTable.Keys)
        //     {
        //         var mappedResponse = StripeResponseMapper.IndexTable[providerResponse];
        //
        //         records.Add(new ProviderResponseToInternalResponseRecord()
        //         {
        //             ProviderResponseCode = providerResponse,
        //             ProviderResponseMessage = null,
        //             InternalResponseCode = mappedResponse.MappedResponseCode,
        //             InternalResponseMessage = mappedResponse.MappedResponseMessage
        //         });
        //     }
        //
        //     await csvWriter.WriteRecordsAsync(records);
        //
        //     return Ok();
        // }
    }
}
#endif