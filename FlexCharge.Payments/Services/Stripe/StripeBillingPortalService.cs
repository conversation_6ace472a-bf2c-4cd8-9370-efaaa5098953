using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.DistributedLock;
using FlexCharge.Payments.Entities;
using Microsoft.EntityFrameworkCore;
using Stripe;
using Stripe.BillingPortal;

namespace FlexCharge.Payments.Services.Stripe;

class StripeBillingPortalService : IStripeBillingPortalService
{
    //public Guid ProviderId => ProviderDescription.ProviderId;

    private readonly IActivityService _activityService;
    private readonly ISecretsManager _secretsManager;
    private IDistributedLockService _distributedLockService;
    private readonly PostgreSQLDbContext _dbContext;

    public StripeBillingPortalService(
        IActivityService activityService,
        ISecretsManager secretsManager,
        IDistributedLockService distributedLockService,
        PostgreSQLDbContext dbContext)
    {
        _activityService = activityService;
        _secretsManager = secretsManager;
        _distributedLockService = distributedLockService;
        _dbContext = dbContext;
    }

    public async Task<string> CreateUpdateSubscriptionLinkAsync(string providerAccountId, Guid mid, Guid orderId,
        string invoiceId)
    {
        using var workspan = Workspan.Start<StripeBillingPortalService>()
            .Baggage("Mid", mid)
            .Baggage("StripeAccount", providerAccountId)
            .Baggage("OrderId", orderId)
            .Baggage("InvoiceId", invoiceId)
            .LogEnterAndExit();

        //return await CreateHostedInvoicePaymentPageAsync(providerAccountId, paymentIntentId);

        return await CreateBillingPortalLinkAsync(mid, orderId, providerAccountId, invoiceId,
            BillingPortalConfiguration.SubscriptionsListWithUpdatePaymentMethodOption);
    }


    private async Task<string> CreateBillingPortalLinkAsync(Guid mid, Guid orderId, string stripeAccountId,
        string invoiceId, BillingPortalConfiguration billingPortalConfiguration)
    {
        using var workspan = Workspan.Start<StripeBillingPortalService>();

        Invoice? expandedInvoice = null;
        try
        {
            var billingPortalConfigurationId =
                await GetOrCreateBillingPortalConfigurationAsync(mid, billingPortalConfiguration, stripeAccountId);

            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = stripeAccountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };

            var invoiceService = new InvoiceService();
            var invoiceGetOptions = new InvoiceGetOptions();

            // StripeObjectHelpers.ExpandObjectProperties(invoiceGetOptions,
            //     $"{nameof(PaymentIntent.Invoice)}"
            // );

            var invoice =
                await invoiceService.GetAsync(invoiceId, invoiceGetOptions,
                    stripeRequestOptions);

            workspan
                .Tag("SubscriptionId", invoice.SubscriptionId)
                .Tag("InvoiceId", invoice.Id)
                .Tag("CustomerId", invoice.CustomerId)
                .Tag("ChargeId", invoice.ChargeId);

            //see: https://docs.stripe.com/api/customer_portal/sessions/create?lang=dotnet&shell=true&api=true&resource=invoices&action=list
            var sessionCreateOptions = new SessionCreateOptions
            {
                Configuration = billingPortalConfigurationId,
                Customer = invoice.CustomerId,
                //ReturnUrl = "https://example.com/account",

                //OnBehalfOf = providerAccountId; // Only for Stripe Connect?
            };

            if (!string.IsNullOrWhiteSpace(billingPortalConfiguration.FlowType))
            {
                sessionCreateOptions.FlowData = new SessionFlowDataOptions
                {
                    Type = billingPortalConfiguration.FlowType,

                    AfterCompletion = new SessionFlowDataAfterCompletionOptions
                    {
                        Type = billingPortalConfiguration.AfterCompletionType
                    }
                };

                if (billingPortalConfiguration.FlowType == "subscription_update")
                {
                    sessionCreateOptions.FlowData.SubscriptionUpdate = new SessionFlowDataSubscriptionUpdateOptions
                    {
                        Subscription = invoice.SubscriptionId
                    };
                }
            }

            var billingPortalSessionService = new SessionService();
            var billingPortalSession =
                await billingPortalSessionService.CreateAsync(sessionCreateOptions, stripeRequestOptions);

            workspan
                .Tag("billingPortalSessionId", billingPortalSession.Id)
                .Tag("billingPortalSessionUrl", billingPortalSession.Url)
                .Log.Information("Created billing portal session");

            return billingPortalSession.Url;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }


    private async Task<string> GetOrCreateBillingPortalConfigurationAsync(Guid mid,
        BillingPortalConfiguration billingPortalConfiguration, string stripeAccountId)
    {
        using var workspan = Workspan.Start<StripeBillingPortalService>();

        try
        {
            StripeBillingPortalData stripeBillingPortalData;

            var billingPortalDataString = await _dbContext.StripeBillingPortalData
                .FirstOrDefaultAsync(x =>
                    x.Mid == mid && x.StripeAccountId == stripeAccountId &&
                    x.ConfigurationName == billingPortalConfiguration.ConfigurationName);

            if (billingPortalDataString != null)
            {
                workspan.Log.Information("Stripe billing portal configuration already exists");

                stripeBillingPortalData =
                    JsonSerializer.Deserialize<StripeBillingPortalData>(billingPortalDataString.Data);
            }
            else
            {
                workspan.Log.Information("Creating Stripe billing portal configuration");

                // Distributed lock to ensure only one request is creating the billing portal configuration
                await using var @lock = await _distributedLockService
                    .AcquireLockAsync(LockKeyFactory.CreateStripeBillingPortalDataLockKey(mid),
                        TimeSpan.FromSeconds(30),
                        maxRetryDuration: TimeSpan.FromSeconds(20));

                var stripeRequestOptions = new RequestOptions()
                {
                    StripeAccount = stripeAccountId,
                    ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
                };

                var billingPortalConfigurationCreateOptions = CreateBillingPortalConfigurationCreateOptions();

                var billingPortalConfigurationService = new ConfigurationService();
                var stripeBillingPortalConfiguration = await billingPortalConfigurationService.CreateAsync(
                    billingPortalConfigurationCreateOptions, stripeRequestOptions);

                workspan
                    .Tag("ConfigurationId", stripeBillingPortalConfiguration.Id)
                    .Tag("StripeBillingPortalConfiguration", stripeBillingPortalConfiguration);

                if (!stripeBillingPortalConfiguration.Active)
                    throw new Exception();

                stripeBillingPortalData = new StripeBillingPortalData
                {
                    ConfigurationId = stripeBillingPortalConfiguration.Id
                };

                _dbContext.StripeBillingPortalData.Add(new Entities.StripeBillingPortalData
                {
                    Mid = mid,
                    StripeAccountId = stripeAccountId,
                    ConfigurationName = billingPortalConfiguration.ConfigurationName,
                    Data = JsonSerializer.Serialize(stripeBillingPortalData)
                });

                await _dbContext.SaveChangesAsync();
            }

            workspan
                .Log.Information("Stripe billing portal configuration");


            return stripeBillingPortalData.ConfigurationId!;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }

        ConfigurationCreateOptions CreateBillingPortalConfigurationCreateOptions()
        {
            var billingPortalConfigurationCreateOptions = new ConfigurationCreateOptions()
            {
                Features = new ConfigurationFeaturesOptions()
                {
                    SubscriptionUpdate = new ConfigurationFeaturesSubscriptionUpdateOptions()
                    {
                        Enabled = billingPortalConfiguration.SubscriptionUpdateEnabled,
                        Products = new List<ConfigurationFeaturesSubscriptionUpdateProductOptions>()
                            { },
                        DefaultAllowedUpdates = new List<string>() { },
                        //ProrationBehavior = "none"
                    },
                    InvoiceHistory = new ConfigurationFeaturesInvoiceHistoryOptions()
                    {
                        Enabled = billingPortalConfiguration.InvoiceHistoryEnabled,
                    },
                    CustomerUpdate = new ConfigurationFeaturesCustomerUpdateOptions()
                    {
                        Enabled = billingPortalConfiguration.CustomerUpdateEnabled,
                        AllowedUpdates = new List<string>()
                    },
                    PaymentMethodUpdate = new ConfigurationFeaturesPaymentMethodUpdateOptions()
                    {
                        Enabled = billingPortalConfiguration.PaymentMethodUpdateEnabled
                    },
                    SubscriptionCancel = new ConfigurationFeaturesSubscriptionCancelOptions()
                    {
                        Enabled = billingPortalConfiguration.SubscriptionCancelEnabled
                    },
                },
                Metadata = new Dictionary<string, string>()
                {
                    {"flx_created_at", DateTime.UtcNow.ToString("u")}
                }
            };
            return billingPortalConfigurationCreateOptions;
        }
    }

    private async Task<string?> CreateHostedInvoicePaymentPageAsync(string providerAccountId, string invoiceId)
    {
        using var workspan = Workspan.Start<StripeBillingPortalService>()
            .Baggage("AccountId", providerAccountId)
            .Baggage("InvoiceId", invoiceId);

        Invoice? expandedInvoice = null;
        try
        {
            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = providerAccountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };


            var invoiceService = new InvoiceService();
            var invoiceGetOptions = new InvoiceGetOptions();

            // StripeObjectHelpers.ExpandObjectProperties(invoiceGetOptions,
            //     $"{nameof(PaymentIntent.Invoice)}"
            // );

            var invoice =
                await invoiceService.GetAsync(invoiceId, invoiceGetOptions, stripeRequestOptions);

            workspan
                .Tag("SubscriptionId", invoice.SubscriptionId)
                .Tag("InvoiceId", invoice.Id)
                .Tag("CustomerId", invoice.CustomerId)
                .Tag("ChargeId", invoice.ChargeId)
                .Tag("HostedInvoiceUrl", invoice.HostedInvoiceUrl);

            return invoice.HostedInvoiceUrl;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }
}