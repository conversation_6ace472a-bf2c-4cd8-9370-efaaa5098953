using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Stripe.Models;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Response;
using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Entities;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.DebugHelpers;
using FlexCharge.Payments.DTO.Webhooks;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Authentication;
using FlexCharge.Payments.Services.PaymentServices.Providers.Stripe;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Stripe;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Subscription = Stripe.Subscription;

namespace FlexCharge.Payments.Services.PaymentServices;

public partial class StripePaymentProvider
{
    public override async Task<RequestResult<ChargePaymentResult>> ChargeAsync(ChargePaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("Token", request.Token)
            .Baggage("AccountId", request.ExternalAccountId)
            .LogEnterAndExit();

        var result = new ChargePaymentResult();
        result.CanBeRetried = true; // it's safe to retry charges, so we set this to true by default

        Invoice invoice = null;
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            _requestOptions.StripeAccount = request.ExternalAccountId;

            var invoiceService = new InvoiceService();

            invoice = await GetExpandedInvoiceAsync(request.Token, invoiceService);

            workspan
                .Baggage("InvoiceId", invoice.Id)
                .Baggage("InvoiceStatus", invoice.Status);

            var canConfirmPaymentResult = CanConfirmInvoice(invoice);
            if (canConfirmPaymentResult.CanBeConfirmed == false)
            {
                workspan
                    .Tag("ErrorMessage", canConfirmPaymentResult.ErrorMessage)
                    .Log.Information("Invoice cannot be confirmed");

                result.CanBeRetried = false;

                result.AddError("Invoice cannot be confirmed");

                return RequestResult.Error(result.Errors, result);
            }


            var invoicePayOptions = new InvoicePayOptions
            {
                PaymentMethod = await GetPaymentMethodIdAsync(invoice),
            };
            StripeObjectHelpers.ExpandObjectProperties(invoicePayOptions,
                $"{nameof(Invoice.Charge)}"
            );


            workspan
                .Log.Information("Initiating invoice payment");

            invoice = await invoiceService.PayAsync(
                request.Token, invoicePayOptions, _requestOptions,
                cancellationToken: token);

            var paymentResultStatus = invoice.Status;

            if (invoice.Status != "paid")
            {
                result.AddError(invoice.Status);
                await MarkInvoiceAsAttemptedAsync(invoice.Id);
            }
            else
            {
                result.CanBeRetried = false; // Approved transactions shouldn't be retried
                await MarkInvoiceAsRescuedAsync(invoice.Id);

                // convert paid to 'succeeded' to have a uniform result status for payments using Invoices and Payment Intents 
                paymentResultStatus = "succeeded";
            }

            result.RawResult = JsonConvert.SerializeObject(invoice);
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = paymentResultStatus;
            result.ProviderResponseMessage = invoice.Status;
            result.ProviderTransactionToken = invoice.ChargeId;

            result.CvvCode = GetCvvResult(invoice.Charge?.PaymentMethodDetails?.Card?.Checks?.CvcCheck)
                .GetResultCode();

            result.AvsCode = GetAvsResult(invoice.Charge?.PaymentMethodDetails?.Card?.Checks)
                .GetResultCode();

            var mapped =
                await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode,
                    result.ProviderResponseMessage);
            result.InternalResponseCode = mapped.MappedResponseCode;
            result.InternalResponseMessage = mapped.MappedResponseMessage;
            result.InternalResponseMessage = mapped.MappedDeclineTypeResponse.ToString();

            #region Helper functions

            СvvVerificationResult GetCvvResult(string? checksCvcCheck)
            {
                return checksCvcCheck switch
                {
                    null => СvvVerificationResult.Unknown,
                    "pass" => СvvVerificationResult.Match,
                    "unchecked" => СvvVerificationResult.NotProcessed,
                    "unavailable" => СvvVerificationResult.Unknown,
                    "failed" => СvvVerificationResult.NoMatch,
                    _ => СvvVerificationResult.Unknown
                };
            }

            AvsVerificationResult GetAvsResult(ChargePaymentMethodDetailsCardChecks checks)
            {
                if (checks is null)
                    return AvsVerificationResult.Unknown;

                if (checks.AddressLine1Check == null && checks.AddressPostalCodeCheck == null)
                    return AvsVerificationResult.Unknown;

                if (checks.AddressLine1Check == "pass" && checks.AddressPostalCodeCheck == "pass")
                    return AvsVerificationResult.Match;


                if (checks.AddressLine1Check == "pass")
                    return AvsVerificationResult.MatchAddressOnly;

                if (checks.AddressPostalCodeCheck == "pass")
                    return AvsVerificationResult.MatchZipOnly;

                return AvsVerificationResult.NoMatch;
            }

            #endregion
        }
        catch (StripeException e)
        {
            switch (e.StripeError?.Type)
            {
                case "card_error":
                    workspan.Log.Information("A payment error occurred: {ErrorMessage}", e.Message);

                    if (invoice != null)
                        await MarkInvoiceAsAttemptedAsync(invoice.Id);

                    break;
                case "invalid_request_error":
                    workspan.Log.Error("An invalid request occurred");
                    break;
                default:
                    workspan.Log.Error("Another problem occurred, maybe unrelated to Stripe");
                    break;
            }

            result.RawResult = JsonConvert.SerializeObject(e);
            result.ProviderResponseCode = e.StripeError?.Code ?? e.Message;
            result.ProviderResponseMessage = e.StripeError?.Message ?? e.Message;
            result.ProviderTransactionToken = e.StripeError?.Charge;

            var mapped =
                await StripeResponseMapper.GetMappedResponseAsync(result.ProviderResponseCode,
                    result.ProviderResponseMessage);
            result.InternalResponseCode = mapped.MappedResponseCode;
            result.InternalResponseMessage = mapped.MappedResponseMessage;
            result.InternalResponseMessage = mapped.MappedDeclineTypeResponse.ToString();

            if (e.StripeError is not null)
            {
                return RequestResult.Error(e.StripeError.Code, e.StripeError.Message, result);
            }
            else
            {
                return RequestResult.Error(e, result);
            }
        }
        catch (Exception e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            workspan.RecordFatalException(e, "An error occurred while processing the external charge payment");
            result.AddError(e.Message);

            return RequestResult.Error(e, result);
        }

        if (!result.Success)
            workspan.Log.Fatal("Charge result is not success - should never get there");

        return RequestResult.Success(result);
    }

    record InvoiceConfirmationTest()
    {
        public bool CanBeConfirmed { get; set; }
        public string ErrorMessage { get; set; }
    }

    private static InvoiceConfirmationTest CanConfirmInvoice(Invoice invoice)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("InvoiceId", invoice.Id)
            .Baggage("InvoiceStatus", invoice.Status)
            .LogEnterAndExit();

        #region Check if invoice is in a correct state to be confirmed

        // Statuses: draft, open, paid, uncollectible, or void
        string errorMessage;
        switch (invoice.Status)
        {
            case "open":
            case "uncollectible":
                workspan.Log.Information("Invoice can be confirmed");

                return new InvoiceConfirmationTest
                {
                    CanBeConfirmed = true
                }; // !!!

            case "paid":
                if (invoice.PaidOutOfBand)
                {
                    workspan.Log.Information("Invoice already paid out of band");
                    errorMessage = "Invoice paid out of band";
                }
                else
                {
                    workspan.Log.Fatal("Invoice already paid");
                    errorMessage = "Invoice already paid";
                }

                break;

            case "void":
                workspan.Log.Fatal("Invoice is voided");
                errorMessage = "Invoice is voided";

                break;

            case "draft":
                workspan.Log.Fatal("Invoice is in draft state");
                errorMessage = "Invoice is draft";

                break;

            default:
                workspan.Log
                    .Fatal("Unknown invoice state");

                errorMessage = "Invoice is not open or uncollectible";
                break;
        }

        return new InvoiceConfirmationTest
        {
            ErrorMessage = errorMessage
        };


        // // If the payment attempt fails (for example, due to a decline),
        // // the PaymentIntent’s status returns to requires_payment_method so that the payment can be retried.
        // // see: https://docs.stripe.com/payments/paymentintents/lifecycle#intent-statuses
        // if (paymentIntent.Status != "requires_payment_method" &&
        //     paymentIntent.Status != "requires_confirmation")
        // {
        //     workspan.Log.Information("Payment intent is not in the correct state to be confirmed");
        //
        //     return new InvoiceConfirmationTest
        //     {
        //         ErrorMessage = "Payment intent is not in the correct state to be confirmed"
        //     };
        // }

        #endregion
    }

    public override async Task<RequestResult<CanChargeExternalTokenResult>> CanChargeExternalTokenAsync(
        CanChargeExternalTokenRequest request, CancellationToken token)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("Token", request.Token)
            .Baggage("AccountId", request.ExternalAccountId)
            .LogEnterAndExit();

        Invoice invoice = null;
        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            _requestOptions.StripeAccount = request.ExternalAccountId;

            var invoiceService = new InvoiceService();

            invoice = await GetExpandedInvoiceAsync(request.Token, invoiceService);

            workspan
                .Baggage("InvoiceId", invoice.Id)
                .Baggage("InvoiceStatus", invoice.Status);

            var canConfirmPaymentResult = CanConfirmInvoice(invoice);
            if (canConfirmPaymentResult.CanBeConfirmed == false)
            {
                workspan
                    .Tag("ErrorMessage", canConfirmPaymentResult.ErrorMessage)
                    .Log.Information("Invoice cannot be confirmed");
            }

            return RequestResult.Success(new CanChargeExternalTokenResult
            {
                CanCharge = canConfirmPaymentResult.CanBeConfirmed,
            });
        }
        catch (StripeException e) when (e.StripeError != null)
        {
            return RequestResult.Error<CanChargeExternalTokenResult>(e.StripeError.Code, e.StripeError.Message);
        }
        catch (Exception e)
        {
            return RequestResult.Error<CanChargeExternalTokenResult>(e);
        }
    }


    async Task<string> GetPaymentMethodIdAsync(Invoice invoice)
    {
        using var workspan = Workspan.Current!;

        //var paymentMethodId = paymentIntent.LatestCharge?.PaymentMethod;

        var paymentMethodId = invoice.Subscription.DefaultPaymentMethodId;

        if (!string.IsNullOrWhiteSpace(paymentMethodId))
        {
            workspan.Log
                .Information("Using default payment method from the subscription");
        }
        else
        {
            paymentMethodId = invoice.DefaultPaymentMethodId;
            if (!string.IsNullOrWhiteSpace(paymentMethodId))
            {
                workspan.Log
                    .Information("Using default payment method from the invoice");
            }
            else
            {
                paymentMethodId = invoice.Customer.InvoiceSettings.DefaultPaymentMethodId;
                if (!string.IsNullOrWhiteSpace(paymentMethodId))
                {
                    workspan.Log
                        .Information("Using default payment method from the customer invoice settings");
                }
                else
                {
                    paymentMethodId = invoice.Charge?.PaymentMethod;

                    if (!string.IsNullOrWhiteSpace(paymentMethodId))
                    {
                        workspan.Log
                            .Warning("Using the payment method from the latest charge");
                    }
                    else
                    {
                        workspan.Log
                            .Fatal("No Stripe payment method found");

                        throw new FlexChargeException("No Stripe payment method found");
                    }
                }
            }
        }

        #region Commented

        // var customerId = paymentIntent.CustomerId;
        //
        // var paymentMethodService = new PaymentMethodService();
        //
        // // Check if the customer has a payment method
        // var paymentMethods = await paymentMethodService.ListAsync(new PaymentMethodListOptions
        // {
        //     Customer = customerId,
        //     Type = "card"
        // }, _requestOptions);
        //
        // string paymentMethodId = paymentMethods.First().Id; // Use the first available payment method

        #endregion

        Workspan.Current!
            .Baggage("PaymentMethodId", paymentMethodId);

        return paymentMethodId;
    }

    async Task<Invoice> GetExpandedInvoiceAsync(string invoiceId, InvoiceService invoiceService)
    {
        var invoiceGetOptions = new InvoiceGetOptions();

        StripeObjectHelpers.ExpandObjectProperties(invoiceGetOptions,
            //$"{nameof(Invoice.LatestCharge)}",
            //$"{nameof(Invoice.PaymentMethod)}",
            $"{nameof(Invoice.Subscription)}",
            $"{nameof(Invoice.Subscription)}.{nameof(PaymentIntent.Invoice.Subscription.DefaultPaymentMethod)}",
            $"{nameof(Invoice.Customer)}.{nameof(Invoice.Customer.InvoiceSettings)}",
            $"{nameof(Invoice.Charge)}"
        );

        var invoice = await invoiceService.GetAsync(invoiceId,
            options: invoiceGetOptions, _requestOptions);

        Workspan.Current!
            .Baggage("InvoiceId", invoice.Id)
            .Baggage("InvoiceStatus", invoice.Status)
            .Baggage("DefaultPaymentMethodId", invoice.Subscription.DefaultPaymentMethodId);


        return invoice;
    }

    private async Task MarkInvoiceAsRescuedAsync(string invoiceId)
    {
        await MarkInvoiceAsync(invoiceId,
            new()
            {
                {FlexFactorMetadata.FlexFactorAttemptedAt, DateTime.UtcNow.ToString("u")},
                {FlexFactorMetadata.FlexFactorRescuedAt, DateTime.UtcNow.ToString("u")}
            });
    }

    private async Task MarkInvoiceAsAttemptedAsync(string invoiceId)
    {
        await MarkInvoiceAsync(invoiceId,
            new() {{FlexFactorMetadata.FlexFactorAttemptedAt, DateTime.UtcNow.ToString("u")}});
    }

    private async Task MarkInvoiceAsync(string invoiceId, Dictionary<string, string> metaData)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>();

        try
        {
            var invoiceService = new InvoiceService();

            var updateOptions = new InvoiceUpdateOptions
            {
                Metadata = metaData
            };

            await invoiceService.UpdateAsync(invoiceId, updateOptions, _requestOptions);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "An error occurred while adding metadata to Stripe invoice");
        }
    }

    public override async Task<RequestResult<MarkInvoiceAsPaidOutOfBandResult>> MarkInvoiceAsPaidOutOfBandAsync(
        MarkInvoiceAsPaidOutOfBandRequest request, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<StripePaymentProvider>()
            .Baggage("InvoiceId", request.InvoiceId)
            .Baggage("AccountId", request.ExternalAccountId);

        try
        {
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);
            _requestOptions.StripeAccount = request.ExternalAccountId;

            var invoiceService = new InvoiceService();

            // For Stripe, InvoiceId is actually a PaymentIntentId and we need to get the real InvoiceId
            var invoice = await invoiceService.GetAsync(request.InvoiceId,
                requestOptions: _requestOptions, cancellationToken: token);

            workspan
                .Baggage("InvoiceStatus", invoice.Status)
                .Tag("InitialInvoice", invoice);

            switch (invoice.Status)
            {
                case "open":
                case "uncollectible":
                    break;
                case "paid":
                case "void":
                case "draft":
                    workspan.Log.Fatal("Invoice is in incorrect state to be marked as paid out of band: {Status}",
                        invoice.Status);

                    return RequestResult.Error("Invoice is in incorrect state to be marked as paid out of band",
                        new MarkInvoiceAsPaidOutOfBandResult
                        {
                            CanBeRetried = false
                        });

                default:
                    workspan.Log.Fatal("Unknown invoice status");

                    return RequestResult.Error("Invoice is in incorrect state to be marked as paid out of band",
                        new MarkInvoiceAsPaidOutOfBandResult
                        {
                            CanBeRetried = true
                        });
            }

            workspan.Log.Information("Marking invoice as rescued");

            await MarkInvoiceAsRescuedAsync(invoice.Id);

            workspan.Log.Information("Marking invoice as paid out of band");

            var updatedInvoice = await invoiceService.PayAsync(invoice.Id, new InvoicePayOptions
                {
                    PaidOutOfBand = true,
                },
                requestOptions: _requestOptions, cancellationToken: token);

            workspan
                .Tag("UpdatedInvoice", updatedInvoice)
                .Log.Information("Invoice marked as paid out of band");

            return RequestResult.Success(new MarkInvoiceAsPaidOutOfBandResult());
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "An error occurred while adding metadata to Stripe invoice");

            return RequestResult.Error(e, new MarkInvoiceAsPaidOutOfBandResult
            {
                CanBeRetried = true
            });
        }
    }
}