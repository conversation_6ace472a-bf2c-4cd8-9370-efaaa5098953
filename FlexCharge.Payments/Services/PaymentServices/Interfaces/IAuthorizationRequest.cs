using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands.Common;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Payments.Domain.Payments.PaymentInstruments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using BillingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.BillingAddress;
using ShippingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.ShippingAddress;

namespace FlexCharge.Payments.Services.PaymentServices.Interfaces
{
    public interface IAuthorizationRequest : IMerchantRequest
    {
        public string Token { get; set; }
        public CardType? CardType { get; set; }

        public string ProcessorCode { get; set; }
        public bool? IsRebilling { get; set; }
        public SaleRequestCreditCard CreditCard { get; set; }
        public Guid PaymentInstrumentId { get; set; }
        public string? ScaAuthenticationToken { get; set; }
        public int? DuplicateCheckInSeconds { get; set; }
        public BillingAddress BillingAddress { get; set; }
        public ShippingAddress? ShippingAddress { get; set; }
        public ThreeDsecureDTO ThreeDS { get; set; }

        public PaymentModifiers Modifiers { get; set; }
        public bool UseDynamicDescriptor { get; set; }

        public AccountUpdaterSettings AccountUpdaterSettings { get; set; }
        public NetworkReferenceData? NetworkReferenceData { get; set; }
        public string SenseKey { get; set; }
        public NetworkTokenInfo? NetworkTokenInfo { get; set; }

        public bool IsCit { get; set; }
        public DateTime? AccountLastUpdatedAt { get; set; }
        public FlexAccountUpdaterMessage? AccountUpdaterMessage { get; set; }
        public bool TryUseAccountUpdater { get; set; }
    }
}