using System;
using System.Collections.Generic;
using FlexCharge.Contracts.Vault;
using FlexCharge.Common.SensitiveData.Obfuscation;
using FlexCharge.Contracts;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Payments.BinChecker.CardBrands;
using FlexCharge.Payments.Domain.Payments.PaymentInstruments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class SaleRequest : MerchantBaseRequest, IAuthorizationRequest
    {
        //In case its a tokenized sale either you send the payment
        //instrument ID or you send the token as the credit card number
        public bool IsTokenizedSale { get; set; }

        public string ProcessorCode { get; set; }
        public bool? IsRebilling { get; set; }
        public int? DuplicateCheckInSeconds { get; set; }
        public Guid PaymentInstrumentId { get; set; }

        [Obsolete("Use ThreeDS instead")] public string? ScaAuthenticationToken { get; set; }
        public BillingAddress BillingAddress { get; set; }
        public ShippingAddress ShippingAddress { get; set; }
        public ThreeDsecureDTO ThreeDS { get; set; }
        public PaymentModifiers Modifiers { get; set; }

        public NetworkReferenceData? NetworkReferenceData { get; set; }
        public NetworkTokenInfo NetworkTokenInfo { get; set; }
        public string SenseKey { get; set; }
        public bool IsCit { get; set; }
        public string Token { get; set; }
        public CardType? CardType { get; set; }

        public SaleRequestCreditCard CreditCard { get; set; }
        public AccountUpdaterSettings AccountUpdaterSettings { get; set; }
        public bool Use3DS { get; set; }
        public DateTime? AccountLastUpdatedAt { get; set; }
        public FlexAccountUpdaterMessage? AccountUpdaterMessage { get; set; }
        public bool TryUseAccountUpdater { get; set; }

        /// <summary>
        /// Fields to send to payment providers that support user-defined fields
        /// </summary>
        public Dictionary<string, string>? UserDefinedFields { get; set; }
    }

    public class NetworkReferenceData
    {
        public string TransactionId { get; set; }
        public string Brand { get; set; }
    }


    public class SaleRequestCreditCard
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }

        [SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
        public string Number { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public string VerificationValue { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public int Month { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public int Year { get; set; }

        public string CardNumberMasked { get; set; }
        public string Bin { get; set; }
        public string Last4 { get; set; }
        public CardBrand CardBrand { get; set; }
        public bool ValidLuhn { get; set; }
    }
}