using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class VerifyAmountCommandConsumer :
    IdempotentCommandConsumer<VerifyAmountPaymentCommand, VerifyAmountPaymentCommandResponse>
{
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public VerifyAmountCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IPaymentOrchestrator paymentOrchestrator) : base(serviceScopeFactory)
    {
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<VerifyAmountPaymentCommandResponse> ConsumeCommand(VerifyAmountPaymentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentToken", command.PaymentInstrumentToken)
                .LogEnterAndExit();

            var gateway = await _paymentOrchestrator.GetNextGatewayAsync(command.Mid, new OrchestrationOptions()
            {
                Order = command.GatewayOrder,
                PreviousOrder = command.GatewayOrder,
                BlocklistedProviders = command.BlocklistedProviders,
            }, null, cancellationToken);
            if (gateway is null)
            {
                Workspan.Log.Information($"Gateway not found");

                return new VerifyAmountPaymentCommandResponse
                {
                    Success = false,
                    GatewayFound = false
                };
            }

            var descriptor = new DescriptorDTO();
            if (command.UseDynamicDescriptor)
            {
                descriptor.Name = command.Descriptor.Name;
                descriptor.Phone = command.Descriptor.Phone;
                descriptor.Address = command.Descriptor.Address;
                descriptor.City = command.Descriptor.City;
                descriptor.State = command.Descriptor.State;
                descriptor.Postal = command.Descriptor.Postal;
                descriptor.Country = command.Descriptor.Country;
                descriptor.Mcc = command.Descriptor.Mcc;
                descriptor.Url = command.Descriptor.Url;
                descriptor.MerchantId = command.Descriptor.MerchantId;
            }

            var billingAddress = command.BillingAddress;

            var verifyAmountRequest = new AuthorizationRequest()
            {
                Mid = command.Mid,
                Token = command.PaymentInstrumentToken,
                //Deprecated (use ThreeDS property instead)
                //ScaAuthenticationToken = gateway.Name == "nmi" ? command.ScaAuthenticationToken : null,
                CurrencyCode = command.Currency,
                Amount = command.Amount,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
                UseDynamicDescriptor = command.UseDynamicDescriptor,
                DuplicateCheckInSeconds =
                    command
                        .OverrideDuplicateOrderCheckTimespan, // see: https://docs.qpilot.cloud/docs/2000-nmi-error-code-300-duplicate-transaction-refid__
                BillingAddress = new BillingAddress().InitializeFrom(billingAddress),
                Descriptor = descriptor,
                TryUseAccountUpdater = command.TryUseAccountUpdater
            };

            if (command.ThreeDs != null)
            {
                verifyAmountRequest.ThreeDS = new ThreeDsecureDTO
                {
                    EcommerceIndicator = command.ThreeDs.EcommerceIndicator,
                    AuthenticationValue = command.ThreeDs.AuthenticationValue,
                    DirectoryServerTransactionId = command.ThreeDs.DirectoryServerTransactionId,
                    ThreeDsVersion = command.ThreeDs.ThreeDsVersion,
                    Xid = command.ThreeDs.Xid,
                    AuthenticationValueAlgorithm = command.ThreeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = command.ThreeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = command.ThreeDs.AuthenticationResponseStatus,
                    Enrolled = command.ThreeDs.Enrolled == true ? "Y" : "N",
                };
            }

            var verifyAmountResponse = await _paymentOrchestrator.VerifyAmountAsync(verifyAmountRequest,
                token: CancellationToken.None);

            return new VerifyAmountPaymentCommandResponse
            {
                Success = verifyAmountResponse.Success,
                ResponseCode = verifyAmountResponse.ProviderResponseCode,
                ResponseMessage = verifyAmountResponse.ProviderResponseMessage,
                BinNumber = verifyAmountResponse.BinNumber,
                TransactionId = verifyAmountResponse.TransactionId,
                GatewayFound = verifyAmountResponse.IsGatewayFound,
                Provider = verifyAmountResponse.Provider,
                ProviderResponseCode = verifyAmountResponse.ProviderResponseCode,

                InternalResponseCode = verifyAmountResponse.InternalResponseCode,
                InternalResponseMessage = verifyAmountResponse.InternalResponseMessage,
                InternalResponseGroup = verifyAmountResponse.InternalResponseGroup,

                CvvCode = verifyAmountResponse.CvvCode,
                AvsCode = verifyAmountResponse.AvsCode,
                NetworkTokenUsed = verifyAmountResponse.NetworkTokenUsed
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}