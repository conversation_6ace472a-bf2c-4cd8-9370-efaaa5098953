using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Domain.Payments.PaymentInstruments;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.Extensions.DependencyInjection;
using BillingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.BillingAddress;
using ShippingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.ShippingAddress;

namespace FlexCharge.Payments.Consumers;

public class FullyAuthorizePaymentCommandConsumer : IdempotentCommandConsumer<FullyAuthorizePaymentCommand,
    FullyAuthorizePaymentCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPaymentOrchestrator _paymentOrchestrator;
    private IPaymentInstrumentsService _instruments;

    public FullyAuthorizePaymentCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        PostgreSQLDbContext dbContext,
        IPaymentInstrumentsService instruments,
        IPaymentOrchestrator paymentOrchestrator) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _instruments = instruments;
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<FullyAuthorizePaymentCommandResponse> ConsumeCommand(
        FullyAuthorizePaymentCommand command, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentId", command.PaymentInstrument?.PaymentInstrumentId)
                .LogEnterAndExit();

            var descriptor = new DescriptorDTO();

            if (command.UseDynamicDescriptor)
            {
                descriptor.Name = command.Descriptor.Name;
                descriptor.Phone = command.Descriptor.Phone;
                descriptor.Address = command.Descriptor.Address;
                descriptor.City = command.Descriptor.City;
                descriptor.State = command.Descriptor.State;
                descriptor.Postal = command.Descriptor.Postal;
                descriptor.Country = command.Descriptor.Country;
                descriptor.Mcc = command.Descriptor.Mcc;
                descriptor.Url = command.Descriptor.Url;
                descriptor.MerchantId = command.Descriptor.MerchantId;
            }

            var billingAddress = command.BillingAddress;
            var shippingAddress = command.ShippingAddress;

            if (command.UseBillingAsShipping)
            {
                shippingAddress = new Contracts.Common.ShippingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Phone = billingAddress.Phone,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country
                };
            }

            var authorizationRequest = new AuthorizationRequest()
            {
                Token = command.PaymentInstrument.PaymentInstrumentToken,
                CardType = CardTypeHelper.ConvertFromString(command.PaymentInstrument.Type),
                ProcessorCode = command.ProcessorId,
                Mid = command.Mid,
                CurrencyCode = command.Currency,
                Amount = command.Amount,
                Discount = command.Discount,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
                Modifiers = command.Modifiers == null
                    ? null
                    : PaymentModifiers.FromPaymentModifiersModel(command.Modifiers),
                BillingAddress = new BillingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country,
                    PhoneNumber = billingAddress.Phone,
                    Email = command.PayerEmail
                },
                ShippingAddress = command.UseBillingAsShipping
                    ? new ShippingAddress
                    {
                        Address1 = billingAddress.Address1,
                        Address2 = billingAddress.Address2,
                        City = billingAddress.City,
                        State = billingAddress.State,
                        Zip = billingAddress.Zip,
                        Country = billingAddress.Country,
                        FirstName = billingAddress.FirstName,
                        LastName = billingAddress.LastName,
                        PhoneNumber = billingAddress.Phone
                    }
                    : new ShippingAddress
                    {
                        FirstName = shippingAddress?.FirstName,
                        LastName = shippingAddress?.LastName,
                        Address1 = shippingAddress?.Address1,
                        Address2 = shippingAddress?.Address2,
                        City = shippingAddress?.City,
                        State = shippingAddress?.State,
                        Zip = shippingAddress?.Zip,
                        Country = shippingAddress?.Country,
                        PhoneNumber = shippingAddress?.Phone,
                    },
                UseDynamicDescriptor = command.UseDynamicDescriptor,
                Descriptor = descriptor,
                IsRebilling = command.IsRebilling,
                Device = new DeviceDetailsDTO
                {
                    IpAddress = command.DeviceInformation?.IpAddress,
                    UserAgent = command.DeviceInformation?.UserAgent
                },
                IsCit = command.IsCit,
                TryUseAccountUpdater = command.TryUseAccountUpdater,
                UserDefinedFields = command.UserDefinedFields
            };

            if (!string.IsNullOrEmpty(command.SchemeTransactionId))
            {
                authorizationRequest.NetworkReferenceData = new NetworkReferenceData
                {
                    TransactionId = command.SchemeTransactionId
                };
            }

            if (command.OverrideDuplicateOrderCheckTimespan.HasValue)
            {
                authorizationRequest.DuplicateCheckInSeconds =
                    command.OverrideDuplicateOrderCheckTimespan;
            }

            authorizationRequest.BillingAddress.Email = command.PayerEmail;

            if (command.ThreeDs != null)
            {
                authorizationRequest.ThreeDS = new ThreeDsecureDTO
                {
                    EcommerceIndicator = command.ThreeDs.EcommerceIndicator,
                    AuthenticationValue = command.ThreeDs.AuthenticationValue,
                    DirectoryServerTransactionId = command.ThreeDs.DirectoryServerTransactionId,
                    ThreeDsVersion = command.ThreeDs.ThreeDsVersion,
                    Xid = command.ThreeDs.Xid,
                    AuthenticationValueAlgorithm = command.ThreeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = command.ThreeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = command.ThreeDs.AuthenticationResponseStatus,
                    Enrolled = command.ThreeDs.Enrolled == true ? "Y" : "N",
                };
            }


            var authResponse = await _paymentOrchestrator.AuthorizeAsync(authorizationRequest,
                new OrchestrationOptions
                {
                    IsCascadingPayment = command.PerformCascadingPayment,
                    Order = command.GatewayOrder,
                    PreviousOrder = command.GatewayOrder,
                    IsRebilling = command.IsRebilling,
                    IsCIT = command.IsCit,
                    Currency = command.Currency,
                    Country = command.PaymentInstrument?.Country,
                    PaymentRoutingStrategy = command.PaymentRoutingStrategy,
                    BlocklistedProviders = command.BlocklistedProviders,
                    AlreadyTriedProviders = command.AlreadyTriedProviders?.Select(x =>
                        (SupportedGatewayId: x.SupportedGatewayId, Timestamp: x.Timestamp)).ToList(),
                    RiskTier = command.RiskTier,
                }, token: CancellationToken.None);

            var gatewayNotFound = authResponse.ErrorsWithCodes.Any(x => x.Key == "GatewayNotFound");

            return new FullyAuthorizePaymentCommandResponse
            {
                Success = authResponse.Success,
                ResponseCode = authResponse.ProviderResponseCode,
                ResponseMessage = authResponse.ProviderResponseMessage,
                BinNumber = authResponse.BinNumber,
                TransactionId = authResponse.TransactionId,
                OrderId = authResponse.OrderId,

                GatewayFound = !gatewayNotFound,
                GatewayId = authResponse.ProviderId,
                SupportedGatewayId = authResponse.SupportedGatewayId,
                GatewayOrder = authResponse.GatewayOrder,

                Provider = authResponse.Provider,
                ProviderResponseCode = authResponse.ProviderResponseCode,
                ProviderTransactionToken = authResponse.ProviderTransactionToken,

                InternalResponseCode = authResponse.InternalResponseCode,
                InternalResponseMessage = authResponse.InternalResponseMessage,
                InternalResponseGroup = authResponse.InternalResponseGroup,

                CvvCode = authResponse.CvvCode,
                AvsCode = authResponse.AvsCode,
                Errors = authResponse.Errors,
                ErrorsWithCodes = authResponse.ErrorsWithCodes,
                NextGateway = authResponse?.NextGatewayOrder,

                AccountUpdaterMessage = authResponse.AccountUpdaterMessage
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}