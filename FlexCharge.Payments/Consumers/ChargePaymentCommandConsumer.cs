using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class ChargePaymentCommandConsumer : IdempotentCommandConsumer<ChargePaymentCommand,
    ChargePaymentCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public ChargePaymentCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        PostgreSQLDbContext dbContext,
        IPaymentOrchestrator paymentOrchestrator) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<ChargePaymentCommandResponse> ConsumeCommand(
        ChargePaymentCommand paymentCommand, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", paymentCommand.Mid)
                .Baggage("OrderId", paymentCommand.OrderId)
                .Baggage("PaymentInstrumentExternalToken", paymentCommand.ExternalPaymentInstrumentToken)
                .Baggage("ExternalAccountId", paymentCommand.ExternalAccountId)
                .LogEnterAndExit();

            var stripeNmorSupportedGatewayId = SystemWideSupportedGateways.Stripe_NMOR;
            var supportedGateway =
                await _dbContext.SupportedGateways.SingleAsync(x => x.Id == stripeNmorSupportedGatewayId,
                    cancellationToken);

            var chargeRequest = new ChargePaymentRequest()
            {
                SupportedGateway = supportedGateway,
                ExternalAccountId = paymentCommand.ExternalAccountId,
                Token = paymentCommand.ExternalPaymentInstrumentToken,
                Mid = paymentCommand.Mid,
                CurrencyCode = paymentCommand.Currency,
                Amount = paymentCommand.Amount,
                Discount = paymentCommand.Discount,
                PayerId = paymentCommand.PayerId,
                OrderId = paymentCommand.OrderId,
                IsCit = paymentCommand.IsCit,
                UserDefinedFields = paymentCommand.UserDefinedFields
            };

            var chargeResponse =
                await _paymentOrchestrator.ChargeAsync(chargeRequest, token: CancellationToken.None);

            var gatewayNotFound = chargeResponse.Errors.Any(x => x.Code == "GatewayNotFound");

            var chargeResult = chargeResponse.Result!;

            return new ChargePaymentCommandResponse
            {
                Success = chargeResponse.Succeeded,
                ResponseCode = chargeResult.ProviderResponseCode,
                ResponseMessage = chargeResult.ProviderResponseMessage,
                BinNumber = chargeResult.BinNumber,
                TransactionId = chargeResult.TransactionId,
                OrderId = chargeResult.OrderId,
                GatewayFound = !gatewayNotFound,
                GatewayOrder = chargeResult.GatewayOrder,
                Provider = chargeResult.Provider,
                ProviderResponseCode = chargeResult.ProviderResponseCode,
                ProviderTransactionToken = chargeResult.ProviderTransactionToken,

                InternalResponseCode = chargeResult.InternalResponseCode,
                InternalResponseMessage = chargeResult.InternalResponseMessage,
                InternalResponseGroup = chargeResult.InternalResponseGroup,

                CvvCode = chargeResult.CvvCode,
                AvsCode = chargeResult.AvsCode,
                Errors = chargeResponse.Errors.Select(x => x.ErrorMessage).ToList(),
                ErrorsWithCodes = chargeResult.ErrorsWithCodes,
                NextGateway = chargeResult?.NextGatewayOrder,

                AccountUpdaterMessage = chargeResult.AccountUpdaterMessage,

                CanBeRetried = chargeResult.CanBeRetried,
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}