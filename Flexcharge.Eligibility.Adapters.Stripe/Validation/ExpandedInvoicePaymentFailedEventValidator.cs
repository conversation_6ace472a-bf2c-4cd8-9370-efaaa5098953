using FluentValidation;

namespace FlexCharge.Eligibility.Adapters.Stripe.Validation;

class ExpandedInvoicePaymentFailedEventValidator : InvoicePaymentFailedEventValidator
{
    public ExpandedInvoicePaymentFailedEventValidator()
    {
        RuleFor(invoice => invoice.Subscription)
            .NotNull();

        // RuleFor(invoice => invoice.PaymentIntent)
        //     .NotNull();

        RuleFor(invoice => invoice.Charge)
            .NotNull();

        RuleFor(invoice => invoice.Charge.PaymentMethodDetails)
            .NotNull();

        //see: https://docs.stripe.com/api/payment_methods/object
        RuleFor(invoice => invoice.Charge.PaymentMethodDetails.Type)
            .Equal("card");

        RuleFor(invoice => invoice.Charge.PaymentMethodDetails.Card)
            .NotNull();

        RuleFor(invoice => invoice.Charge.BillingDetails)
            .NotNull();

        RuleFor(invoice => invoice.Charge.BillingDetails.Address)
            .NotNull();

        RuleFor(x => x)
            .Must(x =>
                !string.IsNullOrWhiteSpace(x.Customer?.Name) ||
                !string.IsNullOrWhiteSpace(x.Charge.BillingDetails.Name))
            .WithMessage($"No name information provided");
    }
}