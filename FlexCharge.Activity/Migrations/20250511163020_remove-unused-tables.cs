using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Activity.Migrations
{
    /// <inheritdoc />
    public partial class removeunusedtables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditedCorrelationErrors");

            migrationBuilder.DropTable(
                name: "EmailsSent");

            migrationBuilder.DropTable(
                name: "PendingAudits");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AuditedCorrelationErrors",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CorrelationId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    Mid = table.Column<Guid>(type: "uuid", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Severity = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditedCorrelationErrors", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EmailsSent",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    FirstErrorTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    LastErrorTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailsSent", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PendingAudits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CheckPoint = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CorrelationId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsRollingTime = table.Column<bool>(type: "boolean", nullable: false),
                    LastAuditStartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NextAuditTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextAuditTimeOrLastSeen = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RuleSet = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PendingAudits", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditedCorrelationErrors_CorrelationId_Message",
                table: "AuditedCorrelationErrors",
                columns: new[] { "CorrelationId", "Message" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EmailsSent_SentAt",
                table: "EmailsSent",
                column: "SentAt");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_TenantId_IsRollingTime",
                table: "PendingAudits",
                columns: new[] { "RuleSet", "CorrelationId", "TenantId", "IsRollingTime" },
                unique: true);
        }
    }
}
