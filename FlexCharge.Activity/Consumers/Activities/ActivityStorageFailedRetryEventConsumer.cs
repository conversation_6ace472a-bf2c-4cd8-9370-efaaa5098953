//#define MEASURE_PERFORMANCE

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2.Model;
using Amazon.SQS;
using FlexCharge.Common.DataStreaming;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Activity.Services.AggregatedActivities;
using FlexCharge.Common.MassTransit;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using IActivity = FlexCharge.Contracts.Activities.IActivity;

namespace FlexCharge.Activity.Consumers;

public class ActivityStorageFailedRetryEventConsumerDefinition
    : ConsumerDefinitionBase<ActivityStorageFailedRetryEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

public class ActivityStorageFailedRetryEventConsumer : ConsumerBase<ActivityStorageFailedRetryEvent>
{
    //Don't want to log enter and exit, because it's too much noise in logs
    protected override bool LogEnterAndExit => false;

    private readonly KinesisService _dataStreaming;
    private readonly IAggregatedActivitiesService _aggregatedActivitiesService;


    public ActivityStorageFailedRetryEventConsumer(
        IServiceScopeFactory serviceScopeFactory,
        KinesisService dataStreaming,
        IAggregatedActivitiesService aggregatedActivitiesService) : base(serviceScopeFactory)
    {
        _dataStreaming = dataStreaming;
        _aggregatedActivitiesService = aggregatedActivitiesService;
    }


    protected override async Task ConsumeMessage(ActivityStorageFailedRetryEvent message,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ActivityStorageFailedRetryEventConsumer>()
            .Baggage("ActivityId", message.Activity.Id)
            .Baggage("TenantId", message.Activity.TenantId)
            .Baggage("CorrelationId", message.Activity.CorrelationId);

        #region Commented test code

        // #if DEBUG
        //         workspan.Log.Information($"CALLED: {DateTime.Now} ACTIVITY: {message.Activity.Id}");
        //         workspan.Log.Information($"REDELIVERY COUNT: {Context.GetRedeliveryCount()}");
        //         workspan.Log.Information($"RETRY      ATTEMPT: {Context.GetRetryAttempt()}");
        //         workspan.Log.Information($"           COUNT: {Context.GetRetryCount()}");
        // #endif

        #endregion

        try
        {
            switch (message.Storage)
            {
                case ActivityStorageFailedRetryEvent.StorageType.Kinesis:
                    await SendToKinesisAsync(message.Activity);
                    break;
                case ActivityStorageFailedRetryEvent.StorageType.Database:
                    await StoreToDatabaseAsync(message.Activity);
                    break;
                default:
                    Workspan.RecordError("Unknown storage type: {StorageType}", message.Storage);
                    break;
            }
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e,
                "Failed storing activity. Returning message to retry queue.");

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed recording activity to data stream", e);
        }
    }


    private async Task SendToKinesisAsync(IActivity activityToSave)
    {
        using var workspan = Workspan.Start<ActivityStorageFailedRetryEventConsumer>();

#if MEASURE_PERFORMANCE
        var processingTimeStopwatch = Stopwatch.StartNew();
#endif

        #region Commented test code

// #if DEBUG
//         throw new Exception("Test exception");
// #endif

        #endregion

        await _dataStreaming.PutRecordAsync(activityToSave, "activity-stream", "orders-ms");
        Workspan.Log.Information("Successfully stored activity {ActivityId} to Kinesis on retry", activityToSave.Id);

#if MEASURE_PERFORMANCE
        processingTimeStopwatch.Stop();
        workspan.Log.Information("Kinesis processing time: {KinesisTime}",
            processingTimeStopwatch.ElapsedMilliseconds.ToString());
#endif
    }

    private async Task StoreToDatabaseAsync(IActivity activityToSave)
    {
        using var workspan = Workspan.Start<ActivityStorageFailedRetryEventConsumer>();

#if MEASURE_PERFORMANCE
        var processingTimeStopwatch = Stopwatch.StartNew();
#endif

        await _aggregatedActivitiesService.StoreSingleActivityToDatabaseAsync(activityToSave);
        Workspan.Log.Information("Successfully stored activity to Database on retry");

#if MEASURE_PERFORMANCE
        processingTimeStopwatch.Stop();
        workspan.Log.Information("Kinesis processing time: {KinesisTime}",
            processingTimeStopwatch.ElapsedMilliseconds.ToString());
#endif
    }
}