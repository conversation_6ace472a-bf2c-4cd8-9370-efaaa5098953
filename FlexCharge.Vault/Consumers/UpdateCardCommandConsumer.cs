using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Utils;
using FlexCharge.Vault.Services;
using FlexCharge.Vault.Services.AccountUpdaterServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;


namespace FlexCharge.Vault.Consumers;

#region Retry Configuration

public class UpdateCardCommandConsumerDefinition
    : ConsumerDefinitionBase<UpdateCardCommandConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    const int TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS = 24;

    protected override Action<IRedeliveryConfigurator> RedeliveryConfigurator => rd =>
    {
        var delayedRetryIntervals = CalculateDelayedRetryIntervals();
        rd.Intervals(delayedRetryIntervals);
    };

    private static TimeSpan[] CalculateDelayedRetryIntervals()
    {
        //Each delayed redelivery interval is up to 15 minutes on AWS SQS!!!
        int intervals = (TOTAL_DELAYED_RETRY_INTERVAL_IN_HOURS * 60) / MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES;

        TimeSpan[] delayedRetryIntervals =
            Enumerable.Repeat(TimeSpan.FromMinutes(MAX_AWS_SQS_MESSAGE_DELAY_IN_MINUTES), intervals)
                .ToArray();

        return delayedRetryIntervals;
    }

    protected override Action<IRetryConfigurator> RetryConfigurator => r =>
    {
        //r.Interval(3, TimeSpan.FromSeconds(5));
        // //r.Intervals(new[] { 1, 2, 4, 8, 16, 32 }.Select(t => TimeSpan.FromSeconds(t)).ToArray());

        //see: https://petenicholls.com/backoff-calculator/
        //Use formulae: (i ** 2)*<intervalDelta> + <minInterval> . Example: (i ** 2)*10 + 3 
        r.Exponential(3,
            TimeSpan.FromSeconds(3), // First retry attempt delay
            TimeSpan.FromSeconds(
                60), // Max retry interval ((if the formulae return a greater value, then this value will be used))
            TimeSpan.FromSeconds(10)); // Increment multiplier between retries


        r.Handle<MassTransitRetryException>();
    };

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

#endregion

public class UpdateCardCommandConsumer : CommandConsumer<UpdateCardCommand>
{
    private readonly IRealTimeAccountUpdaterService _accountUpdaterService;

    public UpdateCardCommandConsumer(IRealTimeAccountUpdaterService accountUpdaterService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _accountUpdaterService = accountUpdaterService;
    }


    protected override async Task ConsumeCommand(UpdateCardCommand command, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<UpdateCardCommandConsumer>()
            .LogEnterAndExit();

        try
        {
            await _accountUpdaterService.UpdateAccountAsync(command);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            // This exception forces retries as configured in this consumer definition
            throw new MassTransitRetryException("Failed storing updated vault", e);
        }
    }
}