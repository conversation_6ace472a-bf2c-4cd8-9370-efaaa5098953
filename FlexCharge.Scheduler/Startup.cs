using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Swagger;
using Microsoft.Extensions.Options;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Contracts.Commands;
using FlexCharge.Scheduler.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Partitions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Common;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Contracts.Commands.FinancialAccounts;
using FlexCharge.Contracts.Commands.Orders;
using FlexCharge.Contracts.Commands.Risk;
using FlexCharge.Contracts.Commands.Vault;


namespace FlexCharge.Scheduler
{
    public class Startup
    {
        // private static ISendEndpoint _ordersServiceSendEndpoint;
        // private static ISendEndpoint _paymentsServiceSendEndpoint;
        // private static ISendEndpoint _eligibilityServiceSendEndpoint;
        // private static ISendEndpoint _vaultServiceSendEndpoint;
        // private static ISendEndpoint _eligibilitySftpGatewayServiceSendEndpoint;
        // static ISendEndpoint _InitiateExecuteOffSessionRetryStrategiesCommandEndpoint;


        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddTransient<ISchedulerService, HangFireSchedulerService>();
            services.AddTransient<IInternalIdToSchedulerIdMapper, InternalIdToSchedulerIdMapperService>();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();
            //services.AddMvc().AddNewtonsoftJson();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString = "Host=localhost;Database=fc.scheduler;Username=scheduler-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString,
                    npgsqlOptionsAction: options => options.EnableRetryOnFailure(3));
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.scheduler;Username=scheduler-service-staging;Password=*****");


            services.AddJwt();
            services.AddDependencies();

            services.AddControllers();
            services.AddSwaggerDocs();

            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));
                }
            );

            services.AddMassTransit<Startup>();

            services.AddRedisCache();

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });


            services.AddScheduler(
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';");
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseAutoMigrations<PostgreSQLDbContext>();


            app.UseRouting();
            app.UseAuthorization();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseScheduler();

            ScheduleRecurringJobsAsync(app.ApplicationServices);

            app.UseMassTransit();
        }


        private static Guid SendAuditResultsJobId { get; } = new Guid("AF0FE357-60A2-40F2-A832-CB9B48C83156");
        private static Guid ExecuteScheduledPayoutsJobId { get; } = new Guid("518263C7-C24E-4318-8F6A-3A8BBB6F234D");
        private static Guid PerformDailyRepaymentJobId { get; } = new Guid("84004D94-7BBF-4549-9DA4-B20AF2D5ACF3");
        private static Guid PollAccountUpdaterSftpJobId { get; } = new Guid("5789C45F-A6F7-4DC0-91FD-213C8AC84389");
        private static Guid AccountUpdaterUploadBatchJobId { get; } = new Guid("00CA312B-3AF1-40AB-BBF9-9E550BF939B9");
        private static Guid RunAccountUpdaterJobId { get; } = new Guid("2A90F120-B015-44EA-AB4B-ECD74E0ECB3E");

        private static Guid PerformCheckForExpiredOffersJobId { get; } =
            new Guid("D041DC6C-9007-421E-8E91-3015E4CE2ACA");


        private static Guid GeneratePayoutBatchesJobId { get; } = new("CFC0D757-6985-41CA-99C0-7C63BDB2221B");

        private static Guid ProcessPendingTransactionsJobId { get; } = new("C6F2D722-D96A-4143-BBF7-87C42CAA16FB");

        /// <summary>
        /// Orders 
        /// </summary>
        private static Guid SyncProcessingFIPADJobId { get; } = new("A4C56BAA-B62A-40EE-8EBA-DD8B90CCF27D");

        private static Guid InitiateFinancialAccountVerificationSyncJobId { get; } =
            new("D15D62FA-ED7A-4966-A70A-7439A4744FF4");

        private static Guid ResetCycleMetricsJobId { get; } = new("4D97ADD4-AB18-4D74-B72F-A3C47822CA94");
        private static Guid ResetDailyCycleMetricsJobId { get; } = new("51668bd6-4217-4c6f-810d-3e4d15c5f2b9");


        private static Guid ProcessSftpBatchRequestsJobId { get; } = new("1453EA56-6D06-473C-B2A4-C514B6E38723");
        private static Guid ProcessSftpBatchResultsJobId { get; } = new("5047B674-69C0-4BEB-9B6D-D1E8BC2E3FBF");
        private static Guid SendConsumerNotificationsJobId { get; } = new("26DAFEE4-7701-41BB-B6FD-7333176B07A3");
        private static Guid ActivityTablePartitioningJobId { get; } = new("76FC509A-**************-A8DFAFE164B0");

        private static Guid ExecuteOffSessionRetryStrategiesJobId { get; } =
            new("5DDF3433-0E9F-44C8-8F72-52F98C07A0A0");

        private static Guid TriggerFIPADCalculationJobId { get; } = new("5BC6A598-87EB-4DF9-A2A7-51FF141C9B74");

        private static Guid DownloadPaymentReportsJobId { get; } = new("23b62ba6-6533-43a6-9c94-06d5b395460f");
        private static Guid ProcessingPaymentReportsJobId { get; } = new("2869082e-0e4c-4597-b814-c9728697b7ca");

        private static Guid NotifyUnprocessedQueueItemsJobId { get; } = new("7e061d41-8e99-4e09-87f7-a101d186485f");
        private static Guid RunRiskMonitoringAlertsJobId { get; } = new("7a24427c-01fc-4511-a600-425c4dda1276");
        private static Guid RunRiskAlertsQueriesJobId { get; } = new("2581d97b-ad4c-43c9-9b34-73d1180f4fc5");

        // Orders reporting 
        private static Guid RunOrdersReportsJobId { get; } = new("42439471-89f6-48c3-8806-c43cbd70407f");


        // we need this service scope to be alive always for DI to work in Hangfire job callbacks
        static IServiceScope _serviceScope;

        private async void ScheduleRecurringJobsAsync(IServiceProvider serviceProvider)
        {
            // Suppress CS4014 here
            // Hangfire serializes job lambda and executes when it's time
#pragma warning disable CS4014

            _serviceScope = serviceProvider.CreateScope();

            //using (var serviceScope = serviceProvider.CreateScope())
            {
                //see: https://stackoverflow.com/questions/48590579/cannot-resolve-scoped-service-from-root-provider-net-core-2
                var scheduler = _serviceScope.ServiceProvider.GetRequiredService<ISchedulerService>();

                //CRON Tools:
                //see: https://crontab.pro/#0_12_*_*_*
                //see: https://www.freeformatter.com/cron-expression-generator-quartz.html


                await scheduler.ScheduleRecurringJobAsync(SendAuditResultsJobId,
                    () => SendAuditResultsJob(),
                    "*/5 * * * *");

                // await scheduler.ScheduleRecurringJobAsync(GeneratePayoutBatchesJobId,
                //     () => GeneratePayoutBatches(),
                //     "0 12 * * *");

                await scheduler.ScheduleRecurringJobAsync(ExecuteScheduledPayoutsJobId,
                    () => ExecuteScheduledPayoutsJob(),
                    "0 18 * * *");

                await scheduler.ScheduleRecurringJobAsync(PerformDailyRepaymentJobId,
                    () => InitiateDailyRepaymentStrategies(),
                    "0 12 * * *");

                await scheduler.ScheduleRecurringJobAsync(PerformCheckForExpiredOffersJobId,
                    () => InitiateCheckForExpiredOffers(),
                    "* * * * *"); //Every 1 minute

                #region Account Updater Scheduled Jobs

                await scheduler.ScheduleRecurringJobAsync(PollAccountUpdaterSftpJobId,
                    () => PollAccountUpdaterSftpCommand(),
                    "0 */2 * * *");

                // await scheduler.ScheduleRecurringJobAsync(RunAccountUpdaterJobId,
                //     () => RunAccountUpdaterCommand(),
                //     "0 12 * * *");

                // await scheduler.ScheduleRecurringJobAsync(AccountUpdaterUploadBatchJobId,
                //     () => AccountUpdaterUploadBatchCommand(),
                //     "0 12 * * *");

                #endregion

                await scheduler.ScheduleRecurringJobAsync(ProcessPendingTransactionsJobId,
                    () => InitiateProcessPendingTransactions(),
                    "0 12,18 * * *"); //Every day at 12:00 and 18:00

                await scheduler.ScheduleRecurringJobAsync(InitiateFinancialAccountVerificationSyncJobId,
                    () => InitiateFinancialAccountVerificationSync(),
                    "0 12 * * *"); //Every day at 12:00 UTC


                await scheduler.ScheduleRecurringJobAsync(ResetCycleMetricsJobId,
                    () => ResetCycleMetrics(),
                    "0 0 1 * *"); //At 00:00 on first day-of-month every month UTC

                await scheduler.ScheduleRecurringJobAsync(ResetDailyCycleMetricsJobId,
                    () => ResetDailyCycleMetrics(),
                    "0 0 * * *"); //Every day at 00:00 UTC

                await scheduler.ScheduleRecurringJobAsync(ProcessSftpBatchRequestsJobId,
                    () => InitiateProcessSftpBatchRequests(),
                    "0 13,18 * * *"); //Every day at 13:00 and 18:00

                await scheduler.ScheduleRecurringJobAsync(ProcessSftpBatchResultsJobId,
                    () => InitiateProcessSftpBatchResults(),
                    "0 12 * * *"); //Every day at 12:00

                await scheduler.ScheduleRecurringJobAsync(ActivityTablePartitioningJobId,
                    () => InitiateActivityTablePartitioningJobId(),
                    "0 12 * * *"); //Every day at 12:00 UTC


                if (EnvironmentHelper.IsInProduction == false)
                {
                    await scheduler.ScheduleRecurringJobAsync(SendConsumerNotificationsJobId,
                        () => InitiateSendConsumerNotificationsJob(),
                        "* * * * *"); //Every 1 minute
                }
                else
                {
                    await scheduler.ScheduleRecurringJobAsync(SendConsumerNotificationsJobId,
                        () => InitiateSendConsumerNotificationsJob(),
                        "0 17 * * *");
                }


                await scheduler.ScheduleRecurringJobAsync(ExecuteOffSessionRetryStrategiesJobId,
                    () => InitiateExecuteOffSessionRetryStrategiesJobId(),
                    "0 16 * * *");

                // await scheduler.ScheduleRecurringJobAsync(ExecuteOffSessionRetryStrategiesJobId,
                //     () => InitiateExecuteOffSessionRetryStrategiesJobId(),
                //     "0 00 * * *");

                //collect Fees every day at 18:00
                await scheduler.ScheduleRecurringJobAsync(TriggerFIPADCalculationJobId,
                    () => TriggerFIPADCalculationJob(),
                    "0 18 * * *");

                await scheduler.ScheduleRecurringJobAsync(SyncProcessingFIPADJobId,
                    () => TriggerSyncProcessingFIPADJob(),
                    "0 18 * * *");

                await scheduler.ScheduleRecurringJobAsync(DownloadPaymentReportsJobId,
                    () => DownloadPaymentReportsJob(),
                    "0 18 * * *");

                await scheduler.ScheduleRecurringJobAsync(ProcessingPaymentReportsJobId,
                    () => ProcessingPaymentReportsJob(),
                    "0 19 * * *");

                await scheduler.ScheduleRecurringJobAsync(NotifyUnprocessedQueueItemsJobId,
                    () => NotifyUnprocessedQueueItemsJob(),
                    "0 14,22 * * *"); // Every day at 2pm and 10pm UTC


                await scheduler.ScheduleRecurringJobAsync(RunRiskMonitoringAlertsJobId,
                    () => RunRiskMonitoringAlertsJob(),
                    "0 17 * * *");

                await scheduler.ScheduleRecurringJobAsync(RunRiskAlertsQueriesJobId,
                    () => RunRiskAlertsQueriesJob(),
                    "0 16 * * *");


                await scheduler.ScheduleRecurringJobAsync(RunOrdersReportsJobId,
                    () => RunOrdersReportsJob(),
                    "0 6 * * *");
            }

#pragma warning restore CS4014
        }

        public void EmptyJob()
        {
        }


        static void SendCommand<T>(string microserviceName, Guid jobId)
            where T : class
        {
            Task.Run(async () =>
            {
                using var workspan = Workspan.Start<Startup>()
                    .Baggage("Microservice", microserviceName)
                    .Baggage("JobId", jobId);

                try
                {
                    workspan.Log.Information("Sending command to {Microservice} with JobId {JobId}",
                        microserviceName, jobId);


                    var sendEndpointProvider =
                        _serviceScope.ServiceProvider.GetRequiredService<ISendEndpointProvider>();
                    var sendEndpoint = await sendEndpointProvider.GetServiceSendEndpoint<T>(microserviceName);

                    var commandParameters = new
                    {
                        CallerId = jobId.ToString()
                    };
                    await sendEndpoint.Send<T>(commandParameters);
                }
                catch (Exception e)
                {
                    workspan.RecordException(e, "Cannot send command from scheduled job");
                }
            }).Wait();
        }

        public static async Task SendAuditResultsJob() =>
            SendCommand<SendAuditResultsCommand>(Microservices.Audit, SendAuditResultsJobId);

        public static async Task GeneratePayoutBatches() =>
            SendCommand<ExecutePayoutBatchCalculationsCommand>(Microservices.Orders, GeneratePayoutBatchesJobId);

        public static async Task ExecuteScheduledPayoutsJob() =>
            SendCommand<ExecuteScheduledPayoutsCommand>(Microservices.Orders, ExecuteScheduledPayoutsJobId);

        public static async Task InitiateDailyRepaymentStrategies() =>
            SendCommand<InitiateRepaymentCommand>(Microservices.Orders, PerformDailyRepaymentJobId);

        public static async Task TriggerFIPADCalculationJob() =>
            SendCommand<TriggerFIPADCalculationCommand>(Microservices.Orders, TriggerFIPADCalculationJobId);

        public static async Task TriggerSyncProcessingFIPADJob() =>
            SendCommand<TriggerSyncProcessingFIPADCommand>(Microservices.Orders, SyncProcessingFIPADJobId);

        public static async Task InitiateCheckForExpiredOffers() =>
            SendCommand<InitiateCheckForExpiredOffersCommand>(Microservices.Eligibility,
                PerformCheckForExpiredOffersJobId);

        public static async Task PollAccountUpdaterSftpCommand() =>
            SendCommand<PollAccountUpdaterSftpCommand>(Microservices.Vault, PollAccountUpdaterSftpJobId);

        // public static async Task AccountUpdaterUploadBatchCommand() =>
        //     SendCommand<AccountUpdaterUploadBatchCommand>(Microservice.Vault, AccountUpdaterUploadBatchJobId);

        // public static async Task RunAccountUpdaterCommand() =>
        //     SendCommand<RunAccountUpdaterCommand>(Microservice.Vault, RunAccountUpdaterJobId);

        public static async Task InitiateProcessPendingTransactions() =>
            SendCommand<InitiateProcessPendingTransactionsCommand>(Microservices.Payments,
                ProcessPendingTransactionsJobId);

        public static async Task ResetCycleMetrics() =>
            SendCommand<ResetCycleMetricsCommand>(Microservices.Payments,
                ResetCycleMetricsJobId);

        public static async Task ResetDailyCycleMetrics() =>
            SendCommand<ResetDailyCycleMetricsCommand>(Microservices.Payments,
                ResetCycleMetricsJobId);


        public static async Task InitiateFinancialAccountVerificationSync() =>
            SendCommand<InitiateFinancialAccountsVerificationCommand>(Microservices.Payments,
                InitiateFinancialAccountVerificationSyncJobId);

        public static async Task InitiateProcessSftpBatchRequests() =>
            SendCommand<InitiateProcessSftpBatchRequestsCommand>(Microservices.Eligibility_SftpGateway,
                ProcessSftpBatchRequestsJobId);

        public static async Task InitiateProcessSftpBatchResults() =>
            SendCommand<InitiateProcessSftpBatchResultsCommand>(Microservices.Eligibility_SftpGateway,
                ProcessSftpBatchResultsJobId);

        public void InitiateSendConsumerNotificationsJob() =>
            SendCommand<InitiateSendConsumerNotificationsCommand>(Microservices.Eligibility,
                SendConsumerNotificationsJobId);

        public void InitiateExecuteOffSessionRetryStrategiesJobId() =>
            SendCommand<InitiateExecuteOffSessionRetryStrategiesCommand>(Microservices.Eligibility,
                ExecuteOffSessionRetryStrategiesJobId);

        public void InitiateActivityTablePartitioningJobId() =>
            SendCommand<ActivitiesMaintainTablePartitionCommand>(Microservices.Activity,
                ActivityTablePartitioningJobId);

        public void DownloadPaymentReportsJob() =>
            SendCommand<ReportsToS3FileTransferCommand>(Microservices.Payments,
                DownloadPaymentReportsJobId);

        public void ProcessingPaymentReportsJob() =>
            SendCommand<ProcessingPaymentReportsCommand>(Microservices.Payments,
                ProcessingPaymentReportsJobId);

        public void NotifyUnprocessedQueueItemsJob() =>
            SendCommand<NotifyUnprocessedQueueItemsCommand>(Microservices.Payments,
                NotifyUnprocessedQueueItemsJobId);

        public void RunOrdersReportsJob() =>
            SendCommand<GenerateOrdersReportsCommand>(Microservices.Orders,
                RunOrdersReportsJobId);


        #region RISK RELATED

        public void RunRiskMonitoringAlertsJob() =>
            SendCommand<ExecuteRiskTriggeredAlertsCommand>(Microservices.Risk,
                RunRiskMonitoringAlertsJobId);

        public void RunRiskAlertsQueriesJob() =>
            SendCommand<RunRiskAlertsQueriesCommand>(Microservices.Risk,
                RunRiskAlertsQueriesJobId);

        #endregion
    }
}