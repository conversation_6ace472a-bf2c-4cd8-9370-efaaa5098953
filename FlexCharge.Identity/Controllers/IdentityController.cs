using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Identity.DTO;
using FlexCharge.Identity.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Identity.Activities;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Telemetry;
using FlexCharge.Identity.Messages.Commands;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Identity.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class IdentityController : BaseController
    {
        private readonly IIdentityService _identityService;
        private readonly AppOptions _globalOptions;
        private readonly ILogger<IdentityController> logger;
        private readonly IRecaptchaService recaptchaService;
        private readonly IActivityService _activityService;
        private readonly IUsersService _usersService;
        private readonly PostgreSQLDbContext _dbContext;

        public IdentityController(IIdentityService identityService,
            IOptions<AppOptions> globalOptions, ILogger<IdentityController> logger, IRecaptchaService recaptchaService,
            IActivityService activityService, IUsersService usersService, PostgreSQLDbContext dbContext)
        {
            _identityService = identityService;
            _globalOptions = globalOptions.Value;
            this.logger = logger;
            this.recaptchaService = recaptchaService;
            _activityService = activityService;
            _usersService = usersService;
            _dbContext = dbContext;
        }

        [HttpPost("admin-signup")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(AdminSignUpResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(AdminSignUpResponseDTO), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AdminSignUp(AdminSignUpRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, payload.UserName, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            payload.Email = payload.Email.RemoveWhitespace();

            var response = await _identityService.AdminSignUpAsync(payload, null);

            return ReturnResponse(response);
        }

        [HttpPost]
        [Authorize(MyPolicies.ADMINS_ONLY)]
        [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SignUp(SignUpRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            payload.Email = payload.Email.RemoveWhitespace();

            var response = await _identityService.SignUpAsync(payload);

            return ReturnResponse(response);
        }

        [HttpPost("signin")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SignIn), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SignIn([FromBody] SignIn command)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions)
                .Baggage("Username", command.Email);

            try
            {
                var result = await this.recaptchaService.CheckRecaptchaAsync(command.RecaptchaToken, command.Email);
                if (!result)
                    ModelState.AddModelError("Captcha", "Captcha is not valid");

                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                var response = await _identityService.SignInAsync(command.Email, command.Password);

                if (!response.Success)
                    return ReturnResponse(response);

                if (!string.IsNullOrEmpty(response.RefreshToken))
                    this.SetTokenCookie(response.RefreshToken);

                //Delete once set in cookie //TODO refactoring needed
                response.RefreshToken = "";

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return StatusCode(StatusCodes.Status500InternalServerError, Consts.GeneralError);
            }
        }

        [HttpPost("signin-challenge")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SignIn), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SignInChallenge(SignInChallengeRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            try
            {
                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                var response = await _identityService.SignInChallengeAsync(payload.Email, payload.Session, payload.Code,
                    payload.RememberMFA);

                if (!response.Success)
                    return ReturnResponse(response);

                if (!string.IsNullOrEmpty(response.RefreshToken))
                    SetTokenCookie(response.RefreshToken);

                //Delete once set in cookie //TODO refactoring needed
                response.RefreshToken = "";

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return StatusCode(StatusCodes.Status500InternalServerError, Consts.GeneralError);
            }
        }

        [HttpPost("ChangePassword")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(JsonWebToken), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ChangePassword(ChangePasswordDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions)
                .Baggage("Username", payload.Username);

            try
            {
                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                JsonWebToken response = await _identityService.ChangePasswordAsync(payload.Username,
                    payload.CurrentPassword, payload.NewPassword);

                if (!string.IsNullOrEmpty(response.RefreshToken))
                    SetTokenCookie(response.RefreshToken);

                //Delete once set in cookie //TODO refactoring needed
                response.RefreshToken = "";

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    update => update
                        .Meta(meta => meta
                            .SetValue("Request", JsonConvert.SerializeObject(payload))
                        ));

                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("SetPassword")]
        [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SetPassword(SetPasswordDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions)
                .Baggage("Username", payload.Username);

            try
            {
                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                var user = await _usersService.GetUserByCognitoUsername(payload.Username);
                if (user == null)
                {
                    ModelState.AddModelError("General", "User not found");
                    return ValidationProblem();
                }

                if (
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId != GetPID()) ||
                    (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                     user.PartnerId != GetPID()))
                {
                    await _identityService.AdminChangePasswordAsync(user.UserName, user.Email, payload.Password);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("ForgotPassword")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(JsonWebToken), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            try
            {
                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                await _identityService.ForgotPasswordAsync(payload);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return Ok();
            }
        }

        [HttpPost("ConfirmForgotPassword")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(JsonWebToken), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ConfirmForgotPassword(ConfirmForgotPasswordDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, payload.Username, _globalOptions);

            try
            {
                // var captchaResult = await this.recaptchaService.CheckRecaptchaAsync(payload.RecaptchaToken, payload.Username);
                // if (!captchaResult)
                //     ModelState.AddModelError("Captcha", "Captcha is not valid");

                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                var response = await _identityService.ConfirmForgotPasswordAsync(payload);

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }


        // [HttpPost("verify")]
        // public async Task<IActionResult> Verify(PhoneVerify command)
        // {
        //     this.logger.LogInformation($"ENTERED: {_globalOptions.Name} => Identity => Verify POST");
        //     if (!ModelState.IsValid) return ValidationProblem();
        //
        //     var jwt = await _identityService.VerifyCode(command.Phone, command.Code);
        //
        //     if (!jwt.Success)
        //         return ReturnResponse(jwt);
        //
        //     if (!string.IsNullOrEmpty(jwt.RefreshToken))
        //         this.SetTokenCookie(jwt.RefreshToken);
        //
        //     this.logger.LogInformation($"EXIT: {_globalOptions.Name} => Identity => Verify POST");
        //     return Ok(jwt);
        // }

        // [HttpPost("create-admin")]
        // [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status400BadRequest)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // [NonAction]
        // public async Task<IActionResult> CreateAdmin(CreateAdmin createAdmin)
        // {
        //     if (!this.ModelState.IsValid)
        //     {
        //         return this.BadRequest(this.ModelState);
        //     }
        //
        //     createAdmin.Email = this.RemoveWhitespace(createAdmin.Email);
        //
        //     await _identityService.CreateAdminAsync(createAdmin);
        //
        //     return this.Ok();
        // }
        //
        // [HttpPost("back-office-signin")]
        // [ProducesResponseType(typeof(JsonWebToken), StatusCodes.Status200OK)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // public async Task<IActionResult> BackOfficeSingIn(BackOfficeSingIn command)
        // {
        //     try
        //     {
        //         this.logger.LogInformation($"ENTERED: {_globalOptions.Name} => Identity => SignIn POST");
        //         if (!ModelState.IsValid)
        //         {
        //             return ValidationProblem();
        //         }
        //
        //         JsonWebToken response = await _identityService.SignInAsync(command.Email, command.Password);
        //         this.logger.LogInformation($"EXIT: {_globalOptions.Name} => Identity => SignIn POST");
        //         return ReturnResponse(response);
        //     }
        //     catch (Exception)
        //     {
        //         return StatusCode(500, new {error = Consts.GeneralError});
        //     }
        // }

        [HttpPost("sign-out")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [JwtAuth]
        public async Task<IActionResult> SignOut(SignOut command)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, command.Token, _globalOptions);

            try
            {
                if (!ModelState.IsValid)
                {
                    workspan.RecordEndpointBadRequest(ModelState);
                    return ValidationProblem();
                }

                //string idToken = Request.Headers[HeaderNames.Authorization].ToString().Replace("Bearer ", "");

                await _identityService.SignOutAsync(GetUID(), command.Token);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("update-mfa")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(UpdateMFASettingsResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(UpdateMFASettingsResponseDTO), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateMFA(UpdateMFASettingsRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user == null)
                {
                    ModelState.AddModelError("General", "User not found");
                    return ValidationProblem();
                }

                var emailFromJWT = HttpContext.User.Claims.Where(x => x.Type == System.Security.Claims.ClaimTypes.Email)
                    .Select(x => x.Value).Single();

                if (!string.IsNullOrEmpty(emailFromJWT) && emailFromJWT == payload.Email ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId == GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    user.PartnerId == GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) &&
                    user.MerchantId == GetMID() // TODO: check if merchant admin can disable MFA
                   )
                {
                    var response = new UpdateMFASettingsResponseDTO()
                    {
                        Name = $"FlexCharge:Portal:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}"
                    };

                    var userToUpdate = new UpdateLocalUserDTO()
                    {
                        Email = user.Email,
                    };

                    if (payload.TwoFactorEnabled && user.VerificationCode == null ||
                        payload.TwoFactorEnabled && payload.IsUpdate)
                    {
                        var code = await _identityService.AssociateMFATokenAsync(payload.Token);

                        if (code != null)
                        {
                            userToUpdate.VerificationCode = code;
                            response.VerificationCode = code;
                        }
                    }

                    // if (payload.TwoFactorEnabled)
                    // {
                    //     userToUpdate.TwoFactorEnabled = DateTime.UtcNow;
                    // }
                    // else 
                    if (user.VerificationCode != null)
                    {
                        await _identityService.SetUserMFAPreference(payload.Token, payload.Email, false, false);
                        userToUpdate.TwoFactorEnabled = DateTime.MinValue;
                        userToUpdate.AuthenticatorAppEnabled = false;
                    }

                    await _usersService.UpdateLocalUserAsync(userToUpdate);

                    return ReturnResponse(response);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("verify-mfa")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(VerifyMFAResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(VerifyMFAResponseDTO), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> VerifyMFA(VerifyMFARequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user == null)
                {
                    ModelState.AddModelError("General", "User not found");
                    return ValidationProblem();
                }

                var emailFromJWT = HttpContext.User.Claims.Where(x => x.Type == System.Security.Claims.ClaimTypes.Email)
                    .Select(x => x.Value).Single();

                if (!string.IsNullOrEmpty(emailFromJWT) && emailFromJWT == payload.Email ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId == GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    user.PartnerId == GetPID()
                   )
                {
                    var response = await _identityService.VerifyMFATokenAsync(payload.Token, payload.Code);

                    if (response.IsValid)
                    {
                        await _identityService.SetUserMFAPreference(payload.Token, payload.Email, false, true);

                        var userToUpdate = new UpdateLocalUserDTO()
                        {
                            Email = user.Email,
                        };

                        userToUpdate.TwoFactorEnabled = DateTime.UtcNow;
                        userToUpdate.AuthenticatorAppEnabled = true;

                        userToUpdate.RememberMFA = payload.RememberMFA == true;

                        await _usersService.UpdateLocalUserAsync(userToUpdate);
                    }

                    return ReturnResponse(response);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("disable-mfa")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DisableMFA(DisableMFARequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                await _identityService.SetUserMFAPreference(payload.Token, payload.Email, false, false);

                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user == null)
                {
                    ModelState.AddModelError("General", "User not found");
                    return ValidationProblem();
                }

                var emailFromJWT = HttpContext.User.Claims.Where(x => x.Type == System.Security.Claims.ClaimTypes.Email)
                    .Select(x => x.Value).Single();

                if (!string.IsNullOrEmpty(emailFromJWT) && emailFromJWT == payload.Email ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                    user.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) &&
                    user.MerchantId != GetMID() // TODO: check if merchant admin can disable MFA
                   )
                {
                    var userToUpdate = new UpdateLocalUserDTO()
                    {
                        Email = user.Email,
                    };

                    userToUpdate.TwoFactorEnabled = DateTime.MinValue;
                    userToUpdate.AuthenticatorAppEnabled = false;
                    userToUpdate.RememberMFA = false;

                    await _usersService.UpdateLocalUserAsync(userToUpdate);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        // remove two factor verification
        [HttpPost("disable-users-mfa")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DisableUsersMFA(DisableUsersMFARequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                await _identityService.AdminSetUserMFAPreferenceAsync(payload.Username, false, false);

                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user == null)
                {
                    ModelState.AddModelError("General", "User not found");
                    return ValidationProblem();
                }

                if (
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId == GetPID()) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) && user.PartnerId == GetPID())
                {
                    var userToUpdate = new UpdateLocalUserDTO()
                    {
                        Email = user.Email,
                    };

                    userToUpdate.TwoFactorEnabled = DateTime.MinValue;
                    userToUpdate.AuthenticatorAppEnabled = false;

                    await _usersService.UpdateLocalUserAsync(userToUpdate);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("reset-password")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ResetPassword(ResetPasswordRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                await _identityService.ResetUserPasswordAsync(payload.Username);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("mfa-range-preference")] // 30 days or permanent
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateMFARangePreference(UpdateMFARangePreferenceReuestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, null, _globalOptions);

            if (!ModelState.IsValid)
            {
                workspan.RecordEndpointBadRequest(ModelState);
                return ValidationProblem();
            }

            try
            {
                if (payload.RememberMFA != true)
                {
                    await _identityService.SetUserMFAPreference(payload.Token, payload.Email, false, true);
                }

                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user != null)
                {
                    var userToUpdate = new UpdateLocalUserDTO()
                    {
                        Email = user.Email,
                    };

                    userToUpdate.RememberMFA = payload.RememberMFA;
                    userToUpdate.TwoFactorEnabled = DateTime.MinValue;
                    userToUpdate.AuthenticatorAppEnabled = false;

                    await _usersService.UpdateLocalUserAsync(userToUpdate);
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                ModelState.AddModelError("General", Consts.GeneralError);
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpPost("resend-invitation")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ResendInvitation([FromBody] ResendInvitationDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, payload.Email, _globalOptions)
                .LogEnterAndExit();

            try
            {
                var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.Email == payload.Email);

                if (user == null)
                {
                    return NotFound();
                }

                if (
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && user.PartnerId != GetPID() ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) && user.PartnerId != GetPID())
                {
                    await _identityService.AdminResendInvitation(user);
                }
                else if (!HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                await _activityService.CreateActivityAsync(IdentityActivities.Identity_ResendInvitation_Ended,
                    update => update
                        .Meta(meta => meta
                            .SetValue("Email", payload.Email)
                        ));

                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Fatal(e, "Error while resending invitation");

                await _activityService.CreateActivityAsync(IdentityErrorActivities.Identity_ResendInvitation_Error, e,
                    update => update
                        .Meta(meta => meta
                            .SetValue("Email", payload.Email)
                        ));

                return StatusCode(StatusCodes.Status500InternalServerError, Consts.GeneralError);
            }
        }
    }
}