-- Sample Notification Categories
-- These examples show the main categories: orders, payments, settlements/payouts

-- Orders Category
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'orders',
    'Orders',
    'All order-related notifications including creation, updates, cancellations, and fulfillment',
    'shopping-cart',
    '#4CAF50',
    1,
    NULL,
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true,
        "maxFrequency": "immediate"
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Order Sub-categories
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'orders.lifecycle',
    'Order Lifecycle',
    'Order creation, confirmation, updates, and completion notifications',
    'refresh',
    '#4CAF50',
    1,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'orders'),
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'orders.fulfillment',
    'Order Fulfillment',
    'Shipping, delivery, and pickup notifications',
    'truck',
    '#2196F3',
    2,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'orders'),
    '{
        "defaultChannels": ["email", "sms", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Payments Category
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'payments',
    'Payments',
    'Payment processing, failures, refunds, and billing notifications',
    'credit-card',
    '#FF9800',
    2,
    NULL,
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true,
        "criticalNotifications": ["payment.failed", "payment.disputed"]
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Payment Sub-categories
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'payments.processing',
    'Payment Processing',
    'Payment authorization, capture, and completion notifications',
    'check-circle',
    '#4CAF50',
    1,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'payments'),
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'payments.issues',
    'Payment Issues',
    'Failed payments, disputes, and chargebacks',
    'alert-triangle',
    '#F44336',
    2,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'payments'),
    '{
        "defaultChannels": ["email", "sms", "in-app"],
        "allowUserCustomization": false,
        "mandatory": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'payments.refunds',
    'Refunds & Credits',
    'Refund processing and account credits',
    'rotate-ccw',
    '#9C27B0',
    3,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'payments'),
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Settlements/Payouts Category
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'settlements',
    'Settlements & Payouts',
    'Settlement processing, payout notifications, and financial reporting',
    'dollar-sign',
    '#607D8B',
    3,
    NULL,
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true,
        "businessOnly": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Settlement Sub-categories
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'settlements.processing',
    'Settlement Processing',
    'Daily, weekly, and monthly settlement notifications',
    'calendar',
    '#607D8B',
    1,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'settlements'),
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'settlements.payouts',
    'Payouts',
    'Payout initiation, completion, and failure notifications',
    'send',
    '#3F51B5',
    2,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'settlements'),
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'settlements.reports',
    'Financial Reports',
    'Settlement reports, tax documents, and financial summaries',
    'file-text',
    '#795548',
    3,
    (SELECT "Id" FROM "NotificationCategories" WHERE "CategoryKey" = 'settlements'),
    '{
        "defaultChannels": ["email"],
        "allowUserCustomization": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);

-- Additional Categories
INSERT INTO "NotificationCategories" (
    "Id", "CategoryKey", "Name", "Description", "Icon", "Color", "SortOrder", 
    "ParentCategoryId", "Settings", "IsActive",
    "CreatedOn", "ModifiedOn", "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'account',
    'Account & Security',
    'Account updates, security alerts, and profile changes',
    'user',
    '#9E9E9E',
    4,
    NULL,
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": true,
        "securityNotifications": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'system',
    'System Notifications',
    'Maintenance, updates, and system-wide announcements',
    'settings',
    '#607D8B',
    5,
    NULL,
    '{
        "defaultChannels": ["email", "in-app"],
        "allowUserCustomization": false,
        "systemWide": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
),
(
    gen_random_uuid(),
    'marketing',
    'Marketing & Promotions',
    'Promotional offers, newsletters, and marketing communications',
    'megaphone',
    '#E91E63',
    6,
    NULL,
    '{
        "defaultChannels": ["email"],
        "allowUserCustomization": true,
        "optional": true
    }'::jsonb,
    true,
    NOW(),
    NOW(),
    false
);
