using EntityFramework.Exceptions.PostgreSQL;
using FlexCharge.NotificationCenter.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;
using System;
using System.Collections;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;


namespace FlexCharge.NotificationCenter
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseExceptionProcessor();
        }


        protected override void OnModelCreating(ModelBuilder builder)
        {
            // Configure Subscriber entity
            builder.Entity<Subscriber>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.UserId).IsUnique();
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.Property(e => e.UserId).IsRequired().HasMaxLength(100);
            });

            // Configure Notification entity
            builder.Entity<Notification>(entity =>
            {
                entity.HasIndex(e => e.EventType);
                entity.HasIndex(e => e.CreatedOn);
                entity.HasIndex(e => e.ScheduledAt);
                entity.Property(e => e.EventType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Channel).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Status).HasMaxLength(50);
                entity.Property(e => e.TemplateData).HasColumnType("jsonb");
            });

            // Configure SubscriberNotification many-to-many relationship
            builder.Entity<SubscriberNotification>(entity =>
            {
                entity.HasKey(sn => new { sn.SubscriberId, sn.NotificationId });
                
                entity.HasOne(sn => sn.Subscriber)
                    .WithMany(s => s.SubscriberNotifications)
                    .HasForeignKey(sn => sn.SubscriberId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(sn => sn.Notification)
                    .WithMany(n => n.SubscriberNotifications)
                    .HasForeignKey(sn => sn.NotificationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(sn => sn.DeliveredAt);
                entity.HasIndex(sn => sn.ReadAt);
                entity.Property(sn => sn.DeliveryStatus).HasMaxLength(50);
            });

            // Configure SubscriberPreferences relationship
            builder.Entity<SubscriberPreferences>(entity =>
            {
                entity.HasOne(sp => sp.Subscriber)
                    .WithOne(s => s.Preferences)
                    .HasForeignKey<SubscriberPreferences>(sp => sp.SubscriberId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.Property(sp => sp.Channels).HasColumnType("jsonb");
                entity.Property(sp => sp.Language).HasMaxLength(10);
                entity.Property(sp => sp.EmailFrequency).HasMaxLength(50);
                entity.Property(sp => sp.SmsFrequency).HasMaxLength(50);
                entity.Property(sp => sp.TimeZone).HasMaxLength(100);
            });

            // Configure NotificationCategory entity
            builder.Entity<NotificationCategory>(entity =>
            {
                entity.HasIndex(e => e.CategoryKey).IsUnique();
                entity.HasIndex(e => e.ParentCategoryId);
                entity.Property(e => e.CategoryKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Icon).HasMaxLength(255);
                entity.Property(e => e.Color).HasMaxLength(7); // Hex color
                entity.Property(e => e.Settings).HasColumnType("jsonb");
                
                entity.HasOne(e => e.ParentCategory)
                    .WithMany(e => e.SubCategories)
                    .HasForeignKey(e => e.ParentCategoryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure NotificationSubscription entity
            builder.Entity<NotificationSubscription>(entity =>
            {
                entity.HasIndex(e => new { e.SubscriberId, e.CategoryId }).IsUnique();
                entity.HasIndex(e => e.CategoryId);
                entity.HasIndex(e => e.EventType);
                
                entity.HasOne(e => e.Subscriber)
                    .WithMany()
                    .HasForeignKey(e => e.SubscriberId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.Category)
                    .WithMany(c => c.Subscriptions)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.Property(e => e.EventType).HasMaxLength(100);
                entity.Property(e => e.EmailFrequency).HasMaxLength(50);
                entity.Property(e => e.SmsFrequency).HasMaxLength(50);
                entity.Property(e => e.PushFrequency).HasMaxLength(50);
                entity.Property(e => e.InAppFrequency).HasMaxLength(50);
            });

            // Configure NotificationRegistry entity
            builder.Entity<NotificationRegistry>(entity =>
            {
                entity.HasIndex(e => new { e.ServiceName, e.EventType }).IsUnique();
                entity.HasIndex(e => e.ServiceName);
                entity.HasIndex(e => e.CategoryId);
                
                entity.Property(e => e.ServiceName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EventType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EventName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.EventDescription).HasMaxLength(1000);
                entity.Property(e => e.Version).HasMaxLength(20);
                entity.Property(e => e.ServiceVersion).HasMaxLength(50);
                entity.Property(e => e.RegistrationSource).HasMaxLength(255);
                entity.Property(e => e.DocumentationUrl).HasMaxLength(500);
                entity.Property(e => e.ContactEmail).HasMaxLength(255);
                entity.Property(e => e.RequiredMergeFields).HasColumnType("jsonb");
                entity.Property(e => e.OptionalMergeFields).HasColumnType("jsonb");
                entity.Property(e => e.MergeFieldSchema).HasColumnType("jsonb");
                entity.Property(e => e.SampleMergeFields).HasColumnType("jsonb");
                
                entity.HasOne(e => e.Category)
                    .WithMany()
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure ChannelTemplate entity with merge field validation support
            builder.Entity<ChannelTemplate>(entity =>
            {
                entity.Property(ct => ct.EventType).IsRequired().HasMaxLength(100);
                entity.Property(ct => ct.Channel).IsRequired().HasMaxLength(50);
                entity.Property(ct => ct.TemplateKey).IsRequired().HasMaxLength(255);
                entity.Property(ct => ct.TemplateContent).HasColumnType("text");
                entity.Property(ct => ct.SubjectTemplate).HasMaxLength(500);
                entity.Property(ct => ct.RequiredMergeFields).HasColumnType("jsonb");
                entity.Property(ct => ct.OptionalMergeFields).HasColumnType("jsonb");
                entity.Property(ct => ct.ValidationRules).HasColumnType("jsonb");
                entity.Property(ct => ct.DefaultValues).HasColumnType("jsonb");
                entity.Property(ct => ct.Description).HasMaxLength(1000);
                
                entity.HasIndex(ct => new { ct.EventType, ct.Channel, ct.IsActive });
                
                entity.HasOne(ct => ct.Category)
                    .WithMany(c => c.Templates)
                    .HasForeignKey(ct => ct.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }
        
        public DbSet<ChannelTemplate> ChannelTemplates { get; set; }
        public DbSet<SubscriberPreferences> SubscriberPreferences { get; set; }
        public DbSet<Subscriber> Subscribers { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<SubscriberNotification> SubscriberNotifications { get; set; }
        public DbSet<NotificationCategory> NotificationCategories { get; set; }
        public DbSet<NotificationSubscription> NotificationSubscriptions { get; set; }
        public DbSet<NotificationRegistry> NotificationRegistries { get; set; }


        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);

            foreach (var entry in ChangeTracker.Entries().Where(x => x.Entity is IEntity && x.State != EntityState.Unchanged))
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        {
                            if (user != null)
                            {
                                if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                    entry.Property("CreatedBy").CurrentValue = user.Value;

                                if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                    entry.Property("ModifiedBy").CurrentValue = user.Value;
                            }

                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                                entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                                entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                            entry.CurrentValues["IsDeleted"] = false;

                            break;
                        }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;

                        CascadeSoftDelete(entry);

                        break;
                }
            }
        }

        /// Cascades Soft Delete to Related Entities
        private void CascadeSoftDelete(EntityEntry entry)
        {
            var dependentToPrincipalNavigations =
                entry.Navigations.Where(x => !((IReadOnlyNavigation)x.Metadata).IsOnDependent).ToList();

            foreach (var navigationEntry in dependentToPrincipalNavigations)
            {
                //see: https://github.com/dotnet/efcore/issues/23283
                var foreignKey = ((IReadOnlyNavigation)navigationEntry.Metadata).ForeignKey;
                if (typeof(IEntity).IsAssignableFrom(foreignKey.DeclaringEntityType.ClrType))
                {
                    if (foreignKey.DeleteBehavior == DeleteBehavior.Cascade)
                    {
                        if (!navigationEntry.IsLoaded)
                        {
                            navigationEntry.Load();
                        }

                        if (navigationEntry.CurrentValue == null) continue;

                        var dependentEntries = navigationEntry.CurrentValue is IEnumerable collection
                            ? collection.Cast<object>().ToArray()
                            : new[] { navigationEntry.CurrentValue };

                        foreach (var dependentEntry in dependentEntries)
                        {
                            Entry(dependentEntry).State = EntityState.Deleted;
                        }
                    }
                }
            }
        }
    }
}
