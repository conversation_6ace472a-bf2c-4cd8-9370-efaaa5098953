using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Sms;
using FlexCharge.Common.Telemetry;
using FlexCharge.NotificationCenter.Services.Models;
using SendGrid.Helpers.Mail;

namespace FlexCharge.NotificationCenter.Services;

public class NotificationDispatcher : INotificationDispatcher
{
    private readonly ISmsServices _smsServices;
    private readonly IEmailSender _emailSender;

    public NotificationDispatcher(ISmsServices smsServices, IEmailSender emailSender)
    {
        _smsServices = smsServices;
        _emailSender = emailSender;
    }

    public async Task SendAsync(string channel, NotificationTarget target, IChannelPayload payload)
    {
        using var workspan = Workspan.Start<NotificationDispatcher>()
            .Baggage("Channel", channel)
            .Baggage("TargetEmail", target.Email)
            .Baggage("TargetUserId", target.UserId);

        workspan.Log.Information("Dispatching notification: Channel={Channel}, Target={Target}", 
            channel, target.Email ?? target.UserId);

        try
        {
            switch (channel.ToLower())
            {
                case "sms":
                    await SendSmsAsync(target, payload as SmsChannelPayload ?? new SmsChannelPayload { Message = payload.Message });
                    break;
                case "email":
                    await SendEmailAsync(target, payload as EmailChannelPayload ?? new EmailChannelPayload { Title = payload.Title, Message = payload.Message });
                    break;
                case "push":
                    await SendPushAsync(target, payload as PushChannelPayload ?? new PushChannelPayload { Title = payload.Title, Message = payload.Message });
                    break;
                case "in-app":
                    await SendInAppAsync(target, payload as InAppChannelPayload ?? new InAppChannelPayload { Title = payload.Title, Message = payload.Message });
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(channel), $"Invalid channel: {channel}");
            }

            workspan.Log.Information("Successfully dispatched notification: Channel={Channel}, Target={Target}", 
                channel, target.Email ?? target.UserId);
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Failed to dispatch notification: Channel={Channel}, Target={Target}", 
                channel, target.Email ?? target.UserId);
            throw;
        }
    }

    private async Task SendSmsAsync(NotificationTarget target, SmsChannelPayload payload)
    {
        using var workspan = Workspan.Start<NotificationDispatcher>("SendSms")
            .Baggage("PhoneNumber", target.PhoneNumber);

        if (string.IsNullOrEmpty(target.PhoneNumber))
        {
            workspan.Log.Warning("Cannot send SMS: Phone number is missing for target {Target}", target.UserId);
            return;
        }

        var smsRequest = new SmsRequest
        {
            PhoneNumber = target.PhoneNumber,
            Message = payload.Message,
        };

        workspan.Log.Information("Sending SMS to {PhoneNumber}", target.PhoneNumber);
        await _smsServices.SendSmsAsync(smsRequest);
        workspan.Log.Information("SMS sent successfully to {PhoneNumber}", target.PhoneNumber);
    }

    private async Task SendEmailAsync(NotificationTarget target, EmailChannelPayload payload)
    {
        using var workspan = Workspan.Start<NotificationDispatcher>("SendEmail")
            .Baggage("Email", target.Email)
            .Baggage("Subject", payload.Subject);

        if (string.IsNullOrEmpty(target.Email))
        {
            workspan.Log.Warning("Cannot send email: Email address is missing for target {Target}", target.UserId);
            return;
        }

        try
        {
            var emailRequest = new EmailSendRequest
            {
                Email = target.Email,
                Subject = payload.Subject ?? payload.Title,
                Message = payload.Message,
                TemplateId = payload.TemplateId,
                MergeFields = payload.MergeFields ?? new Dictionary<string, object>(),
                SenderEmail = payload.SenderEmail,
                ReplyTo = payload.ReplyTo,
                BccEmail = payload.BccEmail
            };

            // Add target information to merge fields if not already present
            if (!emailRequest.MergeFields.ContainsKey("recipientEmail"))
                emailRequest.MergeFields["recipientEmail"] = target.Email;
            if (!emailRequest.MergeFields.ContainsKey("recipientName"))
                emailRequest.MergeFields["recipientName"] = $"{target.FirstName} {target.LastName}".Trim();

            workspan.Log.Information("Sending email to {Email} with subject {Subject}", target.Email, emailRequest.Subject);
            
            await _emailSender.SendEmailAsync(
                emailRequest.Email,
                emailRequest.Subject,
                emailRequest.Message,
                emailRequest.MergeFields,
                emailRequest.TemplateId,
                senderEmailOverride: emailRequest.SenderEmail,
                replyTo: emailRequest.ReplyTo,
                bcc: emailRequest.BccEmail);

            workspan.Log.Information("Email sent successfully to {Email}", target.Email);
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Failed to send email to {Email}", target.Email);
            throw;
        }
    }

    private async Task SendPushAsync(NotificationTarget target, PushChannelPayload payload)
    {
        using var workspan = Workspan.Start<NotificationDispatcher>("SendPush")
            .Baggage("UserId", target.UserId);

        workspan.Log.Information("Sending push notification to user {UserId}", target.UserId);
        
        // Implementation for push notifications
        // This would typically integrate with a push notification service like Firebase, APNs, etc.
        // For now, we'll just log the action
        workspan.Log.Information("Push notification would be sent: Title={Title}, Message={Message}, UserId={UserId}", 
            payload.Title, payload.Message, target.UserId);
        
        await Task.CompletedTask;
    }

    private async Task SendInAppAsync(NotificationTarget target, InAppChannelPayload payload)
    {
        using var workspan = Workspan.Start<NotificationDispatcher>("SendInApp")
            .Baggage("UserId", target.UserId);

        workspan.Log.Information("Sending in-app notification to user {UserId}", target.UserId);
        
        // Implementation for in-app notifications
        // This might store the notification in the database for the user to see when they log in
        // For now, we'll just log the action
        workspan.Log.Information("In-app notification would be stored: Title={Title}, Message={Message}, UserId={UserId}", 
            payload.Title, payload.Message, target.UserId);
        
        await Task.CompletedTask;
    }
}
