using System.Collections.Generic;

namespace FlexCharge.NotificationCenter.Services.Models;

public class EmailSendRequest
{
    public string Email { get; set; }
    public string Subject { get; set; }
    public string Message { get; set; }
    public string TemplateId { get; set; }
    public Dictionary<string, object> MergeFields { get; set; } = new Dictionary<string, object>();
    public string SenderEmail { get; set; }
    public string ReplyTo { get; set; }
    public string BccEmail { get; set; }
}