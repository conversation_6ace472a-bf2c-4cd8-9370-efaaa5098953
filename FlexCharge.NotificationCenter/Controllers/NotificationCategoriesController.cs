using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.NotificationCenter.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Controllers;

[ApiController]
[Route("api/notification-center/categories")]
public class NotificationCategoriesController : ControllerBase
{
    private readonly PostgreSQLDbContext _dbContext;

    public NotificationCategoriesController(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<NotificationCategoryDto>>> GetCategories()
    {
        using var workspan = Workspan.Start<NotificationCategoriesController>();
        
        workspan.Log.Information("Fetching notification categories");

        var categories = await _dbContext.NotificationCategories
            .Where(c => c.IsActive)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .Include(c => c.Templates.Where(t => t.IsActive))
            .OrderBy(c => c.SortOrder)
            .ThenBy(c => c.Name)
            .ToListAsync();

        var result = categories.Select(MapToDto).ToList();

        workspan.Log.Information("Retrieved {Count} notification categories", result.Count);
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<NotificationCategoryDto>> GetCategory(Guid id)
    {
        using var workspan = Workspan.Start<NotificationCategoriesController>()
            .Baggage("CategoryId", id.ToString());

        workspan.Log.Information("Fetching notification category {CategoryId}", id);

        var category = await _dbContext.NotificationCategories
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .Include(c => c.Templates.Where(t => t.IsActive))
            .Include(c => c.ParentCategory)
            .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

        if (category == null)
        {
            workspan.Log.Warning("Notification category {CategoryId} not found", id);
            return NotFound();
        }

        var result = MapToDto(category);
        workspan.Log.Information("Retrieved notification category {CategoryId}", id);
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<NotificationCategoryDto>> CreateCategory(CreateNotificationCategoryDto dto)
    {
        using var workspan = Workspan.Start<NotificationCategoriesController>()
            .Baggage("CategoryKey", dto.CategoryKey);

        workspan.Log.Information("Creating notification category {CategoryKey}", dto.CategoryKey);

        // Check if category key already exists
        var existingCategory = await _dbContext.NotificationCategories
            .FirstOrDefaultAsync(c => c.CategoryKey == dto.CategoryKey);

        if (existingCategory != null)
        {
            workspan.Log.Warning("Category key {CategoryKey} already exists", dto.CategoryKey);
            return Conflict($"Category with key '{dto.CategoryKey}' already exists");
        }

        var category = new NotificationCategory
        {
            CategoryKey = dto.CategoryKey,
            Name = dto.Name,
            Description = dto.Description,
            Icon = dto.Icon,
            Color = dto.Color,
            SortOrder = dto.SortOrder ?? await _dbContext.NotificationCategories.CountAsync() + 1,
            ParentCategoryId = dto.ParentCategoryId,
            Settings = dto.Settings,
            IsActive = true
        };

        _dbContext.NotificationCategories.Add(category);
        await _dbContext.SaveChangesAsync();

        var result = MapToDto(category);
        workspan.Log.Information("Created notification category {CategoryId} with key {CategoryKey}", category.Id, dto.CategoryKey);
        
        return CreatedAtAction(nameof(GetCategory), new { id = category.Id }, result);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<NotificationCategoryDto>> UpdateCategory(Guid id, UpdateNotificationCategoryDto dto)
    {
        using var workspan = Workspan.Start<NotificationCategoriesController>()
            .Baggage("CategoryId", id.ToString());

        workspan.Log.Information("Updating notification category {CategoryId}", id);

        var category = await _dbContext.NotificationCategories
            .FirstOrDefaultAsync(c => c.Id == id);

        if (category == null)
        {
            workspan.Log.Warning("Notification category {CategoryId} not found", id);
            return NotFound();
        }

        category.Name = dto.Name ?? category.Name;
        category.Description = dto.Description ?? category.Description;
        category.Icon = dto.Icon ?? category.Icon;
        category.Color = dto.Color ?? category.Color;
        category.SortOrder = dto.SortOrder ?? category.SortOrder;
        category.Settings = dto.Settings ?? category.Settings;
        category.IsActive = dto.IsActive ?? category.IsActive;

        await _dbContext.SaveChangesAsync();

        var result = MapToDto(category);
        workspan.Log.Information("Updated notification category {CategoryId}", id);
        
        return Ok(result);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteCategory(Guid id)
    {
        using var workspan = Workspan.Start<NotificationCategoriesController>()
            .Baggage("CategoryId", id.ToString());

        workspan.Log.Information("Deleting notification category {CategoryId}", id);

        var category = await _dbContext.NotificationCategories
            .FirstOrDefaultAsync(c => c.Id == id);

        if (category == null)
        {
            workspan.Log.Warning("Notification category {CategoryId} not found", id);
            return NotFound();
        }

        // Soft delete
        category.IsActive = false;
        await _dbContext.SaveChangesAsync();

        workspan.Log.Information("Deleted notification category {CategoryId}", id);
        return NoContent();
    }

    private static NotificationCategoryDto MapToDto(NotificationCategory category)
    {
        return new NotificationCategoryDto
        {
            Id = category.Id,
            CategoryKey = category.CategoryKey,
            Name = category.Name,
            Description = category.Description,
            Icon = category.Icon,
            Color = category.Color,
            SortOrder = category.SortOrder,
            ParentCategoryId = category.ParentCategoryId,
            ParentCategoryName = category.ParentCategory?.Name,
            Settings = category.Settings,
            IsActive = category.IsActive,
            SubCategories = category.SubCategories?.Select(MapToDto).ToList() ?? new List<NotificationCategoryDto>(),
            TemplateCount = category.Templates?.Count ?? 0,
            CreatedOn = category.CreatedOn,
            ModifiedOn = category.ModifiedOn
        };
    }
}

// DTOs
public class NotificationCategoryDto
{
    public Guid Id { get; set; }
    public string CategoryKey { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Icon { get; set; }
    public string Color { get; set; }
    public int SortOrder { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public string ParentCategoryName { get; set; }
    public string Settings { get; set; }
    public bool IsActive { get; set; }
    public List<NotificationCategoryDto> SubCategories { get; set; } = new();
    public int TemplateCount { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
}

public class CreateNotificationCategoryDto
{
    public string CategoryKey { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Icon { get; set; }
    public string Color { get; set; }
    public int? SortOrder { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public string Settings { get; set; }
}

public class UpdateNotificationCategoryDto
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string Icon { get; set; }
    public string Color { get; set; }
    public int? SortOrder { get; set; }
    public string Settings { get; set; }
    public bool? IsActive { get; set; }
}
