using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.NotificationCenter.Entities;
using FlexCharge.NotificationCenter.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Controllers;

[ApiController]
[Route("api/notification-center/subscriptions")]
public class NotificationSubscriptionsController : ControllerBase
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly ISubscriberService _subscriberService;

    public NotificationSubscriptionsController(PostgreSQLDbContext dbContext, ISubscriberService subscriberService)
    {
        _dbContext = dbContext;
        _subscriberService = subscriberService;
    }

    [HttpGet("user/{userId}")]
    public async Task<ActionResult<IEnumerable<NotificationSubscriptionDto>>> GetUserSubscriptions(string userId)
    {
        using var workspan = Workspan.Start<NotificationSubscriptionsController>()
            .Baggage("UserId", userId);

        workspan.Log.Information("Fetching notification subscriptions for user {UserId}", userId);

        var subscriber = await _subscriberService.GetSubscriberAsync(userId);
        if (subscriber == null)
        {
            workspan.Log.Warning("Subscriber not found for user {UserId}", userId);
            return NotFound("Subscriber not found");
        }

        var subscriptions = await _dbContext.NotificationSubscriptions
            .Include(s => s.Category)
            .Where(s => s.SubscriberId == subscriber.Id)
            .ToListAsync();

        var result = subscriptions.Select(MapToDto).ToList();

        workspan.Log.Information("Retrieved {Count} subscriptions for user {UserId}", result.Count, userId);
        return Ok(result);
    }

    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<SubscribableCategoryDto>>> GetSubscribableCategories()
    {
        using var workspan = Workspan.Start<NotificationSubscriptionsController>();

        workspan.Log.Information("Fetching subscribable notification categories");

        var categories = await _dbContext.NotificationCategories
            .Where(c => c.IsActive)
            .Include(c => c.SubCategories.Where(sc => sc.IsActive))
            .OrderBy(c => c.SortOrder)
            .ThenBy(c => c.Name)
            .ToListAsync();

        var result = categories.Select(c => new SubscribableCategoryDto
        {
            Id = c.Id,
            CategoryKey = c.CategoryKey,
            Name = c.Name,
            Description = c.Description,
            Icon = c.Icon,
            Color = c.Color,
            SortOrder = c.SortOrder,
            ParentCategoryId = c.ParentCategoryId,
            SubCategories = c.SubCategories?.Select(sc => new SubscribableCategoryDto
            {
                Id = sc.Id,
                CategoryKey = sc.CategoryKey,
                Name = sc.Name,
                Description = sc.Description,
                Icon = sc.Icon,
                Color = sc.Color,
                SortOrder = sc.SortOrder,
                ParentCategoryId = sc.ParentCategoryId
            }).ToList() ?? new List<SubscribableCategoryDto>()
        }).ToList();

        workspan.Log.Information("Retrieved {Count} subscribable categories", result.Count);
        return Ok(result);
    }

    [HttpPost("user/{userId}/subscribe")]
    public async Task<ActionResult<NotificationSubscriptionDto>> Subscribe(string userId, SubscribeRequestDto request)
    {
        using var workspan = Workspan.Start<NotificationSubscriptionsController>()
            .Baggage("UserId", userId)
            .Baggage("CategoryId", request.CategoryId.ToString());

        workspan.Log.Information("Creating subscription for user {UserId} to category {CategoryId}", userId, request.CategoryId);

        var subscriber = await _subscriberService.GetSubscriberAsync(userId);
        if (subscriber == null)
        {
            workspan.Log.Warning("Subscriber not found for user {UserId}", userId);
            return NotFound("Subscriber not found");
        }

        var category = await _dbContext.NotificationCategories
            .FirstOrDefaultAsync(c => c.Id == request.CategoryId && c.IsActive);

        if (category == null)
        {
            workspan.Log.Warning("Category {CategoryId} not found", request.CategoryId);
            return NotFound("Category not found");
        }

        // Check if subscription already exists
        var existingSubscription = await _dbContext.NotificationSubscriptions
            .FirstOrDefaultAsync(s => s.SubscriberId == subscriber.Id && s.CategoryId == request.CategoryId);

        if (existingSubscription != null)
        {
            // Update existing subscription
            existingSubscription.IsSubscribed = true;
            existingSubscription.EmailEnabled = request.EmailEnabled ?? existingSubscription.EmailEnabled;
            existingSubscription.SmsEnabled = request.SmsEnabled ?? existingSubscription.SmsEnabled;
            existingSubscription.PushEnabled = request.PushEnabled ?? existingSubscription.PushEnabled;
            existingSubscription.InAppEnabled = request.InAppEnabled ?? existingSubscription.InAppEnabled;
            existingSubscription.SubscribedAt = DateTime.UtcNow;
            existingSubscription.UnsubscribedAt = null;

            await _dbContext.SaveChangesAsync();

            var result = MapToDto(existingSubscription);
            result.CategoryName = category.Name;

            workspan.Log.Information("Updated existing subscription for user {UserId} to category {CategoryId}", userId, request.CategoryId);
            return Ok(result);
        }
        else
        {
            // Create new subscription
            var subscription = new NotificationSubscription
            {
                SubscriberId = subscriber.Id,
                CategoryId = request.CategoryId,
                EventType = request.EventType,
                IsSubscribed = true,
                EmailEnabled = request.EmailEnabled ?? true,
                SmsEnabled = request.SmsEnabled ?? false,
                PushEnabled = request.PushEnabled ?? true,
                InAppEnabled = request.InAppEnabled ?? true,
                EmailFrequency = request.EmailFrequency ?? "Immediate",
                SmsFrequency = request.SmsFrequency ?? "Immediate",
                PushFrequency = request.PushFrequency ?? "Immediate",
                InAppFrequency = request.InAppFrequency ?? "Immediate",
                SubscribedAt = DateTime.UtcNow
            };

            _dbContext.NotificationSubscriptions.Add(subscription);
            await _dbContext.SaveChangesAsync();

            var result = MapToDto(subscription);
            result.CategoryName = category.Name;

            workspan.Log.Information("Created new subscription for user {UserId} to category {CategoryId}", userId, request.CategoryId);
            return CreatedAtAction(nameof(GetUserSubscriptions), new { userId }, result);
        }
    }

    [HttpPost("user/{userId}/unsubscribe")]
    public async Task<ActionResult> Unsubscribe(string userId, UnsubscribeRequestDto request)
    {
        using var workspan = Workspan.Start<NotificationSubscriptionsController>()
            .Baggage("UserId", userId)
            .Baggage("CategoryId", request.CategoryId.ToString());

        workspan.Log.Information("Unsubscribing user {UserId} from category {CategoryId}", userId, request.CategoryId);

        var subscriber = await _subscriberService.GetSubscriberAsync(userId);
        if (subscriber == null)
        {
            workspan.Log.Warning("Subscriber not found for user {UserId}", userId);
            return NotFound("Subscriber not found");
        }

        var subscription = await _dbContext.NotificationSubscriptions
            .FirstOrDefaultAsync(s => s.SubscriberId == subscriber.Id && s.CategoryId == request.CategoryId);

        if (subscription == null)
        {
            workspan.Log.Warning("Subscription not found for user {UserId} and category {CategoryId}", userId, request.CategoryId);
            return NotFound("Subscription not found");
        }

        subscription.IsSubscribed = false;
        subscription.UnsubscribedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        workspan.Log.Information("Unsubscribed user {UserId} from category {CategoryId}", userId, request.CategoryId);
        return NoContent();
    }

    [HttpPut("user/{userId}/subscription/{subscriptionId}")]
    public async Task<ActionResult<NotificationSubscriptionDto>> UpdateSubscription(
        string userId, 
        Guid subscriptionId, 
        UpdateSubscriptionRequestDto request)
    {
        using var workspan = Workspan.Start<NotificationSubscriptionsController>()
            .Baggage("UserId", userId)
            .Baggage("SubscriptionId", subscriptionId.ToString());

        workspan.Log.Information("Updating subscription {SubscriptionId} for user {UserId}", subscriptionId, userId);

        var subscriber = await _subscriberService.GetSubscriberAsync(userId);
        if (subscriber == null)
        {
            workspan.Log.Warning("Subscriber not found for user {UserId}", userId);
            return NotFound("Subscriber not found");
        }

        var subscription = await _dbContext.NotificationSubscriptions
            .Include(s => s.Category)
            .FirstOrDefaultAsync(s => s.Id == subscriptionId && s.SubscriberId == subscriber.Id);

        if (subscription == null)
        {
            workspan.Log.Warning("Subscription {SubscriptionId} not found for user {UserId}", subscriptionId, userId);
            return NotFound("Subscription not found");
        }

        // Update subscription settings
        subscription.EmailEnabled = request.EmailEnabled ?? subscription.EmailEnabled;
        subscription.SmsEnabled = request.SmsEnabled ?? subscription.SmsEnabled;
        subscription.PushEnabled = request.PushEnabled ?? subscription.PushEnabled;
        subscription.InAppEnabled = request.InAppEnabled ?? subscription.InAppEnabled;
        subscription.EmailFrequency = request.EmailFrequency ?? subscription.EmailFrequency;
        subscription.SmsFrequency = request.SmsFrequency ?? subscription.SmsFrequency;
        subscription.PushFrequency = request.PushFrequency ?? subscription.PushFrequency;
        subscription.InAppFrequency = request.InAppFrequency ?? subscription.InAppFrequency;
        subscription.QuietHoursStart = request.QuietHoursStart ?? subscription.QuietHoursStart;
        subscription.QuietHoursEnd = request.QuietHoursEnd ?? subscription.QuietHoursEnd;

        await _dbContext.SaveChangesAsync();

        var result = MapToDto(subscription);
        workspan.Log.Information("Updated subscription {SubscriptionId} for user {UserId}", subscriptionId, userId);
        
        return Ok(result);
    }

    private static NotificationSubscriptionDto MapToDto(NotificationSubscription subscription)
    {
        return new NotificationSubscriptionDto
        {
            Id = subscription.Id,
            CategoryId = subscription.CategoryId,
            CategoryName = subscription.Category?.Name,
            EventType = subscription.EventType,
            IsSubscribed = subscription.IsSubscribed,
            EmailEnabled = subscription.EmailEnabled,
            SmsEnabled = subscription.SmsEnabled,
            PushEnabled = subscription.PushEnabled,
            InAppEnabled = subscription.InAppEnabled,
            EmailFrequency = subscription.EmailFrequency,
            SmsFrequency = subscription.SmsFrequency,
            PushFrequency = subscription.PushFrequency,
            InAppFrequency = subscription.InAppFrequency,
            QuietHoursStart = subscription.QuietHoursStart,
            QuietHoursEnd = subscription.QuietHoursEnd,
            SubscribedAt = subscription.SubscribedAt,
            UnsubscribedAt = subscription.UnsubscribedAt
        };
    }
}

// DTOs
public class NotificationSubscriptionDto
{
    public Guid Id { get; set; }
    public Guid CategoryId { get; set; }
    public string CategoryName { get; set; }
    public string EventType { get; set; }
    public bool IsSubscribed { get; set; }
    public bool EmailEnabled { get; set; }
    public bool SmsEnabled { get; set; }
    public bool PushEnabled { get; set; }
    public bool InAppEnabled { get; set; }
    public string EmailFrequency { get; set; }
    public string SmsFrequency { get; set; }
    public string PushFrequency { get; set; }
    public string InAppFrequency { get; set; }
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    public DateTime? SubscribedAt { get; set; }
    public DateTime? UnsubscribedAt { get; set; }
}

public class SubscribableCategoryDto
{
    public Guid Id { get; set; }
    public string CategoryKey { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Icon { get; set; }
    public string Color { get; set; }
    public int SortOrder { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public List<SubscribableCategoryDto> SubCategories { get; set; } = new();
}

public class SubscribeRequestDto
{
    public Guid CategoryId { get; set; }
    public string EventType { get; set; }
    public bool? EmailEnabled { get; set; }
    public bool? SmsEnabled { get; set; }
    public bool? PushEnabled { get; set; }
    public bool? InAppEnabled { get; set; }
    public string EmailFrequency { get; set; }
    public string SmsFrequency { get; set; }
    public string PushFrequency { get; set; }
    public string InAppFrequency { get; set; }
}

public class UnsubscribeRequestDto
{
    public Guid CategoryId { get; set; }
}

public class UpdateSubscriptionRequestDto
{
    public bool? EmailEnabled { get; set; }
    public bool? SmsEnabled { get; set; }
    public bool? PushEnabled { get; set; }
    public bool? InAppEnabled { get; set; }
    public string EmailFrequency { get; set; }
    public string SmsFrequency { get; set; }
    public string PushFrequency { get; set; }
    public string InAppFrequency { get; set; }
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
}
