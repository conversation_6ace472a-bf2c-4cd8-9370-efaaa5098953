using FlexCharge.NotificationCenter.Consumers;
using FlexCharge.NotificationCenter.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.NotificationCenter;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNotificationCenter(this IServiceCollection services)
    {
        // Register services
        services.AddScoped<INotificationDispatcher, NotificationDispatcher>();
        services.AddScoped<ITemplateRegistry, TemplateRegistry>();
        services.AddScoped<ISubscriberService, SubscriberService>();
        services.AddScoped<INotificationPermissionsService, NotificationPermissionsService>();
        services.AddScoped<IMergeFieldValidationService, MergeFieldValidationService>();

        return services;
    }

    public static void ConfigureNotificationCenterConsumers(this IBusRegistrationConfigurator configurator)
    {
        // Register consumers
        configurator.AddConsumer<NotificationConsumer>();
        configurator.AddConsumer<NotificationRegistryConsumer>();
    }
}
