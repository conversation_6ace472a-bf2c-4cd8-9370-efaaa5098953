using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.NotificationCenter;
using FlexCharge.Common.Telemetry;
using FlexCharge.NotificationCenter.Services;
using FlexCharge.NotificationCenter.Services.Models;
using MassTransit;

namespace FlexCharge.NotificationCenter.Consumers;

public class NotificationConsumer : IConsumer<INotificationMessage>
{
    private readonly INotificationPermissionsService _permissionsService;
    private readonly ISubscriberService _subscriberService;
    private readonly INotificationDispatcher _dispatcher;
    private readonly ITemplateRegistry _templateRegistry;
    private readonly IMergeFieldValidationService _mergeFieldValidationService;
    

    public NotificationConsumer(
        INotificationPermissionsService permissionsService,
        ISubscriberService subscriberService,
        ITemplateRegistry templateRegistry,
        INotificationDispatcher dispatcher,
        IMergeFieldValidationService mergeFieldValidationService)
    {
        _permissionsService = permissionsService;
        _subscriberService = subscriberService;
        _templateRegistry = templateRegistry;
        _dispatcher = dispatcher;
        _mergeFieldValidationService = mergeFieldValidationService;
    }

    public async Task Consume(ConsumeContext<INotificationMessage> context)
    {
        var message = context.Message;

        using var workspan = Workspan.Start<NotificationConsumer>()
            .Baggage("EventType", message.EventType)
            .Baggage("AudienceCount", message.Audiences?.Count().ToString() ?? "0");

        workspan.Log.Information("Processing notification: EventType={EventType}, Audiences={AudienceCount}", 
            message.EventType, message.Audiences?.Count() ?? 0);

        foreach (var audience in message.Audiences)
        {
            if (!await _permissionsService.IsAllowedAsync(audience, message.EventType))
            {
                workspan.Log.Warning("Notification not allowed for audience {Audience}, EventType={EventType}", 
                    audience, message.EventType);
                continue;
            }

            var preferences = await _subscriberService.GetPreferencesAsync(audience);

            foreach (var channel in preferences.Channels)
            {
                var template = await _templateRegistry.GetTemplateAsync(message.EventType, channel);
                if (template == null)
                {
                    workspan.Log.Warning("No template found for EventType={EventType}, Channel={Channel}", 
                        message.EventType, channel);
                    continue;
                }

                // Convert Dictionary<string, string> to Dictionary<string, object>
                var mergeFields = message.MergeFields?.ToDictionary(
                    kvp => kvp.Key, 
                    kvp => (object)kvp.Value) ?? new Dictionary<string, object>();

                // Validate merge fields against template requirements
                var validationResult = await _mergeFieldValidationService.ValidateAndProcessAsync(
                    template, mergeFields);

                if (!validationResult.IsValid)
                {
                    workspan.Log.Error("Merge field validation failed for EventType={EventType}, Channel={Channel}. " +
                                   "Missing fields: {MissingFields}, Validation errors: {ValidationErrors}",
                        message.EventType, channel,
                        string.Join(", ", validationResult.MissingRequiredFields),
                        string.Join(", ", validationResult.ValidationErrors));
                    continue;
                }

                workspan.Log.Information("Merge field validation passed for EventType={EventType}, Channel={Channel}", 
                    message.EventType, channel);

                // Use processed content from validation result
                var title = validationResult.ProcessedSubject ?? message.Name ?? message.EventType;
                var messageText = validationResult.ProcessedContent ?? message.Description ?? "Notification";

                // Create the appropriate payload based on channel type with merge fields
                IChannelPayload payload = channel.ToLower() switch
                {
                    "email" => new EmailChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        Subject = title,
                        MergeFields = validationResult.ProcessedMergeFields
                    },
                    "sms" => new SmsChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        MergeFields = validationResult.ProcessedMergeFields
                    },
                    "push" => new PushChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        MergeFields = validationResult.ProcessedMergeFields
                    },
                    "in-app" => new InAppChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        MergeFields = validationResult.ProcessedMergeFields
                    },
                    _ => new SmsChannelPayload 
                    { 
                        Title = title,
                        Message = messageText,
                        MergeFields = validationResult.ProcessedMergeFields
                    }
                };

                // Create notification target
                var target = new NotificationTarget
                {
                    Email = audience, // Assuming audience is email for now
                    UserId = audience
                };
                
                try
                {
                    await _dispatcher.SendAsync(channel, target, payload);
                    workspan.Log.Information("Notification sent successfully: EventType={EventType}, Channel={Channel}, Audience={Audience}", 
                        message.EventType, channel, audience);
                }
                catch (System.Exception ex)
                {
                    workspan.RecordException(ex, "Failed to send notification: EventType={EventType}, Channel={Channel}, Audience={Audience}", 
                        message.EventType, channel, audience);
                }
            }
        }
    }
}
