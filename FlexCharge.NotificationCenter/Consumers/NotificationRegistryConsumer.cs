using System;
using System.Threading.Tasks;
using FlexCharge.Common.NotificationCenter;
using FlexCharge.Common.Telemetry;
using FlexCharge.NotificationCenter.Entities;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Consumers;

public class NotificationRegistryConsumer : IConsumer<INotificationRegistrationMessage>
{
    private readonly PostgreSQLDbContext _dbContext;

    public NotificationRegistryConsumer(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Consume(ConsumeContext<INotificationRegistrationMessage> context)
    {
        var message = context.Message;

        using var workspan = Workspan.Start<NotificationRegistryConsumer>()
            .Baggage("ServiceName", message.ServiceName)
            .Baggage("EventType", message.EventType);

        workspan.Log.Information("Processing notification registration: ServiceName={ServiceName}, EventType={EventType}", 
            message.ServiceName, message.EventType);

        try
        {
            // Find or create category
            var category = await FindOrCreateCategoryAsync(message.CategoryKey, message.CategoryName);

            // Find existing registration or create new one
            var existingRegistration = await _dbContext.NotificationRegistries
                .FirstOrDefaultAsync(nr => nr.ServiceName == message.ServiceName && nr.EventType == message.EventType);

            if (existingRegistration != null)
            {
                // Update existing registration
                existingRegistration.EventName = message.EventName;
                existingRegistration.EventDescription = message.EventDescription;
                existingRegistration.CategoryId = category.Id;
                existingRegistration.Version = message.Version ?? "1.0";
                existingRegistration.RequiredMergeFields = message.RequiredMergeFields ?? new();
                existingRegistration.OptionalMergeFields = message.OptionalMergeFields ?? new();
                existingRegistration.MergeFieldSchema = message.MergeFieldSchema;
                existingRegistration.ServiceVersion = message.ServiceVersion;
                existingRegistration.LastRegistered = DateTime.UtcNow;
                existingRegistration.RegistrationSource = context.SourceAddress?.ToString();
                existingRegistration.SampleMergeFields = message.SampleMergeFields;
                existingRegistration.DocumentationUrl = message.DocumentationUrl;
                existingRegistration.ContactEmail = message.ContactEmail;
                existingRegistration.IsActive = message.IsActive ?? true;
                existingRegistration.RequiresSubscription = message.RequiresSubscription ?? true;

                workspan.Log.Information("Updated existing notification registration: ServiceName={ServiceName}, EventType={EventType}", 
                    message.ServiceName, message.EventType);
            }
            else
            {
                // Create new registration
                var registration = new NotificationRegistry
                {
                    ServiceName = message.ServiceName,
                    EventType = message.EventType,
                    EventName = message.EventName,
                    EventDescription = message.EventDescription,
                    CategoryId = category.Id,
                    Version = message.Version ?? "1.0",
                    RequiredMergeFields = message.RequiredMergeFields ?? new(),
                    OptionalMergeFields = message.OptionalMergeFields ?? new(),
                    MergeFieldSchema = message.MergeFieldSchema,
                    ServiceVersion = message.ServiceVersion,
                    LastRegistered = DateTime.UtcNow,
                    RegistrationSource = context.SourceAddress?.ToString(),
                    SampleMergeFields = message.SampleMergeFields,
                    DocumentationUrl = message.DocumentationUrl,
                    ContactEmail = message.ContactEmail,
                    IsActive = message.IsActive ?? true,
                    RequiresSubscription = message.RequiresSubscription ?? true
                };

                _dbContext.NotificationRegistries.Add(registration);

                workspan.Log.Information("Created new notification registration: ServiceName={ServiceName}, EventType={EventType}", 
                    message.ServiceName, message.EventType);
            }

            await _dbContext.SaveChangesAsync();

            workspan.Log.Information("Successfully processed notification registration: ServiceName={ServiceName}, EventType={EventType}", 
                message.ServiceName, message.EventType);
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex, "Failed to process notification registration: ServiceName={ServiceName}, EventType={EventType}", 
                message.ServiceName, message.EventType);
            throw;
        }
    }

    private async Task<NotificationCategory> FindOrCreateCategoryAsync(string categoryKey, string categoryName)
    {
        var category = await _dbContext.NotificationCategories
            .FirstOrDefaultAsync(nc => nc.CategoryKey == categoryKey);

        if (category == null)
        {
            category = new NotificationCategory
            {
                CategoryKey = categoryKey,
                Name = categoryName ?? categoryKey,
                Description = $"Auto-created category for {categoryKey}",
                IsActive = true,
                SortOrder = await _dbContext.NotificationCategories.CountAsync() + 1
            };

            _dbContext.NotificationCategories.Add(category);
            await _dbContext.SaveChangesAsync();
        }

        return category;
    }
}
