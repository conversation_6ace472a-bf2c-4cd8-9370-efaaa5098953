using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(ServiceName), nameof(EventType), IsUnique = true)]
[Index(nameof(ServiceName))]
[Index(nameof(CategoryId))]
public class NotificationRegistry : AuditableEntity
{
    public string ServiceName { get; set; } // "orders-service", "payments-service", etc.
    public string EventType { get; set; } // "orders.created", "payments.failed", etc.
    public string EventName { get; set; } // Human-readable name
    public string EventDescription { get; set; }
    
    public Guid CategoryId { get; set; }
    public NotificationCategory Category { get; set; }
    
    // Event metadata
    public string Version { get; set; } = "1.0";
    public bool IsActive { get; set; } = true;
    public bool RequiresSubscription { get; set; } = true; // Whether users need to subscribe to receive this
    
    // Merge field schema
    [Column(TypeName = "jsonb")]
    public List<string> RequiredMergeFields { get; set; } = new();
    
    [Column(TypeName = "jsonb")]
    public List<string> OptionalMergeFields { get; set; } = new();
    
    [Column(TypeName = "jsonb")]
    public string MergeFieldSchema { get; set; } // JSON schema for merge fields
    
    // Service information
    public string ServiceVersion { get; set; }
    public DateTime LastRegistered { get; set; }
    public string RegistrationSource { get; set; } // IP or service identifier
    
    // Sample data for testing/documentation
    [Column(TypeName = "jsonb")]
    public string SampleMergeFields { get; set; } // Example merge field data
    
    public string DocumentationUrl { get; set; }
    public string ContactEmail { get; set; }
}
