using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(CategoryKey), IsUnique = true)]
[Index(nameof(ParentCategoryId))]
public class NotificationCategory : AuditableEntity
{
    public string CategoryKey { get; set; } // "orders", "payments", "settlements", etc.
    public string Name { get; set; } // "Orders", "Payments", "Settlements/Payouts"
    public string Description { get; set; }
    public string Icon { get; set; } // Icon class or URL
    public string Color { get; set; } // Hex color code
    public int SortOrder { get; set; } = 0;
    public bool IsActive { get; set; } = true;
    
    // Hierarchical structure
    public System.Guid? ParentCategoryId { get; set; }
    public NotificationCategory ParentCategory { get; set; }
    public ICollection<NotificationCategory> SubCategories { get; set; } = new List<NotificationCategory>();
    
    // Configuration
    [Column(TypeName = "jsonb")]
    public string Settings { get; set; } // JSON settings for the category
    
    // Navigation properties
    public ICollection<ChannelTemplate> Templates { get; set; } = new List<ChannelTemplate>();
    public ICollection<NotificationSubscription> Subscriptions { get; set; } = new List<NotificationSubscription>();
}
