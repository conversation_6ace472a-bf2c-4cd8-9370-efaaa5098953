using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.NotificationCenter.Entities;

[Index(nameof(SubscriberId), nameof(CategoryId), IsUnique = true)]
[Index(nameof(CategoryId))]
[Index(nameof(EventType))]
public class NotificationSubscription : AuditableEntity
{
    public Guid SubscriberId { get; set; }
    public Subscriber Subscriber { get; set; }
    
    public Guid CategoryId { get; set; }
    public NotificationCategory Category { get; set; }
    
    public string EventType { get; set; } // Specific event type (optional, null means all events in category)
    
    // Subscription settings
    public bool IsSubscribed { get; set; } = true;
    public bool EmailEnabled { get; set; } = true;
    public bool SmsEnabled { get; set; } = false;
    public bool PushEnabled { get; set; } = true;
    public bool InAppEnabled { get; set; } = true;
    
    // Frequency settings
    public string EmailFrequency { get; set; } = "Immediate"; // Immediate, Daily, Weekly
    public string SmsFrequency { get; set; } = "Immediate";
    public string PushFrequency { get; set; } = "Immediate";
    public string InAppFrequency { get; set; } = "Immediate";
    
    // Quiet hours (inherits from subscriber preferences if null)
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    
    public DateTime? SubscribedAt { get; set; }
    public DateTime? UnsubscribedAt { get; set; }
}
