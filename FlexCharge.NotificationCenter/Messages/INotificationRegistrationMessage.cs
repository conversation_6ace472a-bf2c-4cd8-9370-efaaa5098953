using System.Collections.Generic;

namespace FlexCharge.Common.NotificationCenter;

public interface INotificationRegistrationMessage
{
    string ServiceName { get; set; }
    string EventType { get; set; }
    string EventName { get; set; }
    string EventDescription { get; set; }
    string CategoryKey { get; set; }
    string CategoryName { get; set; }
    string Version { get; set; }
    List<string> RequiredMergeFields { get; set; }
    List<string> OptionalMergeFields { get; set; }
    string MergeFieldSchema { get; set; }
    string ServiceVersion { get; set; }
    string SampleMergeFields { get; set; }
    string DocumentationUrl { get; set; }
    string ContactEmail { get; set; }
    bool? IsActive { get; set; }
    bool? RequiresSubscription { get; set; }
}

public class NotificationRegistrationMessage : INotificationRegistrationMessage
{
    public string ServiceName { get; set; }
    public string EventType { get; set; }
    public string EventName { get; set; }
    public string EventDescription { get; set; }
    public string CategoryKey { get; set; }
    public string CategoryName { get; set; }
    public string Version { get; set; }
    public List<string> RequiredMergeFields { get; set; } = new();
    public List<string> OptionalMergeFields { get; set; } = new();
    public string MergeFieldSchema { get; set; }
    public string ServiceVersion { get; set; }
    public string SampleMergeFields { get; set; }
    public string DocumentationUrl { get; set; }
    public string ContactEmail { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool? RequiresSubscription { get; set; } = true;
}
